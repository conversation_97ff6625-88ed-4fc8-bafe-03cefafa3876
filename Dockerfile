FROM golang:1.23-alpine AS build
WORKDIR /app
RUN apk --no-cache add ca-certificates && apk --no-cache add tzdata
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN GOOS=linux go build -a -o gapp .

FROM debian

RUN apt-get update && apt-get install -y wkhtmltopdf fonts-lohit-deva fonts-noto-cjk fonts-noto-mono fonts-noto-unhinted
COPY --from=build /usr/share/zoneinfo /usr/share/zoneinfo
ENV TZ=Asia/Kolkata
COPY --from=build /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
WORKDIR /root/
COPY --from=build /app/gapp .
COPY --from=build /app/credentials.toml credentials.toml
CMD ["./gapp"]
