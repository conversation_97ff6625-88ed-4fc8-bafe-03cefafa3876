## Setting up go
Refer this (doc)[https://docs.google.com/document/d/1cVKP-HivAaeDdqeoNZomhFtv9WTEmcq6GJq1hTild2U/edit#].

## Project structure
It is as follows:


    | - <module directories>
    | - go.mod
    | - go.sum
    | - main.go
    | - Dockerfile

Only `main.go`, `go.mod` and `go.sum` should be at top level. Other modules should be inside their corresponding directory. `resource` contains config files and other resources.

Some of the common modules are as follows (with their responsibilities):

- `app/`: This module is to initialize the application. If there is no DI to be used, it will initialize the necessary objects and interfaces in the required order. It may contain API routes.
- `config/`: This module initializes configuration and other dependencies (such as database client, API clients, global config etc.)
- `controller/`: It defines all the controller functions. All requests through API endpoints land in controller function.
- `dtype/`: It contains definition of types and common constants
- `helper/`: It contains helper constructs (functions, interfaces, struct).
- `model/`: It contains definition of ORM structure.
- `repo/`: It contains interfaces and implementation of operate on ORM objects. Through these interfaces, database operations take place.
- `service/`: It contains business logics related to different models and use-cases. Please follow: One model one repo one service.
- `util/`: It contains utility method, structs, interfaces.

Above structure is suitable for small apps. However, if the service is planning to grow big, then you can create modules functionality wise (e.g. `payment`, `recon` etc.). Inside these modules, you can create `service`, `repo` and `model` for each. Functionality will be exposed through interfaces provided in corresponding `service` package.

e.g

    |- payment
    |----service
    |------service.go
    |----model
    |------model.go
    |----repo
    |------repo.go


Functionality of payment module can be exposed through interfaces present in `service`.

## Building project
Use `go build` command.

## Formatting
Before every checkin, ensure to run `go fmt`.

## Running the application.
Once build is generated, directly run the generated application as follows:

`./magneto`

### Running unit tests

Unit tests are present in same tree and file is to be suffixed with `_test` and package name should also be suffixed with `_test`.

    |--controller
    |---user.go
    |---user_test.go

Inside `user_test.go`

    package controller_test


Then, you can start writing test.

Please note that the design of testability is very important. Without considering testability, it will become difficult to write unit tests.

At present, functionality involving only database can be handled through mocking database. You can check `user_test.go` for more details. For other cases, you need to define proper abstraction (using interface etc.).
Then, you can mock those abstraction for testing.

#### Run unit tests

    go test -coverprofile=coverage.out ./...

    go test -coverpkg=./... -coverprofile=profile.cov ./...

#### Generate coverage report

    go tool cover -html=profile.cov -o coverage.html

## Docker build
You can use `docker` command to build image.

    docker build -t gotag .

To run this image, you can use

    docker run --rm --network host -it gotag

Above command will run it in docker container. It will use local mysql database.
