package offer

import (
	"time"

	"stashfin.com/stashfin/magneto/pkg/offer/model"
)

type GradientColor struct {
	Colors    []string  `json:"colors"`
	Positions []float64 `json:"positions"`
	Angle     float64   `json:"angle"`
}

type Voucher struct {
	UID                string        `json:"uid"`
	Thumbnail          string        `json:"thumbnail"`
	Icon               string        `json:"icon"`
	Name               string        `json:"name"`
	DiscountPercentage float32       `json:"discount_percentage"`
	BgGradient         GradientColor `json:"bg_gradient"`
}

type OfferLisitingResponse struct {
	TopBanner    []BannerItem `json:"top_banner"`
	Vouchers     []Voucher    `json:"vouchers"`
	BottomBanner *BannerItem  `json:"bottom_banner,omitempty"`
	FooterImage  string       `json:"footer_image"`
}

type BannerItem struct {
	Name            string `json:"name"`
	ImageURL        string `json:"image_url"`
	RedirectURL     string `json:"redirect_url"`
	IsDisableForLOC bool   `json:"is_disable_for_loc"`
}

type SKU struct {
	Amount   float32 `json:"amount"`
	Validity string  `json:"validity,omitempty"`
	UID      string  `json:"uid"`
}

type OfferDetailResponse struct {
	Title              string   `json:"title"`
	Icon               string   `json:"icon"`
	OfferText          string   `json:"offer_text"`
	SKU                []SKU    `json:"sku"`
	KeyPoints          []string `json:"key_points,omitempty"`
	RedeemSteps        []string `json:"redeem_steps,omitempty"`
	TermsAndConditions string   `json:"tnc"`
	PromotionText      string   `json:"promotion_text,omitempty"`
}

type CartItem struct {
	Title              string  `json:"title"`
	UID                string  `json:"uid"`
	DiscountPercentage int     `json:"dicount_percentage"`
	Icon               string  `json:"icon"`
	OriginalPrice      float32 `json:"original_price"`
	DiscountedPrice    float32 `json:"discounted_price"`
	Quantity           int     `json:"quantity"`
}

type CartTotal struct {
	Items            int     `json:"items"`
	OriginalAmount   float32 `json:"original_amount"`
	DiscountedAmount float32 `json:"discounted_amount"`
	FinalAmount      float32 `json:"final_amount"`
}

type CartDiscount struct {
	IsDiscountCTAAvailable bool      `json:"is_discount_cta_available"`
	LOCDiscoutPercent      float32   `json:"loc_discount_percent"`
	LOCDiscountCap         float32   `json:"loc_discount_cap"`
	Total                  CartTotal `json:"total"`
}

type GetCartResponse struct {
	Items    []CartItem   `json:"items"`
	Discount CartDiscount `json:"discount"`
}

type OrderItem struct {
	Title         string   `json:"title"`
	Icon          string   `json:"icon"`
	TransactionID string   `json:"transaction_id"`
	Status        int      `json:"status"`
	Validity      *string  `json:"validity"`
	AmountPaid    string   `json:"amount_paid"`
	CouponCode    *string  `json:"coupon_code"`
	CouponPin     *string  `json:"coupon_pin"`
	RedeemSteps   []string `json:"redeem_steps"`
	TNC           string   `json:"tnc"`
	ItemUID       string   `json:"item_uid"`
}

type GetOrdersResponse struct {
	Items []OrderItem `json:"items"`
}

type GetPaymentLinkResponse struct {
	RedirectUrl string `json:"redirect_url"`
	Action      string `json:"action"`
}

type OrderStatusData struct {
	Title         string `json:"title"`
	Icon          string `json:"icon"`
	Validity      string `json:"validity"`
	Type          string `json:"type"`
	AmountPaid    string `json:"amount_paid"`
	Name          string `json:"name"`
	TransactionID string `json:"transaction_id"`
	CouponCode    string `json:"coupon_code"`
	CouponPin     string `json:"coupon_pin"`
}

type OrderStatusResponse struct {
	Status model.PaymentStatus `json:"status"`
	Data   []OrderStatusData   `json:"data"`
}

type GetCouponResponse struct {
	CouponCode     string
	CouponPin      string
	CouponValidity *time.Time
}
type GetPaymentOrderResponse struct {
	Uid    string `json:"uid"`
	Action string `json:"action"`
}
