package app

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"

	"github.com/dgrijalva/jwt-go"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/auth"
	"stashfin.com/stashfin/magneto/util"
)

func getRequestId(r *http.Request) string {
	requestId := r.Header.Get("X-Request-Id")
	if requestId == "" {
		requestId = r.Header.Get("Request-Id")
	}

	if requestId == "" {
		requestId = r.Header.Get("request_id")
	}

	if requestId == "" {
		// generate new uuid for request
		requestId = uuid.NewString()
	}

	return requestId
}

func getRemoteIP(r *http.Request) string {
	forwardedFor := r.Header.Get("X-Forwarded-For")
	if forwardedFor != "" {
		return strings.SplitN(forwardedFor, ",", 2)[0]
	}
	// get remote ip address from r
	return r.RemoteAddr
}

func LoggingHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := getRequestId(c.Request)
		cont := c.Request.Context()
		clogger := util.Log.WithField(dtype.RID, requestID)
		cont = context.WithValue(cont, dtype.CLOG, clogger)
		remoteIP := getRemoteIP(c.Request)
		clogger.Info("Request ", remoteIP, " ", c.Request.Method, " ", c.Request.URL.Path)
		// c.Set(dtype.CLOG, clogger)
		c.Request = c.Request.WithContext(cont)
		c.Next()
		clogger.Info("Done ", c.Writer.Status())
	}
}

func DBConnectionHandler(handler func(c *gin.Context) error) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := c.Request.Context()
		tx := config.GetDBConnection().Begin()
		ctx = context.WithValue(ctx, dtype.DBCON, tx)
		c.Request = c.Request.WithContext(ctx)

		// this handles the logic to rollback transaction is case of panic
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback() // A panic occurred, rollback the transaction
				panic(r)      // Panic again to propagate it further
			}
		}()

		err := handler(c)
		if err != nil {
			tx.Rollback()
		} else {
			tx.Commit()
		}
	}
}

// Auth handler which only checks auth_token and not device_id
func AuthHandlerWithoutDeviceID(c *gin.Context) {
	logger := util.CLog(c.Request)

	authToken := c.GetHeader("auth_token")
	if authToken == "" {
		util.MarkRequestUnauthorize(c, "Unauthorized")
	}

	customerID, err := auth.GetAuthService().ValidateAuthToken(c.Request.Context(), authToken)
	if err != nil {
		logger.Errorf("auth failed for token: %s, err: %s", authToken, err)
		util.MarkRequestUnauthorize(c, "Unauthorized")
	}

	ctx := c.Request.Context()
	ctx = context.WithValue(ctx, dtype.CustomerID, customerID)
	c.Request = c.Request.WithContext(ctx)
}

func ServiceAuthHandler(c *gin.Context) {
	logger := util.CLog(c.Request)

	authToken := c.GetHeader("auth_token")
	if authToken == "" {
		util.MarkRequestUnauthorize(c, "Unauthorized")
		return
	}

	if authToken != config.GetConfigValues().ServiceSecretKey {
		logger.Errorf("auth failed for token: %s", authToken)
		util.MarkRequestUnauthorize(c, "Unauthorized")
		return
	}
}

func corsHandler() gin.HandlerFunc {
	return cors.New(
		cors.Config{
			AllowAllOrigins:  true,
			AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
			AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "auth_token", "device_id", "Cache-Control", "source", "partner_code", "jwt_token", "secret", "access-control-allow-origin"},
			ExposeHeaders:    []string{"Content-Length", "auth_token", "device_id"},
			AllowCredentials: false,
			MaxAge:           12 * time.Hour,
		},
	)
}
func VerifyJWT(tokenString string, secretKey string) (jwt.MapClaims, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		if token.Method != jwt.SigningMethodHS256 {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(secretKey), nil
	})

	if err != nil {
		return nil, fmt.Errorf("error parsing token: %v", err)
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}

func AuthenticateRequest(c *gin.Context) {
	logger := util.CLog(c.Request)
	var customerID int64
	var err error

	if len(c.GetHeader("partner_code")) > 0 {
		customerID, err = authenticatePartner(c)
	} else {
		customerID, err = authenticateToken(c, logger)
	}

	if err != nil {
		util.MarkRequestUnauthorize(c, "Unauthorized")
		return
	}

	ctx := context.WithValue(c.Request.Context(), dtype.CustomerID, customerID)
	c.Request = c.Request.WithContext(ctx)

	c.Next()
}

func authenticatePartner(c *gin.Context) (int64, error) {
	partnerCode := c.GetHeader("partner_code")
	jwtToken := c.GetHeader("jwt_token")
	result, err := auth.GetPartnerService().GetPartnersInfo(c.Request.Context(), partnerCode)
	if err != nil {
		return 0, err
	}
	jwtData, err := VerifyJWT(jwtToken, result.JwtKey)
	if err != nil {
		fmt.Printf("%s", err.Error())
		return 0, fmt.Errorf("%s", err.Error())
	}
	customerIDStr, ok := jwtData["customer_id"].(string)
	if !ok {
		return 0, fmt.Errorf("customer id not found")
	}

	customerId, err := strconv.ParseInt(customerIDStr, 10, 64)
	if err != nil {
		return 0, fmt.Errorf("invalid customer_id")
	}

	return customerId, nil
}

func authenticateToken(c *gin.Context, logger *logrus.Entry) (int64, error) {
	authToken := c.GetHeader("auth_token")
	if authToken == "" {
		logger.Errorf("Missing auth token")
		return 0, fmt.Errorf("missing auth token")
	}

	customerID, err := auth.GetAuthService().ValidateAuthToken(c.Request.Context(), authToken)
	if err != nil {
		logger.Errorf("Auth token validation failed: %v", err)
		return 0, err
	}

	return customerID, nil
}
