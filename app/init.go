package app

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/gin-gonic/gin"
	"github.com/newrelic/go-agent/v3/integrations/nrgin"

	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
	"go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp"
	"go.opentelemetry.io/otel"
	apiTrace "go.opentelemetry.io/otel/trace"

	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/crons"
	"stashfin.com/stashfin/magneto/pkg"
	"stashfin.com/stashfin/magneto/util"
)

var tracer apiTrace.Tracer
var hcl http.Client

func InitializeApplication() (*gin.Engine, error) {
	// Create a root context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer func() {
		if err := recover(); err != nil {
			cancel() // Ensure context is canceled if we panic
			log.Printf("Panic during initialization: %v", err)
		}
	}()

	hcl = http.Client{
		Transport: otelhttp.NewTransport(http.DefaultTransport),
	}

	_, err := setupOTelSDK(ctx)
	if err != nil {
		cancel()
		return nil, nil
	}

	tracer = otel.Tracer("stashfin.com/stashfin/magneto/app")

	conf, err := config.InitializeAppConfig()
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to load configuration: %w", err)
	}

	config.InitializeSentry(conf)

	err = config.InitializeAWSClients(ctx, conf)
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to initialize AWS clients: %w", err)
	}

	// Add a specific timeout for DB initialization
	dbCtx, dbCancel := context.WithTimeout(ctx, 45*time.Second)
	defer dbCancel()

	db, err := config.InitializeDatabase(dbCtx, conf)
	if err != nil || db == nil {
		cancel()
		return nil, fmt.Errorf("failed to connect to the database: %w", err)
	}

	if err := util.InitializeEncryptor(conf.EncryptionKey); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to initialize encryptor: %w", err)
	}
	pkg.InitializeApps()
	client.InitializeClients(conf)
	config.InitializeRedis(conf)

	nrapp, err := config.InitializeNewrelic(conf.NewRelicKey, conf.AppName, conf.Environment)
	if err != nil {
		log.Fatalf("Failed to load newrelic: %v", err)
	}

	grouter := gin.Default()
	grouter.Use(otelgin.Middleware(os.Getenv("OTEL_SERVICE_NAME")))
	grouter.Use(LoggingHandler())
	grouter.Use(corsHandler())
	if nrapp != nil {
		grouter.Use(nrgin.Middleware(nrapp))
	}

	AddRoutes(grouter)
	if config.GetConfigValues().CronEnable {
		go crons.IntializeCrons()
	}
	return grouter, nil
}

func DeferredOperations() {
	sentry.Flush(2 * time.Second)
}
