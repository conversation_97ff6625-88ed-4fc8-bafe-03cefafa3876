package app

import (
	"github.com/gin-gonic/gin"

	"stashfin.com/stashfin/magneto/controller"
)

func AddRoutes(router gin.IRouter) {
	router.GET("/ping", controller.Ping)
	router.POST("/save_account_details", controller.SaveAccountDetails)
	router.POST("/save_upi_status_details", controller.SaveUpiStatusDetails)
	router.GET("/upi_profile", controller.GetBankAccountDetails)
	router.GET("/my_passbook", controller.GetTransHistory)
	router.POST("/save_transaction", controller.SaveTransaction)
	router.POST("/create_bigcity_voucher", controller.CreateBigcityVoucher)
	router.GET("/get_voucher_history", controller.GetVoucherHistory)
	router.DELETE("/remove_bank_accounts", controller.RemoveBankAccounts)
	router.POST("/GetComplaintReasonCode", controller.GetComplaintReasonCodes)
	router.POST("/RaiseTransactionComplaint", controller.RaiseTransactionComplaint)
	router.POST("/CheckTransactionDisputeStatus", controller.GetComplaintStatus)
	router.POST("/v1/icici/sp-status", controller.ICICIPaymentCallback)
	router.POST("/check-transaction-status", controller.CheckTransactionStatus)

	leadRouter := router.Group("/lead")
	leadRouter.GET("/detail", AuthHandlerWithoutDeviceID, controller.GetLeadDetail)
	leadRouter.POST("/submit", AuthHandlerWithoutDeviceID, controller.CreateCustomerLead)

	insuranceRouter := router.Group("/insurance")
	insuranceRouter.GET("/product/:uid", controller.InsuranceProductDetails)
	insuranceRouter.POST("/request-otp", controller.RequestOTP)
	insuranceRouter.POST("/verify-otp", controller.VerifyOTP)
	insuranceRouter.POST("/applications/:application_uid/post-purchase", ServiceAuthHandler, controller.InsurancePostPurchase)
	insuranceRouter.POST("/applications", AuthHandlerWithoutDeviceID, controller.CreateOrGetInsuranceApplication)
	insuranceRouter.PUT("/applications/:application_uid", AuthHandlerWithoutDeviceID, controller.SaveApplicationDetails)
	insuranceRouter.GET("/payment-status/:application_uid", AuthHandlerWithoutDeviceID, controller.GetInsurancePaymentStatus)
	insuranceRouter.GET("/applications/:application_uid/retry-payment", AuthHandlerWithoutDeviceID, controller.GetRetryPaymentLink)
	insuranceRouter.GET("/feedback/:application_uid", AuthHandlerWithoutDeviceID, controller.GetFeedbackQuestion)
	insuranceRouter.POST("/feedback/:application_uid", AuthHandlerWithoutDeviceID, controller.SaveFeedbackResponse)
	insuranceRouter.POST("/product/updated-info/:uid", controller.GetProductUpdatedInfo)
	insuranceRouter.POST("/internal/create-success-application", ServiceAuthHandler, DBConnectionHandler(controller.CreateSuccessApplication))
	insuranceRouter.GET("/home", controller.GetInsuranceHomePageData)
	insuranceRouter.GET("/sub-catagory/:uid", controller.GetInsuranceSubCatagories)
	insuranceRouter.GET("/listing/:uid", controller.GetInsuranceListingPageData)
	insuranceRouter.GET("/banner", controller.GetInsuranceBanner)
	insuranceRouter.POST("/stashcash/form/submit", controller.SaveExternalApplicationDetail)

	insuranceV2Router := router.Group("/insurance/v2")
	insuranceV2Router.POST("/application", AuthHandlerWithoutDeviceID, controller.CreateOrGetInsuranceApplicationV2)

	cliRouter := router.Group("/cli")
	cliRouter.POST("/send-otp", controller.SendOtp)
	cliRouter.POST("/verify-cli", controller.VerifyCli)

	restructureLoanRouter := router.Group("/restructure-loan")
	restructureLoanRouter.GET("/get-details", AuthHandlerWithoutDeviceID, controller.GetRestructureDetails)
	restructureLoanRouter.POST("/send-otp", AuthHandlerWithoutDeviceID, controller.RestructureLoanSendOTP)
	restructureLoanRouter.POST("/verify", AuthHandlerWithoutDeviceID, controller.VerifyRestructure)

	authRouter := router.Group("/auth")
	authRouter.POST("/request-otp", controller.AuthRequestOTP)
	authRouter.POST("/login", controller.AuthLogin)

	offerRouter := router.Group("/offer")
	offerRouter.GET("/", AuthHandlerWithoutDeviceID, controller.GetOfferListingPage)
	offerRouter.GET("/:uid", AuthHandlerWithoutDeviceID, controller.GetOfferDetails)
	offerRouter.PUT("/update-cart", AuthHandlerWithoutDeviceID, DBConnectionHandler(controller.UpdateCart))
	offerRouter.GET("/cart", AuthHandlerWithoutDeviceID, controller.GetUserCart)
	offerRouter.GET("/payment-link", AuthHandlerWithoutDeviceID, DBConnectionHandler(controller.GetPaymentLink))
	offerRouter.GET("/payment-status/:order_uid", AuthHandlerWithoutDeviceID, controller.GetOrderPaymentStatus)
	offerRouter.GET("/orders", AuthHandlerWithoutDeviceID, controller.GetUserOrdersHistory)
	offerRouter.GET("/fetch-products", controller.FetchProducts)
	offerRouter.POST("/loc-discount", AuthHandlerWithoutDeviceID, DBConnectionHandler(controller.LocDiscount))
	offerRouter.GET("/fetch-coupon-codes", controller.FetchOrdersCouponCodes)
	offerRouter.GET("/check-okyc", AuthHandlerWithoutDeviceID, DBConnectionHandler(controller.Getcheckokyc))
	offerRouter.GET("/get_order_uid", DBConnectionHandler(controller.Getpaymentuid))

	offerRouter.POST("/pdfhtml", DBConnectionHandler(controller.Htmlpdf))

	ordersystemRouter := router.Group("/ordersystem")
	ordersystemRouter.POST("/post-purchase", ServiceAuthHandler, controller.OrderPostPurchase)
	ordersystemRouter.GET("/check-orders-timeout", controller.OrdersCheckTimeout)

	clevertapRouter := router.Group("/clevertap")
	clevertapRouter.PUT("/send-event-from-web", AuthenticateRequest, controller.SendEventFromWeb)
}
