package qwikgift

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"

	"stashfin.com/stashfin/magneto/helper"
)

const (
	CategoriesAPIURL = "/rest/v3/catalog/categories/"
	GetBrandsURL     = "/API/v1/getbrands"
	PullVoucherURL   = "/API/v1/pullvoucher"
)

type Client struct {
	baseURL    string
	username   string
	password   string
	key        string
	ivKey      string
	httpClient *helper.Client
}

func NewClient(baseURL, username, password, key, ivKey string) *Client {
	return &Client{
		baseURL:    baseURL,
		username:   username,
		password:   password,
		key:        key,
		ivKey:      ivKey,
		httpClient: helper.NewHttpClient(),
	}
}

func (c *Client) DecryptAndParseData(data string, resultStruct interface{}) error {
	key := []byte(c.key)
	iv := []byte(c.ivKey)

	block, err := aes.NewCipher(key)
	if err != nil {
		log.Println("Error creating AES cipher block:", err)
		return err
	}

	if len(iv) != aes.BlockSize {
		log.Println("IV length must be equal to block size")
		return fmt.Errorf("IV length must be equal to block size")
	}

	blockMode := cipher.NewCBCDecrypter(block, iv)
	ciphertext, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		log.Println("Error decoding base64:", err)
		return err
	}

	plaintext := make([]byte, len(ciphertext))
	blockMode.CryptBlocks(plaintext, ciphertext)

	// Unpad the plaintext (assuming PKCS7 padding)
	unpaddedLength := len(plaintext)
	padding := int(plaintext[unpaddedLength-1])
	plaintext = plaintext[:unpaddedLength-padding]

	// Parse the decrypted data into the provided struct
	err = json.Unmarshal(plaintext, resultStruct)
	if err != nil {
		log.Println("Error unmarshalling JSON:", err)
		return err
	}

	return nil
}

func (c *Client) EncryptData(data interface{}) string {
	key := []byte(c.key)
	iv := []byte(c.ivKey)

	var jsonData []byte
	var err error

	switch v := data.(type) {
	case string:
		jsonData = []byte(v)
	case []byte:
		jsonData = v
	default:
		jsonData, err = json.Marshal(data)
		if err != nil {
			log.Println("Error marshalling JSON:", err)
			return ""
		}
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		log.Println("Error creating AES cipher block:", err)
		return ""
	}

	if len(iv) != aes.BlockSize {
		log.Println("IV length must be equal to block size")
		return ""
	}

	blockMode := cipher.NewCBCEncrypter(block, iv)
	paddedData := c.AddPKCS7Padding(jsonData, aes.BlockSize)
	encrypted := make([]byte, len(paddedData))

	blockMode.CryptBlocks(encrypted, paddedData)

	return base64.StdEncoding.EncodeToString(encrypted)
}

func (c *Client) AddPKCS7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - (len(data) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

func (c *Client) GetToken() (string, error) {
	// need to add logic for redis save, fetch token
	token, err := c.FetchToken()
	if err != nil {
		return "", err
	}
	return token, nil
}

func (c *Client) FetchToken() (string, error) {
	var result VoucherResponse
	url := c.baseURL + TokenAPIURL
	headers := map[string]string{
		"Content-Type": "application/json",
		"username":     c.username,
		"password":     c.password,
	}
	err := c.httpClient.Get(url, headers, &result)
	if err != nil {
		return "", err
	}

	var token string
	err = c.DecryptAndParseData(result.Data, &token)
	if err != nil {
		return "", err
	}

	return token, nil
}

func (c *Client) GetBrands() ([]GiftVoucher, error) {
	token, err := c.GetToken()
	if err != nil {
		return nil, err
	}
	var result VoucherResponse
	url := c.baseURL + GetBrandsURL
	headers := map[string]string{
		"Content-Type": "application/json",
		"token":        token,
	}

	body := map[string]any{}

	err = c.httpClient.Post(url, headers, body, &result)
	if err != nil {
		return nil, err
	}

	var data []GiftVoucher
	err = c.DecryptAndParseData(result.Data, &data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

func (c *Client) PullVoucher(brandProductCode, extOrderID string, quantity, denomination int) (any, error) {
	token, err := c.GetToken()
	if err != nil {
		return "", err
	}
	var result VoucherResponse
	url := c.baseURL + PullVoucherURL
	headers := map[string]string{
		"Content-Type": "application/json",
		"token":        token,
	}

	body := map[string]interface{}{
		"BrandProductCode": brandProductCode,
		"ExternalOrderId":  extOrderID,
		"Quantity":         quantity,
		"Denomination":     denomination,
	}

	encryptedData := c.EncryptData(body)

	encryptedBody := map[string]interface{}{
		"payload": encryptedData,
	}

	err = c.httpClient.Post(url, headers, encryptedBody, &result)
	if err != nil {
		return "", err
	}

	var data PullVoucherResponse
	err = c.DecryptAndParseData(result.Data, &data)
	if err != nil {
		return "", err
	}

	return data, nil
}
