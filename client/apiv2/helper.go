package apiv2

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"net/url"
)

func (c *Client) generateFormDataWithChecksum(requestBody map[string]string) string {
	bodyStr := ""
	for k, v := range requestBody {
		if len(bodyStr) > 0 {
			bodyStr += "&"
		}
		bodyStr += k + "=" + url.QueryEscape(v)
	}

	h := hmac.New(sha256.New, []byte(c.secretKey))
	h.Write([]byte(bodyStr))
	checksum := hex.EncodeToString(h.Sum(nil))

	return bodyStr + "&checksum=" + checksum
}

func (c *Client) generateFormData(requestBody map[string]string) string {
	bodyStr := ""
	for k, v := range requestBody {
		if len(bodyStr) > 0 {
			bodyStr += "&"
		}
		bodyStr += k + "=" + url.QueryEscape(v)
	}

	return bodyStr
}
