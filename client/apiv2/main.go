package apiv2

import (
	"context"
	"strconv"

	"stashfin.com/stashfin/magneto/helper"
)

type Client struct {
	baseURL       string
	secretKey     string
	deviceVersion string
	httpClient    *helper.Client
}

func NewClient(baseURL, secretKey, deviceVersion string) *Client {
	return &Client{
		baseURL:       baseURL,
		secretKey:     secretKey,
		deviceVersion: deviceVersion,
		httpClient:    helper.NewHttpClient(),
	}
}

func (c *Client) RequestOTP(ctx context.Context, phone int) error {
	body := map[string]string{
		"phone": strconv.Itoa(phone),
		"mode":  "generate_otp",
	}

	formData := c.generateFormDataWithChecksum(body)

	var result OTPResponse
	header := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}

	err := c.httpClient.PostWithFormData(c.baseURL, header, formData, &result)

	// retry once in-case Request OTP fails
	if err != nil {
		err = c.httpClient.PostWithFormData(c.baseURL, header, formData, &result)
		if err != nil {
			return err
		}
	}

	if result.Status == StatusTypeError {
		return helper.NotFoundError(result.Message)
	}

	return nil
}

func (c *Client) VerifyOTP(ctx context.Context, phone int, otp int) (string, error) {
	body := map[string]string{
		"phone": strconv.Itoa(phone),
		"otp":   strconv.Itoa(otp),
		"mode":  "login",
	}

	formData := c.generateFormDataWithChecksum(body)

	var result OTPVerifyResponse
	header := map[string]string{
		"Content-Type":   "application/x-www-form-urlencoded",
		"device_version": c.deviceVersion,
	}
	err := c.httpClient.PostWithFormData(c.baseURL, header, formData, &result)
	if err != nil {
		return "", err
	}

	if result.Status == StatusTypeError {
		return "", helper.NotFoundError(result.Message)
	}

	return result.AuthToken, nil
}

func (c *Client) RequestOTPBeforeRegistration(ctx context.Context, phone int) error {
	body := map[string]string{
		"phone_number": strconv.Itoa(phone),
		"mode":         "sendOTPBeforeRegistration",
	}

	formData := c.generateFormDataWithChecksum(body)

	var result OTPResponse
	header := make(map[string]string)
	err := c.httpClient.PostWithFormData(c.baseURL, header, formData, &result)

	// retry once in-case Request OTP fails
	if err != nil {
		err = c.httpClient.PostWithFormData(c.baseURL, header, formData, &result)
		if err != nil {
			return err
		}
	}

	if result.Status == StatusTypeError {
		return helper.NotFoundError(result.Message)
	}

	return nil
}

func (c *Client) VerifyOTPBeforeRegistration(ctx context.Context, phone int, otp int) error {
	body := map[string]string{
		"phone_number": strconv.Itoa(phone),
		"otp":          strconv.Itoa(otp),
		"mode":         "verifyOTPBeforeRegistration",
	}

	formData := c.generateFormDataWithChecksum(body)

	var result OTPResponse
	header := make(map[string]string)
	err := c.httpClient.PostWithFormData(c.baseURL, header, formData, &result)
	if err != nil {
		return err
	}

	if result.Status == StatusTypeError {
		return helper.NotFoundError(result.Message)
	}

	return nil
}

func (c *Client) VerifyOTPAndRegisterUser(ctx context.Context, phone int, otp int, utmSource string) (string, error) {
	body := map[string]string{
		"phone":      strconv.Itoa(phone),
		"otp":        strconv.Itoa(otp),
		"mode":       "login",
		"register":   "1",
		"utm_source": utmSource,
	}

	formData := c.generateFormDataWithChecksum(body)

	var result OTPVerifyResponse
	header := map[string]string{
		"Content-Type":   "application/x-www-form-urlencoded",
		"device_version": c.deviceVersion,
	}
	err := c.httpClient.PostWithFormData(c.baseURL, header, formData, &result)
	if err != nil {
		return "", err
	}

	if result.Status == StatusTypeError {
		return "", helper.NotFoundError(result.Message)
	}

	return result.AuthToken, nil
}

func (c *Client) GetOKycUrl(ctx context.Context, customerID int64) (*OkycLinkResponse, error) {
	body := map[string]string{
		"customer_id": strconv.FormatInt(customerID, 10),
		"mode":        "getOkycLink",
		"source":      "offer",
	}

	// formData := c.generateFormData(body)
	formData := c.generateFormDataWithChecksum(body)

	var result OkycLinkResponse
	header := map[string]string{
		"Content-Type": "application/x-www-form-urlencoded",
	}
	err := c.httpClient.PostWithFormData(c.baseURL, header, formData, &result)
	if err != nil {
		return nil, err
	}

	if result.RedirectUrl == "" {
		return nil, helper.NotFoundError("error while getting okyc url")
	}

	return &result, nil
}
