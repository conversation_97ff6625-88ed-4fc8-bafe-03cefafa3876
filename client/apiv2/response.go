package apiv2

type StatusType string

const (
	StatusTypeSuccess StatusType = "success"
	StatusTypeError   StatusType = "error"
)

type OTPResponse struct {
	Status  StatusType `json:"status"`
	Message string     `json:"message"`
}

type OTPVerifyResponse struct {
	Status    StatusType `json:"status"`
	Message   string     `json:"message"`
	AuthToken string     `json:"auth_token"`
}

type OkycLinkResponse struct {
	Status      string `json:"status"`
	RedirectUrl string `json:"redirect_url"`
}
