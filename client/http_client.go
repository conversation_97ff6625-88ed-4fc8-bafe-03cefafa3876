package client

import (
	"bytes"
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"

	"stashfin.com/stashfin/magneto/util"
)

func PostJsonRequest(url string, header map[string]string, reqBody interface{}, apiTag string,
	ctx context.Context) (
	error, map[string]interface{}) {

	var payload map[string]interface{}
	data, _ := json.Marshal(reqBody)
	json.Unmarshal(data, &payload)

	jsonData, err := json.Marshal(payload)
	if err != nil {
		return err, nil
	}

	req, err := http.NewRequest("POST", url, bytes.NewBuffer(jsonData))
	if err != nil {
		return err, nil
	}

	if header != nil {
		for key, value := range header {
			req.Header.Set(key, value)
		}
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		util.Log.Errorf("Error while sending request for %s api, response %s", apiTag, err.Error())
		return err, nil
	}

	defer resp.Body.Close()

	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err, nil
	}

	var response map[string]interface{}
	err = json.Unmarshal(bodyBytes, &response)
	if err != nil {
		return err, nil
	}

	responseJSON, err := json.Marshal(response)
	if err != nil {
		return err, nil
	}

	util.Log.Infof("Response for %s API is : %s", apiTag, string(responseJSON))
	return nil, response
}
