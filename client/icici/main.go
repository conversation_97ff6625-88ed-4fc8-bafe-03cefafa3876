package icici

import (
	"context"
	"encoding/json"
	"fmt"

	"stashfin.com/stashfin/magneto/helper"
)

type Client struct {
	baseURL           string
	apiKey            string
	checkStatusApiKey string
	httpClient        *helper.Client
}

func NewClient(baseURL, apiKey string, checkStatusApiKey string) *Client {
	return &Client{
		baseURL:           baseURL,
		apiKey:            apiKey,
		checkStatusApiKey: checkStatusApiKey,
		httpClient:        helper.NewHttpClient(),
	}
}

func (c *Client) GetComplaintReasonCode(ctx context.Context) (
	*ComplaintReasonCodesResponse, error) {
	var result ICICIApiResponse

	url := c.baseURL + "/api/v1/upi2/GetComplaintReasonCode"
	headers := map[string]string{
		"apiKey":       c.apiKey,
		"Content-Type": "application/json",
	}
	encryptedBody, err := helper.GetICICIEncryptedRequest(map[string]string{})
	if err != nil {
		return nil, err
	}

	err = c.httpClient.Post(url, headers, encryptedBody, &result)
	if err != nil {
		return nil, err
	}

	if result.EncryptedKey == "" && result.EncryptedData == "" {
		return nil, fmt.Errorf("encryptedData or encryptedKey in response in empty")
	}

	decryptedBody, err := helper.GetICICIDecryptedResponse(result.EncryptedData, result.EncryptedKey)
	if err != nil {
		return nil, err
	}

	jsonData, err := json.Marshal(decryptedBody)
	if err != nil {
		return nil, fmt.Errorf("Error marshaling map to JSON: %s", err)
	}

	var response ComplaintReasonCodesResponse
	if err := json.Unmarshal(jsonData, &response); err != nil {
		return nil, fmt.Errorf("Error unmarshaling JSON to struct: %s", err)
	}

	return &response, nil
}

func (c *Client) RaiseTransactionComplaint(ctx context.Context, body map[string]string) (
	map[string]interface{}, error) {
	var result ICICIApiResponse

	url := c.baseURL + "/api/v1/upi2/RaiseTransactionComplaint"
	headers := map[string]string{
		"apiKey":       c.apiKey,
		"Content-Type": "application/json",
	}
	encryptedBody, err := helper.GetICICIEncryptedRequest(body)
	if err != nil {
		return nil, err
	}

	err = c.httpClient.Post(url, headers, encryptedBody, &result)
	if err != nil {
		return nil, err
	}

	if result.EncryptedKey == "" && result.EncryptedData == "" {
		return nil, fmt.Errorf("encryptedData or encryptedKey in response in empty")
	}

	decryptedBody, err := helper.GetICICIDecryptedResponse(result.EncryptedData, result.EncryptedKey)
	if err != nil {
		return nil, err
	}

	return decryptedBody, nil
}

func (c *Client) CheckTransactionDisputeStatus(ctx context.Context, body map[string]string) (
	map[string]interface{}, error) {
	var result ICICIApiResponse

	url := c.baseURL + "/api/v1/upi2/CheckTransactionDisputeStatus"
	headers := map[string]string{
		"apiKey":       c.apiKey,
		"Content-Type": "application/json",
	}
	encryptedBody, err := helper.GetICICIEncryptedRequest(body)
	if err != nil {
		return nil, err
	}

	err = c.httpClient.Post(url, headers, encryptedBody, &result)
	if err != nil {
		return nil, err
	}

	if result.EncryptedKey == "" && result.EncryptedData == "" {
		return nil, fmt.Errorf("encryptedData or encryptedKey in response in empty")
	}

	decryptedBody, err := helper.GetICICIDecryptedResponse(result.EncryptedData, result.EncryptedKey)
	if err != nil {
		return nil, err
	}

	return decryptedBody, nil
}

func (c *Client) TransactionStatus(ctx context.Context, body map[string]string) (
	map[string]interface{}, error) {
	var result ICICIApiResponse

	url := c.baseURL + "/api/v1/upi2/TransactionStatus"
	headers := map[string]string{
		"apiKey":       c.checkStatusApiKey,
		"Content-Type": "application/json",
	}

	encryptedBody, err := helper.GetICICIEncryptedRequest(body)
	if err != nil {
		return nil, err
	}

	err = c.httpClient.Post(url, headers, encryptedBody, &result)
	if err != nil {
		return nil, err
	}

	if result.EncryptedKey == "" && result.EncryptedData == "" {
		return nil, fmt.Errorf("encryptedData or encryptedKey in response in empty")
	}

	decryptedBody, err := helper.GetICICIDecryptedResponse(result.EncryptedData, result.EncryptedKey)
	if err != nil {
		return nil, err
	}

	return decryptedBody, nil
}
