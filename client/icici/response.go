package icici

type ICICIApiResponse struct {
	RequestId            string `json:"requestId"`
	Service              string `json:"Service"`
	EncryptedKey         string `json:"encryptedKey"`
	OaepHashingAlgorithm string `json:"oaepHashingAlgorithm"`
	IV                   string `json:"iv"`
	EncryptedData        string `json:"encryptedData"`
	ClientInfo           string `json:"clientInfo"`
	OptionalParam        string `json:"optionalParam"`
}

type ComplaintReasonCodesResponse struct {
	Response      string                 `json:"response"`
	MobileAppData []MobileAppDataPayload `json:"MobileAppData"`
}

type MobileAppDataPayload struct {
	ReasonCode  string `json:"reasonCode"`
	Description string `json:"description"`
}
