package client

import (
	"stashfin.com/stashfin/magneto/client/apiv2"
	"stashfin.com/stashfin/magneto/client/icici"
	"stashfin.com/stashfin/magneto/client/oneAssist"
	"stashfin.com/stashfin/magneto/client/paymentservice"
	"stashfin.com/stashfin/magneto/client/vouchergram"
	"stashfin.com/stashfin/magneto/dtype"
)

var (
	apiV2Client       *apiv2.Client
	paymentService    *paymentservice.Client
	iciciClient       *icici.Client
	oneAssistClient   *oneAssist.Client
	voucherGramClient *vouchergram.Client
)

func InitializeClients(config *dtype.AppConfig) {
	apiV2Client = apiv2.NewClient(config.ApiV2BaseURL, config.ApiV2SecretKey, config.ApiV2AppVersion)
	paymentService = paymentservice.NewClient(config.PaymentServiceURL, config.WebBaseURL)
	iciciClient = icici.NewClient(config.ICICIBaseURL, config.ICICIApiKey, config.ICICICheckStatusApiKey)
	oneAssistClient = oneAssist.NewClient(config.OneAssistBaseURL, config.OneAssistSecretKey, config.OneAssistBaseURL2, config.OneAssistSecretKey2)
	voucherGramClient = vouchergram.NewClient(config.VoucherGramBaseURL, config.VoucherGramUsername, config.VoucherGramPassword, config.VoucherGramKey, config.VoucherGramIvKey)

}

func GetApiV2Client() *apiv2.Client {
	return apiV2Client
}

func GetPaymentService() *paymentservice.Client {
	return paymentService
}

func GetICICIClient() *icici.Client {
	return iciciClient
}

func GetOneAssistClient() *oneAssist.Client {
	return oneAssistClient
}

func GetVoucherGramClient() *vouchergram.Client {
	return voucherGramClient
}
