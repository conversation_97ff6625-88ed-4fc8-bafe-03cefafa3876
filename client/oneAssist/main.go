package oneAssist

import (
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
	"stashfin.com/stashfin/magneto/util"
)

const (
	OnboardCustomerURL = "/apigateway/OASYS/webservice/rest/customer/onboardCustomer"
)

type Client struct {
	baseURL    string
	secretKey  string
	baseUrl2   string
	httpClient *helper.Client
	secretKey2 string
}

func NewClient(baseURL, secretKey, baseURL2, secretKey2 string) *Client {
	return &Client{
		baseURL:    baseURL,
		secretKey:  secretKey,
		secretKey2: secretKey2,
		baseUrl2:   baseURL2,
		httpClient: helper.NewHttpClient(),
	}
}

func (c *Client) OnboardCustomer(customerPhoneNumber string, applicationDetails *model.InsuranceApplication, applicationExtraDetails *model.InsuranceApplicationExtraDetails) (*CustomerOnboardingResponse, error) {
	var result CustomerOnboardingResponse
	var body map[string]interface{}
	url := c.baseURL + OnboardCustomerURL
	headers := map[string]string{
		"Authorization": c.secret<PERSON>ey,
		"Content-Type":  "application/json",
	}
	if applicationDetails.Product.ApiVersion == 1 {
		body = map[string]any{
			"addressInfo": []any{
				map[string]any{
					"addrLine1": applicationExtraDetails.Address,
					"addrType":  "PER",
					"pinCode":   applicationExtraDetails.Pincode,
				},
			},
			"customerInfo": []any{map[string]any{
				"assetInfo": []any{
					map[string]any{
						"categoryCode": "F",
						"productCode":  "CARD",
						"productName":  "card",
					},
					map[string]any{
						"categoryCode": "F",
						"productCode":  "UPI",
						"productName":  "UPI",
					},
					map[string]any{
						"categoryCode": "F",
						"productCode":  "MWALLET",
						"productName":  "mWallet",
					},
				},
				"emailId":      applicationExtraDetails.Email,
				"firstName":    applicationExtraDetails.Name,
				"mobileNumber": customerPhoneNumber,
				"relationship": "self",
			}},
			"initiatingSystem": "46594256",
			"orderInfo": map[string]any{
				"applicationNo": applicationDetails.UID,
				"partnerBUCode": applicationDetails.Product.Insurer.PartnerBuCode,
				"partnerCode":   applicationDetails.Product.Insurer.PartnerCode,
				"planCode":      applicationDetails.Product.PlanID,
			},
			"paymentInfo": map[string]any{
				"paymentMode": "COD",
			},
		}
	} else {
		body = map[string]any{
			"customerInfo": []map[string]any{
				{
					"customerdob":  util.FormatDateWithPattern(applicationExtraDetails.DateOfBirth, "02-Jan-2006"),
					"firstName":    applicationExtraDetails.Name,
					"emailId":      applicationExtraDetails.Email,
					"mobileNumber": customerPhoneNumber,
					"insuredDetails": []map[string]any{
						{
							"address":              applicationExtraDetails.Address,
							"customerRelationType": "self",
							"dateOfBirth":          util.FormatDateWithPattern(applicationExtraDetails.DateOfBirth, "02-Jan-2006"),
							"emailId":              applicationExtraDetails.Email,
							"gender":               applicationExtraDetails.Gender,
							"mobileNumber":         customerPhoneNumber,
							"name":                 applicationExtraDetails.Name,
							"nomineeInfo": []map[string]any{
								{
									"name":         applicationExtraDetails.NomineeName,
									"relationship": model.GetRelationName(applicationExtraDetails.NomineeRelation),
								},
							},
							"pincode":     applicationExtraDetails.Pincode,
							"productCode": "Individual",
						},
					},
					"relationship": "self",
				},
			},
			"initiatingSystem": "46594256",
			"orderInfo": map[string]any{
				"memStartDate":  util.FormatDateWithPattern(applicationExtraDetails.MembershipStartDate, "02-Jan-2006"),
				"planCode":      applicationDetails.Product.PlanID,
				"partnerCode":   applicationDetails.Product.Insurer.PartnerCode,
				"partnerBUCode": applicationDetails.Product.Insurer.PartnerBuCode,
				"applicationNo": applicationDetails.UID,
			},
			"paymentInfo": map[string]any{
				"paymentMode": "COD",
			},
		}

	}

	if applicationDetails.Product.ApiVersion == 2 {
		url = c.baseUrl2 + OnboardCustomerURL
		headers["Authorization"] = c.secretKey
	}
	err := c.httpClient.Post(url, headers, body, &result)
	if err != nil {
		return nil, err
	}
	return &result, nil
}
