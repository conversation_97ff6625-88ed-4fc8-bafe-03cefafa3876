package oneAssist

type CustomerOrderInfo struct {
	Discount       float64 `json:"discount"`
	OrderID        int64   `json:"orderId"`
	PlanName       string  `json:"planName"`
	PlanID         int64   `json:"planId"`
	TaxAmount      float64 `json:"taxAmount"`
	Price          float64 `json:"price"`
	ActivationCode string  `json:"activationCode"`
	OrderUUID      string  `json:"orderUUID"`
	OasysRefID     int64   `json:"oasysRefId"`
	Activity       string  `json:"activity"`
	SubActivity    string  `json:"subActivity"`
}

type MembershipInfo struct {
	MembershipID               int64       `json:"membershipId"`
	Status                     string      `json:"status"`
	OrderID                    int64       `json:"orderId"`
	OrderUUID                  string      `json:"orderUUID"`
	ActivationCode             string      `json:"activationCode"`
	OasysRefID                 int64       `json:"oasysRefId"`
	CrmRefID                   int64       `json:"crmRefId"`
	ModifyMemStatus            string      `json:"modifyMemStatus"`
	Relationship               string      `json:"relationship"`
	Activity                   string      `json:"activity"`
	CodPhoneNo                 string      `json:"codPhoneNo"`
	ProdCode                   string      `json:"prodCode"`
	PlanCode                   string      `json:"planCode"`
	Frequency                  string      `json:"frequency"`
	MemStatus                  string      `json:"memStatus"`
	MemStartDate               string      `json:"memStartDate"`
	MemEndDate                 string      `json:"memEndDate"`
	RejectReason               string      `json:"rejectReason"`
	ApplicationNo              string      `json:"applicationNo"`
	MembershipName             string      `json:"membershipName"`
	AccountNo                  int64       `json:"accountNo"`
	PartnerCode                int64       `json:"partnerCode"`
	PlanID                     int64       `json:"planId"`
	MembershipCancellationDate string      `json:"membershipCancellationDate"`
	ListPrice                  float64     `json:"listPrice"`
	SalesPrice                 float64     `json:"salesPrice"`
	Price                      float64     `json:"price"`
	InvoiceNo                  int64       `json:"invoiceNo"`
	TrialDays                  int64       `json:"trialDays"`
	Discount                   float64     `json:"discount"`
	BalanceAmt                 int64       `json:"balanceAmt"`
	RefundAmt                  int64       `json:"refundAmt"`
	RenewalPerformed           string      `json:"renewalPerformed"`
	PromoCode                  string      `json:"promoCode"`
	RenewalNo                  string      `json:"renewalNo"`
	UpdateReason               string      `json:"updateReason"`
	PlanFreq                   string      `json:"planFreq"`
	PartnerBUCode              string      `json:"partnerBUCode"`
	ChargeType                 string      `json:"chargeType"`
	Trial                      string      `json:"trial"`
	DiscountAllowed            string      `json:"discountAllowed"`
	DownGradeAllowed           string      `json:"downGradeAllowed"`
	GraceDays                  int64       `json:"graceDays"`
	PartnerInvoiceNo           string      `json:"partnerInvoiceNo"`
	ApplicationDate            string      `json:"applicationDate"`
	SiConsent                  string      `json:"siConsent"`
	UtmCampaign                string      `json:"utmCampaign"`
	CustTempID                 string      `json:"custTempId"`
	SrcChannelType             string      `json:"srcChannelType"`
	CreatedBy                  string      `json:"createdBy"`
	Quantity                   int64       `json:"quantity"`
	TaxAmt                     float64     `json:"taxAmt"`
	TaxCode                    string      `json:"taxCode"`
	BusinessPName              string      `json:"bussinessPName"`
	BusinessUName              string      `json:"businessUName"`
	SubActivity                string      `json:"subActivity"`
	CustTmpUUID                string      `json:"custTmpUUID"`
	NewServices                string      `json:"newServices"`
	OldServices                string      `json:"oldServices"`
	AssetInfoList              interface{} `json:"assetInfoList"`
	PlanVersion                interface{} `json:"planVersion"`
	IsCommunicationSent        interface{} `json:"isCommunicationSent"`
	ReferenceId                interface{} `json:"referenceId"`
	Categories                 interface{} `json:"categories"`
	DeletedCustomer            interface{} `json:"deletedCustomer"`
	MembershipNumber           int64       `json:"membershipNumber"`
	MemUUID                    string      `json:"memUUID"`
	StartDate                  string      `json:"startDate"`
	EndDate                    string      `json:"endDate"`
	PrimaryCustomerId          int64       `json:"primaryCustomerId"`
	SecondaryCustomerId        interface{} `json:"secondaryCustomerId"`
	Enterprise                 bool        `json:"enterprise"`
}

type ProductInfo struct {
	ProductCode       string      `json:"productCode"`
	ProductName       string      `json:"productName"`
	CategoryCode      string      `json:"categoryCode"`
	ProductAttributes interface{} `json:"productAttributes"`
}

type CustomerOnboardingResponse struct {
	PayNowLink            interface{}       `json:"payNowLink"`
	IsMotoPayment         interface{}       `json:"isMotoPayment"`
	PaymentMode           string            `json:"paymentMode"`
	IsMailSent            interface{}       `json:"isMailSent"`
	CustomerOrderInfo     CustomerOrderInfo `json:"customerOrderInfo"`
	MembershipInfo        MembershipInfo    `json:"membershipInfo"`
	PendingCustomerInfo   interface{}       `json:"pendingCustomerInfo"`
	ProductInfo           []ProductInfo     `json:"productInfo"`
	PrimaryCustomerName   string            `json:"primaryCustomerName"`
	SecondaryCustomerName interface{}       `json:"secondaryCustomerName"`
	Status                string            `json:"status"`
	Message               string            `json:"message"`
	Errors                interface{}       `json:"errors"`
}

type AssetInfo struct {
	ProductCode  string `json:"productCode"`
	ProductName  string `json:"productName"`
	CategoryCode string `json:"categoryCode"`
}

type CustomerInfo struct {
	FirstName    string      `json:"firstName"`
	MobileNumber int         `json:"mobileNumber"`
	EmailID      string      `json:"emailId"`
	Relationship string      `json:"relationship"`
	AssetInfo    []AssetInfo `json:"assetInfo"`
}

type AddressInfo struct {
	AddrType  string `json:"addrType"`
	AddrLine1 string `json:"addrLine1"`
	PinCode   string `json:"pinCode"`
}

type OrderInfo struct {
	PlanCode      string `json:"planCode"`
	PartnerCode   string `json:"partnerCode"`
	PartnerBUCode string `json:"partnerBUCode"`
	ApplicationNo string `json:"applicationNo"`
}

type PaymentInfo struct {
	PaymentMode string `json:"paymentMode"`
}

type HTTPRequestBody struct {
	InitiatingSystem string         `json:"initiatingSystem"`
	CustomerInfo     []CustomerInfo `json:"customerInfo"`
	AddressInfo      []AddressInfo  `json:"addressInfo"`
	OrderInfo        OrderInfo      `json:"orderInfo"`
	PaymentInfo      PaymentInfo    `json:"paymentInfo"`
}
