package paymentservice

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/insurance/repo"
	"stashfin.com/stashfin/magneto/util"
)

const (
	InsurancePaymentMode   = 9
	DealsPaymentMode       = 11
	CashFreePaymentGatway  = 2
	RazorpayPaymentGateway = 1
)

type Client struct {
	baseURL    string
	webBaseURL string
	httpClient *helper.Client
}

func NewClient(baseURL, webBaseURL string) *Client {
	return &Client{
		baseURL:    baseURL,
		webBaseURL: webBaseURL,
		httpClient: helper.NewHttpClient(),
	}
}

func (c *Client) GetPaymentLink(customerID int64, amount int, referenceID, mode int64, productName, productUID string, paymentGatway int, offerDiscount string, ctx context.Context) (string, error) {
	var headers map[string]string
	url := c.baseURL + "/generate-payment-link"
	txnId := uuid.New().String()
	var result GeneratePaymentLinkResponse
	body := map[string]any{
		"customer_id": customerID,
		"amount":      amount,
		"mode":        mode,
		"gateway":     paymentGatway,
		"extra_params": map[string]any{
			"reference_id":      referenceID,
			"amount":            amount,
			"product_name":      productName,
			"product_id":        productUID,
			"offer_discount_id": txnId,
		},
	}
	var offerDiscountDetails map[string]interface{}
	var jsonData []byte
	var err error
	if strings.Contains(offerDiscount, "amount") {
		err := json.Unmarshal([]byte(offerDiscount), &offerDiscountDetails)
		if err != nil {
			return "", err
		}
	} else {
		offerDiscountDetails, err = util.ConvertBase64ToJson(offerDiscount)
		if err != nil {
			return "", err
		}
	}

	jsonData, err = json.Marshal(offerDiscountDetails)
	if err != nil {
		return "", err
	}
	processor := dtype.GateWayModes[paymentGatway]
	var orderCustId = uint64(customerID)
	err = repo.CreateOverallOrderManagementTable(ctx, orderCustId, txnId, jsonData, int(mode), float64(amount), processor)
	if err != nil {
		return "", err
	}
	err = c.httpClient.Post(url, headers, body, &result)
	if err != nil {
		return "", err
	}

	if !result.Status {
		return "", fmt.Errorf("request failed, err: %s", result.Result)
	}
	return result.Result, nil
}

func (c *Client) GetDealPaymentLink(customerID int64, amount float32, referenceID string, method bool, offerDiscount string, ctx context.Context) (string, error) {
	var headers map[string]string
	url := c.baseURL + "/generate-payment-link"
	var result GeneratePaymentLinkResponse
	txnId := uuid.New().String()
	Type := ""
	if method {
		Type = "upi"
	}
	var gateway int
	if customerID == 15982483 {
		gateway = 1
	} else {
		gateway = config.GetConfigValues().OfferPaymentGateway
	}

	body := map[string]any{
		"customer_id": customerID,
		"amount":      amount,
		"mode":        DealsPaymentMode,
		"gateway":     gateway,
		"extra_params": map[string]any{
			"reference_id":      referenceID,
			"amount":            amount,
			"type":              Type,
			"offer_discount_id": txnId,
		},
	}
	offerDiscountDetails, err := util.ConvertBase64ToJson(offerDiscount)
	if err != nil {
		return "", err
	}
	jsonData, err := json.Marshal(offerDiscountDetails)
	if err != nil {
		return "", err
	}
	processor := dtype.GateWayModes[gateway]
	var orderCustId = uint64(customerID)
	err = repo.CreateOverallOrderManagementTable(ctx, orderCustId, txnId, jsonData, DealsPaymentMode, float64(amount), processor)
	if err != nil {
		return "", err
	}
	err = c.httpClient.Post(url, headers, body, &result)
	if err != nil {
		return "", err
	}

	if !result.Status {
		return "", fmt.Errorf("request failed, err: %s", result.Result)
	}

	redirectURL := c.webBaseURL + "/deals-and-offers/payment-status?order_uid=" + referenceID
	paymentLink := result.Result + "&redirect_url=" + redirectURL
	return paymentLink, nil
}
