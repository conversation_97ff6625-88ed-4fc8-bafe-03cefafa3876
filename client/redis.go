package client

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"stashfin.com/stashfin/magneto/dtype"

	"github.com/redis/go-redis/v9"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/util"
)

const (
	SendPN                       = "send:push_notification"
	SendSms                      = "send:sms"
	EmailStream                  = "send:email"
	ClevertapStream              = "upload:clevertap"
	ClevertapTemplateId          = "clevertap"
	AppsFlyerCleverTapTemplateId = "appsflyer_clevertap"
	AppsFlyerTemplateId          = "appsflyer"
)

func RedisStreamPush(ctx context.Context, customerId int64, templateId string, templateData map[string]any) error {
	templateDataJson, err := json.Marshal(templateData)
	if err != nil {
		return err
	}

	data := map[string]any{
		"customer_id":   customerId,
		"template_id":   templateId,
		"template_data": string(templateDataJson),
	}

	redisClient := config.GetRedis()

	_, err = redisClient.XAdd(ctx, &redis.XAddArgs{
		Stream: SendPN,
		Values: data,
	}).Result()

	if err != nil {
		return fmt.Errorf(
			"error occurred while pushing data to a Redis stream for customer: %d, template id: %s, and template data: %s",
			customerId,
			templateId,
			templateDataJson,
		)
	}

	return nil
}

func SendSMS(ctx context.Context, customerID int64, templateID string, templateData map[string]any) error {
	templateDataBytes, err := json.Marshal(templateData)
	if err != nil {
		return fmt.Errorf("failed to marshal templateDate, err: %s", err)
	}

	data := map[string]any{
		"customer_id":   customerID,
		"template_id":   templateID,
		"template_data": string(templateDataBytes),
	}

	redisClient := config.GetRedis()
	_, err = redisClient.XAdd(ctx, &redis.XAddArgs{
		Stream: SendSms,
		Values: data,
	}).Result()

	if err != nil {
		return fmt.Errorf(
			"error occurred while pushing data to a Redis stream for customer: %d, template id: %s, and template data: %+v, err: %s",
			customerID,
			templateID,
			templateData,
			err,
		)
	}

	return nil
}

func SendEmail(ctx context.Context, customerID int64, templateID string, templateData map[string]any, destination []string, attachments map[string][]byte) error {
	attachemntsData := map[string]string{}
	for k, v := range attachments {
		redisKey := util.GenerateRandomString(32)
		err := helper.CacheSet(ctx, redisKey, v, 21600)
		if err != nil {
			return err
		}
		attachemntsData[k] = redisKey
	}
	templateData["attachments"] = attachemntsData

	templateDataBytes, err := json.Marshal(templateData)
	if err != nil {
		return fmt.Errorf("failed to marshal templateDate, err: %s", err)
	}
	data := map[string]any{
		"customer_id":   customerID,
		"template_id":   templateID,
		"template_data": string(templateDataBytes),
	}

	if len(destination) > 0 {
		toJsonData, err := json.Marshal(map[string]any{
			"to": destination,
		})
		if err != nil {
			return err
		}
		data["destination"] = toJsonData
	}

	redisClient := config.GetRedis()
	_, err = redisClient.XAdd(ctx, &redis.XAddArgs{
		Stream: EmailStream,
		Values: data,
	}).Result()

	if err != nil {
		return fmt.Errorf(
			"error occurred while pushing data to a Redis stream for customer: %d, template id: %s, and template data: %+v, err: %s",
			customerID,
			templateID,
			templateData,
			err,
		)
	}

	return nil
}

func PushToClevertapStream(ctx context.Context, customerId int64, eventName string, eventProperty map[string]any, req dtype.ClevertapWebRequest) error {
	templateId := ClevertapTemplateId
	if req.AppsFlyer {
		templateId = AppsFlyerCleverTapTemplateId
	}
	if !req.Clevertap {
		templateId = AppsFlyerTemplateId
	}
	templateDataJson, err := json.Marshal(map[string]any{
		"type": "event",
		"data": map[string]any{
			"ts":      int(time.Now().Unix()),
			"evtName": eventName,
			"evtData": eventProperty,
		},
	})

	if err != nil {
		return err
	}

	data := map[string]any{
		"customer_id":   customerId,
		"template_id":   templateId,
		"template_data": string(templateDataJson),
	}
	redisClient := config.GetRedis()

	messageId, err := redisClient.XAdd(ctx, &redis.XAddArgs{
		Stream: ClevertapStream,
		Values: data,
	}).Result()

	if err != nil {
		return fmt.Errorf(
			"error occurred while pushing data to a Clevretap Events Redis stream for customer: %d, template id: %s, and template data: %s",
			customerId,
			ClevertapTemplateId,
			templateDataJson,
		)
	}
	fmt.Printf("clevertap event is successfully send for this customer: %d and message_id :%s\n", customerId, messageId)
	return nil
}
