package vouchergram

import (
	"encoding/json"
)

type VoucherResponse struct {
	Status string `json:"status"`
	Data   string `json:"data"`
	Desc   string `json:"desc"`
	Code   string `json:"code"`
}

type Denomination struct {
	Amount string `json:"amount"`
}

type RedeemStep struct {
	Text  string `json:"text"`
	Image string `json:"image"`
}

type GiftVoucher struct {
	BrandProductCode     string          `json:"BrandProductCode"`
	BrandName            string          `json:"BrandName"`
	BrandType            string          `json:"Brandtype"`
	RedemptionType       json.RawMessage `json:"RedemptionType"`
	OnlineRedemptionUrl  string          `json:"OnlineRedemptionUrl"`
	BrandImage           string          `json:"BrandImage"`
	DenominationList     string          `json:"denominationList"`
	StockAvailable       BoolString      `json:"stockAvailable"`
	Category             string          `json:"Category"`
	Descriptions         string          `json:"Descriptions"`
	Tnc                  string          `json:"tnc"`
	ImportantInstruction string          `json:"importantInstruction"`
	RedeemSteps          string          `json:"redeemSteps"` // Store as raw message to be unmarshaled later
}

type VoucherCodeData struct {
	EndDate       string `json:"EndDate"`
	Value         string `json:"Value"`
	VoucherGCcode string `json:"VoucherGCcode"`
	VoucherGuid   string `json:"VoucherGuid"`
	VoucherNo     string `json:"VoucherNo"`
	Voucherpin    string `json:"Voucherpin"`
}

type PullVoucher struct {
	ProductGuid string            `json:"ProductGuid"`
	ProductName string            `json:"ProductName"`
	VoucherName string            `json:"VoucherName"`
	Vouchers    []VoucherCodeData `json:"Vouchers"`
}

type PullVoucherResponse struct {
	ErrorCode          string        `json:"ErrorCode"`
	ErrorMessage       string        `json:"ErrorMessage"`
	ExternalOrderIdOut string        `json:"ExternalOrderIdOut"`
	BrandProductCode   string        `json:"BrandProductCode"`
	Message            string        `json:"Message"`
	PullVouchers       []PullVoucher `json:"PullVouchers"`
	ResultType         string        `json:"ResultType"`
}

type BoolString string

const (
	TrueBoolString  BoolString = "true"
	FalseBoolString BoolString = "false"
)

func (b BoolString) Bool() bool {
	switch b {
	case TrueBoolString:
		return true
	case FalseBoolString:
		return false
	default:
		return false
	}
}
