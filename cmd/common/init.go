package common

import (
	"context"

	"github.com/newrelic/go-agent/v3/newrelic"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"stashfin.com/stashfin/magneto/dtype"
)

type Application struct {
	Context   context.Context
	Cancel    context.CancelFunc
	Logger    *logrus.Entry
	Db        *gorm.DB
	AppConfig *dtype.AppConfig
	NrApp     *newrelic.Application
}

func InitializeApplication() (*Application, error) {
	ctx, cancel := context.WithCancel(context.Background())

	return &Application{
		Context: ctx,
		Cancel:  cancel,
	}, nil
}
