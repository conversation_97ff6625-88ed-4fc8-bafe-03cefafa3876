package config

import (
	"context"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"stashfin.com/stashfin/magneto/dtype"
)

var sqsClient *SQSClient
var configInstance aws.Config

func GetSQSClient() *SQSClient {
	return sqsClient
}

func InitializeAWSClients(ctx context.Context, conf *dtype.AppConfig) (err error) {
	configInstance, err = config.LoadDefaultConfig(ctx, config.WithRegion(conf.AWSRegion), config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(conf.AWSAccessKeyID, conf.AWSSecretAccessKey, "")))
	if err != nil {
		return err
	}

	sqsClient = NewSQSClient(&configInstance, conf.RewardServiceAWSQueueUrl)
	return err
}
func GetAWSConfig() aws.Config {
	return configInstance
}
