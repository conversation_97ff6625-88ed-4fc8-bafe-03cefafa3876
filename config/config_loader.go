//go:build !coverage

package config

import (
	"fmt"

	"github.com/knadh/koanf"
	"github.com/knadh/koanf/parsers/toml"
	"github.com/knadh/koanf/providers/file"
	"stashfin.com/stashfin/magneto/dtype"
)

var appConfig dtype.AppConfig

func InitializeAppConfig() (*dtype.AppConfig, error) {
	config := koanf.New(".")
	parser := toml.Parser()
	config.Load(file.Provider(fmt.Sprintf("credentials.toml")), parser)
	if err := config.UnmarshalWithConf("", &appConfig, koanf.UnmarshalConf{Tag: "koanf", FlatPaths: true}); err != nil {
		return nil, err
	}

	return &appConfig, nil
}

func GetConfigValues() *dtype.AppConfig {
	return &appConfig
}
