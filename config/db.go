package config

import (
	"context"
	"fmt"
	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"stashfin.com/stashfin/magneto/dtype"
)

var (
	gDB            *gorm.DB
	gDBInitialized bool = false
)

func GetDBConnection() *gorm.DB {
	return gDB
}

// only for testing. Not to be used in live environment.
func SetDBConnection(db *gorm.DB) {
	if !gDBInitialized {
		gDB = db
	}
}

// In db.go

func InitializeDatabase(ctx context.Context, config *dtype.AppConfig) (*gorm.DB, error) {
	// Add a timeout for the database connection
	dbCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	dbUsername := config.DBUsername
	dbPassword := config.DBPassword
	dbHost := config.DBHost
	dbPort := config.DBPort
	dbName := config.DBName

	connString := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true&loc=Local", dbUsername, dbPassword, dbHost, dbPort, dbName)

	// Use a retry mechanism for establishing the connection
	var db *gorm.DB
	var err error
	backoff := 500 * time.Millisecond
	maxRetries := 3

	for i := 0; i < maxRetries; i++ {
		select {
		case <-dbCtx.Done():
			return nil, fmt.Errorf("database connection context canceled: %w", dbCtx.Err())
		default:
			db, err = gorm.Open(mysql.Open(connString), &gorm.Config{})
			if err == nil {
				break
			}

			fmt.Printf("Failed to connect to database (attempt %d/%d): %v. Retrying in %v...\n",
				i+1, maxRetries, err, backoff)

			// Wait before retrying
			time.Sleep(backoff)
			backoff *= 2 // Exponential backoff
		}
	}

	if err != nil {
		return nil, fmt.Errorf("failed to connect to database after %d attempts: %w", maxRetries, err)
	}

	// printing all sql queries for non prod env
	if config.Environment != dtype.ProdEnv {
		db = db.Debug()
	}

	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	sqlDB.SetMaxIdleConns(config.DBMaxPools)
	sqlDB.SetMaxOpenConns(config.DBMaxPools)
	sqlDB.SetConnMaxIdleTime(time.Duration(config.DBMaxIdle * 1000_000_000))
	gDB = db
	gDBInitialized = true
	return db, nil
}

func checkContext(ctx context.Context) {
	if err := ctx.Err(); err != nil {
		fmt.Printf("context is invalid: %", err)
	}
}

// Improved version of GetConnectionCtx
func GetConnectionCtx(ctx context.Context) *gorm.DB {
	checkContext(ctx)
	tx := ctx.Value(dtype.DBCON)
	var txn *gorm.DB

	if tx != nil {
		txn = tx.(*gorm.DB)
	} else {
		txn = GetDBConnection()
	}

	// Important: pass the context to the database query
	return txn.WithContext(ctx)
}
