package config

import (
	"github.com/newrelic/go-agent/v3/newrelic"

	"stashfin.com/stashfin/magneto/dtype"
)

func InitializeNewrelic(license string, appName string, env dtype.EnvType) (*newrelic.Application, error) {
	if license == "" {
		return nil, nil
	}

	return newrelic.NewApplication(
		newrelic.ConfigAppName(appName+"-"+env.String()),
		newrelic.ConfigLicense(license),
		newrelic.ConfigDistributedTracerEnabled(true),
	)
}
