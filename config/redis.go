package config

import (
	redis "github.com/redis/go-redis/v9"

	"stashfin.com/stashfin/magneto/dtype"
)

var rdb *redis.Client

func GetRedis() *redis.Client {
	return rdb
}

func InitializeRedis(appConfig *dtype.AppConfig) {
	if appConfig.RedisHost == "" {
		return
	}
	rdb = redis.NewClient(&redis.Options{
		Addr:     appConfig.RedisHost + ":6379",
		Password: appConfig.RedisPassword,
		DB:       appConfig.RedisDB,
	})
}
