package config

import (
	"log"
	"os"

	"github.com/evalphobia/logrus_sentry"
	"github.com/getsentry/sentry-go"
	"github.com/sirupsen/logrus"
	easy "github.com/t-tomalak/logrus-easy-formatter"

	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/util"
)

func InitializeSentry(config *dtype.AppConfig) {
	util.Log = logrus.New()
	util.Log.SetLevel(logrus.InfoLevel)
	util.Log.SetReportCaller(true)

	// Set the formatter to the easy formatter with a custom format string
	util.Log.SetFormatter(&easy.Formatter{
		LogFormat:       "%time%[%lvl%][%rid%] %msg%\n",
		TimestampFormat: "2006-01-02T15:04:05.000",
	})
	sentryDSN := os.Getenv("SENTRY_DSN")
	if sentryDSN == "" {
		sentryDSN = config.SentryDSN
	}

	if sentryDSN == "" {
		return
	}

	err := sentry.Init(sentry.ClientOptions{
		Environment:   config.Environment.String(),
		Dsn:           sentryDSN,
		MaxErrorDepth: 20,
	})
	if err != nil {
		log.Fatalf("sentry.Init: %s", err)
	}

	hook, err := logrus_sentry.NewSentryHook(sentryDSN, []logrus.Level{
		logrus.PanicLevel,
		logrus.FatalLevel,
		logrus.ErrorLevel,
	})

	hook.SetEnvironment(config.Environment.String())
	hook.StacktraceConfiguration.Enable = true
	hook.StacktraceConfiguration.IncludeErrorBreadcrumb = true
	hook.StacktraceConfiguration.Context = 5

	if err == nil {
		util.Log.Hooks.Add(hook)
	}
}
