package config

import (
	"context"
	"encoding/json"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
)

type SQSClient struct {
	SQSClient *sqs.Client
	SQSQueue  string
}

func NewSQSClient(cfg *aws.Config, queueUrl string) *SQSClient {
	client := sqs.NewFromConfig(*cfg)

	return &SQSClient{
		SQSClient: client,
		SQSQueue:  queueUrl,
	}
}

func (c *SQSClient) SendMessage(ctx context.Context, data string) error {
	if data == "" {
		return nil
	}

	_, err := c.SQSClient.SendMessage(
		ctx,
		&sqs.SendMessageInput{
			QueueUrl:    aws.String(c.SQSQueue),
			MessageBody: aws.String(data),
		},
	)
	if err != nil {
		return err
	}
	return nil
}

type messageType struct {
	EventName string `json:"event_name"`
	Origin   string `json:"origin"`
	Data     any    `json:"data"`
}

func (c *SQSClient) SendMessageStruct(ctx context.Context, data map[string]any, eventName string) error {
	res := &messageType{
		EventName: eventName,
		Origin:   "Magneto",
		Data:     data,
	}

	resJson, err := json.Marshal(res)
	if err != nil {
		return err
	}
	return c.SendMessage(ctx, string(resJson))
}
