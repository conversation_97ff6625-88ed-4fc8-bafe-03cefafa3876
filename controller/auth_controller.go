package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/auth"
	"stashfin.com/stashfin/magneto/util"
)

func AuthRequestOTP(c *gin.Context) {
	logger := util.CLog(c.Request)

	var req dtype.RequestOTP
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to parse the request params, err: %s", err)
		util.ErrorResponse("Invalid request struct.", c, http.StatusBadRequest)
		return
	}

	if err := helper.ValidateStruct(&req); err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusBadRequest)
		return
	}

	err := auth.GetAuthService().RequestOTP(c.Request.Context(), req.Phone, req.Register)
	if err != nil {
		logger.Errorf("Error while requsting otp for user: %d, err: %s", req.Phone, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("Otp sent successfully.", c)
}

func AuthLogin(c *gin.Context) {
	logger := util.CLog(c.Request)

	var req dtype.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to parse the request params, err: %s", err)
		util.ErrorResponse("Invalid request struct.", c, http.StatusBadRequest)
		return
	}

	if err := helper.ValidateStruct(&req); err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusBadRequest)
		return
	}

	response, err := auth.GetAuthService().Login(c.Request.Context(), &req)
	if err != nil {
		logger.Errorf("Error while verifing otp  for user: %d, err: %s", req.Phone, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(response, c)
}
