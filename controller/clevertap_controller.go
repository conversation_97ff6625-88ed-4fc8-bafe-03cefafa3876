package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/clevertap"
	"stashfin.com/stashfin/magneto/util"
)

func SendEventFromWeb(c *gin.Context) {
	logger := util.CLog(c.Request)
	customerID := util.GetCustomerID(c)

	var req dtype.ClevertapWebRequest

	if source := c.GetHeader("source"); source != "web" {
		logger.Error("Invalid request source: ", source)
		util.ErrorResponse("Invalid request source.", c, http.StatusBadRequest)
		return
	}
	req.Clevertap = true
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to parse the request params, Err: %s", err)
		util.ErrorResponse("Invalid request struct.", c, http.StatusBadRequest)
		return
	}

	err := clevertap.GetService().SendEvent(c.Request.Context(), customerID, req.EventName, req.EventProperty, req)
	if err != nil {
		logger.Errorf("Error while adding Clevertap Event To Redis Stream: %d, err %s", customerID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("success", c)
}
