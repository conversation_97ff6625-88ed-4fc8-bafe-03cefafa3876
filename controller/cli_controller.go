package controller

import (
	"github.com/gin-gonic/gin"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/cli"
	"stashfin.com/stashfin/magneto/util"
)

func SendOtp(c *gin.Context) {
	logger := util.CLog(c.Request)

	var req dtype.SendOtpRequest
	err := util.ValidateRequestBody(c, &req)
	if err != nil {
		return
	}

	err = cli.GetService().SendOtp(c.Request.Context(), req.Mobile)
	if err != nil {
		logger.Errorf("Error while requsting otp for user: %d, err: %s", req.Mobile, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(dtype.SendOtpResponse{Message: "OTP has been sent successfully"}, c)
}

func VerifyCli(c *gin.Context) {
	logger := util.CLog(c.Request)

	var req dtype.VerifyCliRequest
	err := util.ValidateRequestBody(c, &req)
	if err != nil {
		return
	}

	response, err := cli.GetService().VerifyCli(c.Request.Context(), req.Mobile, req.Otp)
	if err != nil {
		logger.Errorf("Error while verifing cli for user: %d, err: %s", req.Mobile, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponseWithData(message, c, statusCode, response)
		return
	}

	util.SuccessResponse(response, c)
}
