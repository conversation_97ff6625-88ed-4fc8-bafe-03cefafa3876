package controller

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/util"
)

func GetLeadDetail(c *gin.Context) {
	logger := util.CLog(c.Request)

	leadType := c.Default<PERSON>y("type", "")
	leadTypeInt, err := strconv.ParseInt(leadType, 10, 64)
	if err != nil {
		util.ErrorResponse("invalid lead type", c, http.StatusBadRequest)
		return
	}

	response, err := customer.GetCustomerService().GetLeadDetail(c.Request.Context(), util.GetCustomerID(c), leadTypeInt)
	if err != nil {
		logger.Errorf("Error while creating customer lead: %d, err %s", util.GetCustomerID(c), err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(response, c)
}

func CreateCustomerLead(c *gin.Context) {
	logger := util.CLog(c.Request)

	// Extract UTM query parameters
	utmSource := c.DefaultQuery("utm_source", "")
	utmMedium := c.DefaultQuery("utm_medium", "")
	utmCampaign := c.DefaultQuery("utm_campaign", "")

	var reqBody dtype.CreateCustomerLeadRequestBody
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		util.ErrorResponse("invalid input", c, http.StatusBadRequest)
		return
	}

	fields := map[string]interface{}{
		"lead_type_id": reqBody.Type,
		"name":         reqBody.Name,
		"email":        reqBody.Email,
		"customer_id":  util.GetCustomerID(c),
		"utm_source":   utmSource,
		"utm_medium":   utmMedium,
		"utm_campaign": utmCampaign,
	}

	err := customer.GetCustomerService().CreateCustomerLead(c.Request.Context(), util.GetCustomerID(c), fields)
	if err != nil {
		logger.Errorf("Error while creating customer lead: %d, err %s", util.GetCustomerID(c), err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("successful", c)
}
