package controller

import (
	"encoding/json"
	"io/ioutil"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/scanpay"
	"stashfin.com/stashfin/magneto/util"
)

func ICICIPaymentCallback(c *gin.Context) {
	logger := util.CLog(c.Request)

	body, err := ioutil.ReadAll(c.Request.Body)

	if err != nil {
		logger.Errorf("Error while reading callback %s", err.Error())
		c.<PERSON>(http.StatusBadRequest)
		return
	}
	logger.Infof("ICICI callback, %s\n", body)

	var encryptedReq map[string]string

	if err := json.Unmarshal([]byte(body), &encryptedReq); err != nil {
		logger.Errorf("Error while fetching body into struct, %s", err.Error())
		c.Status(http.StatusBadRequest)
		return
	}

	if encryptedReq["encryptedKey"] == "" || encryptedReq["encryptedData"] == "" {
		logger.Errorf("Encrypted key or Encrypted data is empty")
		c.Status(http.StatusBadRequest)
		return
	}

	decryptedRequest, err := helper.GetICICIDecryptedResponse(encryptedReq["encryptedData"],
		encryptedReq["encryptedKey"])
	if err != nil {
		logger.Errorf("Error while decrypt icici callback, key: %s, data: %s", encryptedReq["encryptedKey"],
			encryptedReq["encryptedData"])
		c.Status(http.StatusBadRequest)
		return
	}

	req, err := getCallbackResponse(decryptedRequest)
	if err != nil {
		logger.Errorf("Error while converting callback")
		c.Status(http.StatusBadRequest)
		return
	}

	err = scanpay.GetTransactionService().SaveICICIPaymentCallback(c.Request.Context(), req)
	if err != nil {
		logger.Errorf("Unable to Process ICICI Callback, Because: %s", err.Error())
		c.Status(http.StatusInternalServerError)
		return
	}

	logger.Infof("ICICI Callback Processed Successfully")
	c.Status(http.StatusOK)
}

func getCallbackResponse(req map[string]interface{}) (*dtype.ICICIPaymentCallbackRequest, error) {
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}

	var request dtype.ICICIPaymentCallbackRequest

	if err := json.Unmarshal(jsonData, &request); err != nil {
		return nil, err
	}

	validate := validator.New()
	validationError := validate.Struct(&request)
	if validationError != nil {
		return nil, validationError
	}

	return &request, nil
}
