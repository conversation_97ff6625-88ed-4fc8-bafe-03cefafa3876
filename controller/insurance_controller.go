package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/insurance"
	"stashfin.com/stashfin/magneto/util"
)

func InsuranceProductDetails(c *gin.Context) {
	logger := util.CLog(c.Request)
	uid := c.Param("uid")
	if !util.IsValidUID(uid) {
		util.ErrorResponse("Invalid insurance UID", c, http.StatusBadRequest)
		return
	}

	insuranceDetails, err := insurance.GetService().GetProductDetails(c.Request.Context(), uid)
	if err != nil {
		logger.Errorf("Error while fetching insurance details for UID: %s, err %s", uid, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(insuranceDetails, c)
}

func RequestOTP(c *gin.Context) {
	logger := util.CLog(c.Request)

	var req dtype.OTPRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to parse the request params, err: %s", err)
		util.ErrorResponse("Invalid request struct.", c, http.StatusBadRequest)
		return
	}

	if err := helper.ValidateStruct(&req); err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusBadRequest)
		return
	}

	err := insurance.GetService().RequestOTP(c.Request.Context(), req.Mobile)
	if err != nil {
		logger.Errorf("Error while requsting otp for user: %d, err: %s", req.Mobile, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("Otp sent successfully.", c)
}

func VerifyOTP(c *gin.Context) {
	logger := util.CLog(c.Request)

	// Extract UTM query parameters
	utmSource := c.DefaultQuery("utm_source", "")
	utmMedium := c.DefaultQuery("utm_medium", "")
	utmCampaign := c.DefaultQuery("utm_campaign", "")

	var req dtype.VerifyOTPRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to parse the request params, err: %s", err)
		util.ErrorResponse("Invalid request struct.", c, http.StatusBadRequest)
		return
	}

	if err := helper.ValidateStruct(&req); err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusBadRequest)
		return
	}

	response, err := insurance.GetService().VerifyOTP(c.Request.Context(), &req, utmSource, utmMedium, utmCampaign)
	if err != nil {
		logger.Errorf("Error while verifing otp for user: %d, err: %s", req.Mobile, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(response, c)
}

func CreateOrGetInsuranceApplication(c *gin.Context) {
	logger := util.CLog(c.Request)

	// Extract UTM query parameters
	utmSource := c.DefaultQuery("utm_source", "")
	utmMedium := c.DefaultQuery("utm_medium", "")
	utmCampaign := c.DefaultQuery("utm_campaign", "")

	var reqBody dtype.CreateInsuranceApplicationRequestBody
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		util.ErrorResponse("product_uid is missing or invalid", c, http.StatusBadRequest)
		return
	}

	insuranceApplicationDetails, err := insurance.GetService().CreateOrGetInsuranceApplication(
		c.Request.Context(),
		reqBody.ProductUID,
		util.GetCustomerID(c),
		utmSource,
		utmMedium,
		utmCampaign,
	)
	if err != nil {
		logger.Errorf("Error while getting insurance application for product uid: %s, err %s", reqBody.ProductUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(insuranceApplicationDetails, c)
}

func SaveApplicationDetails(c *gin.Context) {
	logger := util.CLog(c.Request)

	applicationUID := c.Param("application_uid")
	if !util.IsValidUID(applicationUID) {
		util.ErrorResponse("Invalid Application UID", c, http.StatusBadRequest)
		return
	}

	var reqBody dtype.SaveApplicationDetailsRequestBody
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		util.ErrorResponse("Invalid Request Body", c, http.StatusBadRequest)
		return
	}

	if err := helper.ValidateStruct(&reqBody); err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusBadRequest)
		return
	}

	paymentLinkResponse, err := insurance.GetService().SaveApplicationAndGeneratePaymentLink(
		c.Request.Context(),
		applicationUID,
		util.GetCustomerID(c),
		reqBody,
	)
	if err != nil {
		logger.Errorf("Error while saving data and generating payment link for application UID: %s, err %s", applicationUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(paymentLinkResponse, c)
}

func CreateSuccessApplication(c *gin.Context) error {
	logger := util.CLog(c.Request)

	// Extract UTM query parameters
	utmSource := c.DefaultQuery("utm_source", "internal_application")
	utmMedium := c.DefaultQuery("utm_medium", "internal_application")
	utmCampaign := c.DefaultQuery("utm_campaign", "internal_application")

	var requestBody dtype.InternalInsuranceApplicationRequestBody
	if err := c.ShouldBindJSON(&requestBody); err != nil {
		util.ErrorResponse("Invalid Request Body", c, http.StatusBadRequest)
		return err
	}

	// isEligible and isRegister are always true as the customer is already resgister and eligible
	_, err := insurance.GetService().CreateInternalSuccessApplication(c.Request.Context(), requestBody.ProductUID, requestBody.CustomerID, utmSource, utmMedium, utmCampaign, true, true)
	if err != nil {
		logger.Errorf("Error creating insurance internal success application lead: %s", err)
		util.ErrorResponse("Error creating insurance internal success application lead", c, http.StatusInternalServerError)
		return err
	}

	util.SuccessResponse("Success", c)
	return nil
}

func GetInsurancePaymentStatus(c *gin.Context) {
	logger := util.CLog(c.Request)

	applicationUID := c.Param("application_uid")
	if !util.IsValidUID(applicationUID) {
		util.ErrorResponse("Invalid Application UID", c, http.StatusBadRequest)
		return
	}

	paymentStatusResponse, err := insurance.GetService().GetPaymentStatus(
		c.Request.Context(),
		applicationUID,
		util.GetCustomerID(c),
	)
	if err != nil {
		logger.Errorf("Error while fetching payment status for application uid: %s, err %s", applicationUID, err)
		util.ErrorResponse("Request Failed", c, http.StatusInternalServerError)
		return
	}

	util.SuccessResponse(paymentStatusResponse, c)
}

func GetRetryPaymentLink(c *gin.Context) {
	logger := util.CLog(c.Request)

	applicationUID := c.Param("application_uid")
	if !util.IsValidUID(applicationUID) {
		util.ErrorResponse("Invalid Application UID", c, http.StatusBadRequest)
		return
	}

	paymentStatusResponse, err := insurance.GetService().GetRetryPaymentLink(
		c.Request.Context(),
		applicationUID,
		util.GetCustomerID(c),
	)

	if err != nil {
		logger.Errorf("Error while generating retry paymnet link for application uid: %s, err %s", applicationUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(paymentStatusResponse, c)
}

func GetFeedbackQuestion(c *gin.Context) {
	logger := util.CLog(c.Request)

	applicationUID := c.Param("application_uid")
	if !util.IsValidUID(applicationUID) {
		util.ErrorResponse("Invalid Application UID", c, http.StatusBadRequest)
		return
	}

	res, err := insurance.GetService().GetFeedbackQuestion(
		c.Request.Context(),
		applicationUID,
		util.GetCustomerID(c),
	)

	if err != nil {
		logger.Errorf("Error while getting feeback questions for application uid: %s, err %s", applicationUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(res, c)
}

func InsurancePostPurchase(c *gin.Context) {
	logger := util.CLog(c.Request)

	applicationUID := c.Param("application_uid")
	if !util.IsValidUID(applicationUID) {
		util.ErrorResponse("Invalid Application UID", c, http.StatusBadRequest)
		return
	}

	err := insurance.GetService().PostPaymentSuccess(
		c.Request.Context(),
		applicationUID,
	)

	if err != nil {
		logger.Errorf("Error while doing insurance post purchase application uid: %s, err %s", applicationUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("success", c)
}

func SaveFeedbackResponse(c *gin.Context) {
	logger := util.CLog(c.Request)

	applicationUID := c.Param("application_uid")
	if !util.IsValidUID(applicationUID) {
		util.ErrorResponse("Invalid Application UID", c, http.StatusBadRequest)
		return
	}

	var req dtype.SaveFeedbackRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ErrorResponse("Invalid Request Body", c, http.StatusBadRequest)
		return
	}

	if err := helper.ValidateStruct(&req); err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusBadRequest)
		return
	}

	err := insurance.GetService().SaveFeedbackResponse(
		c.Request.Context(),
		applicationUID,
		util.GetCustomerID(c),
		req.QuestionID,
		req.Response,
		req.Remarks,
	)

	if err != nil {
		logger.Errorf("Error while saving feedback response for application uid: %s, err %s", applicationUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("Successful", c)
}

func CreateOrGetInsuranceApplicationV2(c *gin.Context) {
	logger := util.CLog(c.Request)

	// Extract UTM query parameters
	utmSource := c.DefaultQuery("utm_source", "")
	utmMedium := c.DefaultQuery("utm_medium", "")
	utmCampaign := c.DefaultQuery("utm_campaign", "")

	var reqBody dtype.CreateInsuranceApplicationRequestBody
	if err := c.ShouldBindJSON(&reqBody); err != nil {
		util.ErrorResponse("product_uid is missing or invalid", c, http.StatusBadRequest)
		return
	}

	insuranceApplicationDetails, err := insurance.GetService().CreateOrGetGenericApplication(
		c.Request.Context(),
		reqBody.ProductUID,
		util.GetCustomerID(c),
		utmSource,
		utmMedium,
		utmCampaign,
	)
	if err != nil {
		logger.Errorf("Error while getting insurance application for product uid: %s, err %s", reqBody.ProductUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(insuranceApplicationDetails, c)
}

func GetProductUpdatedInfo(c *gin.Context) {
	logger := util.CLog(c.Request)
	productUID := c.Param("uid")
	var req map[string]interface{}

	if err := c.ShouldBindJSON(&req); err != nil {
		util.ErrorResponse("Invalid Request Body", c, http.StatusBadRequest)
		return
	}

	res, err := insurance.GetService().GetProductUpdatedInfo(c.Request.Context(), productUID, req)

	if err != nil {
		logger.Errorf("Error while getting product updated-info for product-uid: %s, err %s", productUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(res, c)
}

func GetInsuranceHomePageData(c *gin.Context) {
	logger := util.CLog(c.Request)

	res, err := insurance.GetService().GetInsuranceHomePage(c.Request.Context())
	if err != nil {
		logger.Errorf("Error while getting insurance Hoge Page err %s", err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(res, c)
}

func GetInsuranceSubCatagories(c *gin.Context) {
	logger := util.CLog(c.Request)
	parentUID := c.Param("uid")

	if !util.IsValidUID(parentUID) {
		util.ErrorResponse("Invalid UID", c, http.StatusBadRequest)
		return
	}

	res, err := insurance.GetService().GetInsuranceSubCatagory(c.Request.Context(), parentUID)
	if err != nil {
		logger.Errorf("Error while getting insurance sub catagory for parentUID: %s, err %s", parentUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(res, c)
}

func GetInsuranceListingPageData(c *gin.Context) {
	logger := util.CLog(c.Request)
	parentUID := c.Param("uid")

	if !util.IsValidUID(parentUID) {
		util.ErrorResponse("Invalid UID", c, http.StatusBadRequest)
		return
	}

	res, err := insurance.GetService().GetInsuranceLisitingData(c.Request.Context(), parentUID)
	if err != nil {
		logger.Errorf("Error while getting insurance listing page for parentUID: %s, err %s", parentUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(res, c)
}

func GetInsuranceBanner(c *gin.Context) {
	logger := util.CLog(c.Request)

	res, err := insurance.GetService().GetInsuranceBanner(c.Request.Context())
	if err != nil {
		logger.Errorf("Error while getting insurance banner err %s", err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(res, c)
}

func SaveExternalApplicationDetail(c *gin.Context) {
	logger := util.CLog(c.Request)

	var req dtype.ExternalApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ErrorResponse("Invalid Request Body", c, http.StatusBadRequest)
		return
	}

	if err := helper.ValidateStruct(&req); err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusBadRequest)
		return
	}

	err := insurance.GetService().SaveExternalApplication(c.Request.Context(), req)
	if err != nil {
		logger.Errorf("Error while getting insurance banner err %s", err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("Succeess", c)
}
