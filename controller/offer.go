package controller

import (
	"bytes"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"

	offerDtype "stashfin.com/stashfin/magneto/api/offer"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/offer"
	"stashfin.com/stashfin/magneto/util"

	wkhtml "github.com/SebastiaanKlippert/go-wkhtmltopdf"
	"github.com/tdewolff/minify/v2"
	"github.com/tdewolff/minify/v2/html"
)

func GetOfferListingPage(c *gin.Context) {
	logger := util.CLog(c.Request)

	response, err := offer.GetService().GetOfferListingPage(c.Request.Context(), util.GetCustomerID(c))
	if err != nil {
		logger.Errorf("Error while requsting offer listing page, err: %s", err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(response, c)
}

func GetOfferDetails(c *gin.Context) {
	logger := util.CLog(c.Request)
	uid := c.Param("uid") // offer detail UID
	if !util.IsValidUID(uid) {
		util.ErrorResponse("Invalid offer product UID", c, http.StatusBadRequest)
		return
	}

	offerDetails, err := offer.GetService().GetOfferDetail(c.Request.Context(), uid)
	if err != nil {
		logger.Errorf("Error while fetching offer details for UID: %s, err %s", uid, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}
	util.SuccessResponse(offerDetails, c)
}

func UpdateCart(c *gin.Context) error {
	logger := util.CLog(c.Request)

	var req offerDtype.SKUPayload
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to parse the request params, err: %s", err)
		util.ErrorResponse("Invalid request struct.", c, http.StatusBadRequest)
		return err
	}
	if err := helper.ValidateStruct(&req); err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusBadRequest)
		return err
	}
	err := offer.GetService().UpdateCart(c.Request.Context(), util.GetCustomerID(c), req)
	if err != nil {
		logger.Errorf("Error while updating cart,payload : %+v,  err %s", req, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return err
	}
	util.SuccessResponse("success", c)
	return nil
}

func GetPaymentLink(c *gin.Context) error {
	logger := util.CLog(c.Request)
	offerDiscount := c.Query("offerDiscount")
	paymentLink, err := offer.GetService().GetPaymentLink(c.Request.Context(), util.GetCustomerID(c), offerDiscount)
	if err != nil {
		logger.Errorf("Error while getting payment link for offer for CustomerId: %d, err %s", util.GetCustomerID(c), err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		if statusCode != http.StatusInternalServerError {
			return nil
		}
		return err
	}
	util.SuccessResponse(paymentLink, c)
	return nil
}

func GetUserCart(c *gin.Context) {
	logger := util.CLog(c.Request)

	response, err := offer.GetService().GetUserOfferCart(c.Request.Context(), util.GetCustomerID(c))
	if err != nil {
		logger.Errorf("Error while getting user offer cart for CustomerId: %d, err %s", util.GetCustomerID(c), err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}
	util.SuccessResponse(response, c)
}

func GetUserOrdersHistory(c *gin.Context) {
	logger := util.CLog(c.Request)

	response, err := offer.GetService().GetOrdersHistory(c.Request.Context(), util.GetCustomerID(c))
	if err != nil {
		logger.Errorf("Error while getting user offer order history for CustomerId: %d, err %s", util.GetCustomerID(c), err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}
	util.SuccessResponse(response, c)
}

func GetOrderPaymentStatus(c *gin.Context) {
	logger := util.CLog(c.Request)

	orderUID := c.Param("order_uid") // offer order UID
	if !util.IsValidUID(orderUID) {
		util.ErrorResponse("Invalid offer order UID", c, http.StatusBadRequest)
		return
	}

	response, err := offer.GetService().GetOrderStatus(c.Request.Context(), util.GetCustomerID(c), orderUID)
	if err != nil {
		logger.Errorf("Error while getting offer order payment status for CustomerId: %d, orderUID: %s , err %s", util.GetCustomerID(c), orderUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}
	util.SuccessResponse(response, c)
}

func FetchProducts(c *gin.Context) {
	logger := util.CLog(c.Request)

	err := offer.GetService().FetchProducts(c.Request.Context())
	if err != nil {
		logger.Errorf("Error while fetching offer products, err %s", err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}
	util.SuccessResponse("success", c)
}

func LocDiscount(c *gin.Context) error {
	logger := util.CLog(c.Request)

	err := offer.GetService().LocDiscount(c.Request.Context(), util.GetCustomerID(c))
	if err != nil {
		logger.Errorf("Error while getting user offer order history for CustomerId: %d, err %s", util.GetCustomerID(c), err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return err
	}
	util.SuccessResponse("success", c)
	return nil
}

func FetchOrdersCouponCodes(c *gin.Context) {
	logger := util.CLog(c.Request)
	orderIds := c.Query("orderIDs")
	orderIdsArr := strings.Split(orderIds, ",")
	resp := dtype.FetchOrdersCouponCodesResponse{
		OrdersData: []dtype.OrdersData{},
	}
	for _, orderID := range orderIdsArr {
		msg, err := offer.GetService().FetchCouponCodes(c.Request.Context(), orderID)
		if err != nil {
			logger.Errorf("Error while fetching coupon codes for orderID: %s, err %s", orderID, err)
		}
		resp.OrdersData = append(resp.OrdersData, dtype.OrdersData{OrderID: orderID, Msg: msg})
	}
	util.SuccessResponse(resp, c)
}

func Getcheckokyc(c *gin.Context) error {
	logger := util.CLog(c.Request)

	paymentLink, err := offer.GetService().Getcheckokyc(c.Request.Context(), util.GetCustomerID(c))
	if err != nil {
		logger.Errorf("Error while getting payment link for offer for CustomerId: %d, err %s", util.GetCustomerID(c), err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		if statusCode != http.StatusInternalServerError {
			return nil
		}
		return err
	}
	util.SuccessResponse(paymentLink, c)
	return nil
}

func Getpaymentuid(c *gin.Context) error {
	logger := util.CLog(c.Request)
	customer_id := c.Query("customer_id")

	offerDiscount := c.Query("offerDiscount")
	customer_id_int64, err := strconv.ParseInt(customer_id, 10, 64)
	paymentLink, err := offer.GetService().Getpaymentuid(c.Request.Context(), customer_id_int64, offerDiscount)
	if err != nil {
		logger.Errorf("Error while getting payment link for offer for CustomerId: %d, err %s", customer_id, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		if statusCode != http.StatusInternalServerError {
			return nil
		}
		return err
	}
	util.SuccessResponse(paymentLink, c)
	return nil

}

type PDFRequest struct {
	HTML      string `json:"html"`
	LoanID    string `json:"loan_id"`
	Dir       string `json:"dir"`
	FileName  string `json:"file_name"`
	Extintion string `json:"extintion"`
}

// Gin handler to generate PDF and return it
func Htmlpdf(c *gin.Context) error {
	var pdfRequest PDFRequest

	// Parse the JSON request
	if err := c.ShouldBindJSON(&pdfRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid JSON"})
		return err
	}

	// Create a new PDF generator
	pdfg, err := wkhtml.NewPDFGenerator()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create PDF generator"})
		return err
	}

	minifiedHTML, err := minifyHTML(pdfRequest.HTML)
	if err != nil {
		return err
	} // Assume you implement this function

	page := wkhtml.NewPageReader(strings.NewReader(minifiedHTML))

	// Configure the page
	pdfg.PageSize.Set(wkhtml.PageSizeA4) // Set page size

	// Optional: Add any other configuration for the page
	// For example, you could add margins if desired
	// Reduce DPI for lower quality
	pdfg.PageSize.Set(wkhtml.PageSizeA4)
	// pdfg.ImageQuality.Set(75) // Set image quality (1-100, lower means more compression)

	pdfg.MarginTop.Set(10)
	pdfg.MarginBottom.Set(10)
	pdfg.MarginLeft.Set(10)
	pdfg.MarginRight.Set(10)

	// Add the configured page to the PDF generator
	pdfg.AddPage(page)

	// Create the PDF
	err = pdfg.Create()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate PDF"})
		return err
	}

	// Set the filename from the request
	fileName := pdfRequest.FileName
	if fileName == "" {
		fileName = "output.pdf"
	} else {
		fileName += "." + pdfRequest.Extintion
	}

	// Set the HTTP response headers to serve the PDF as an attachment
	c.Writer.Header().Set("Content-Type", "application/pdf")
	c.Writer.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))
	c.Status(http.StatusOK)

	// Write the generated PDF to the response
	_, err = c.Writer.Write(pdfg.Bytes())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send PDF"})
	}
	return nil
}

func minifyHTML(input string) (string, error) {
	// Create a new minifier
	m := minify.New()
	m.AddFunc("text/html", html.Minify)

	var buf bytes.Buffer
	err := m.Minify("text/html", &buf, bytes.NewBufferString(input))
	if err != nil {
		return "", err
	}

	return buf.String(), nil
}
