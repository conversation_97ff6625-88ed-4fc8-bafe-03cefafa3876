package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"

	orderSystemDT "stashfin.com/stashfin/magneto/api/ordersystem"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/ordersystem"
	"stashfin.com/stashfin/magneto/util"
)

func OrderPostPurchase(c *gin.Context) {
	logger := util.CLog(c.Request)
	var req orderSystemDT.PostPurchase

	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Errorf("Failed to parse the request params, err: %s", err)
		util.ErrorResponse("Invalid request struct.", c, http.StatusBadRequest)
		return
	}
	if err := helper.ValidateStruct(&req); err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusBadRequest)
		return
	}

	logger.Infof("Processing post order for order UID: %s, status: %d", req.OrderUID, req.Status)

	err := ordersystem.GetService().ProcessPostOrder(c.Request.Context(), req.OrderUID, req.Status, req.PaymentID)
	if err != nil {
		logger.Errorf("Error while processing post order for order UID: %s, err: %s", req.OrderUID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}
}

func OrdersCheckTimeout(c *gin.Context) {
	logger := util.CLog(c.Request)
	err := ordersystem.GetService().OrdersCheckTimeout(c.Request.Context())
	if err != nil {
		logger.Errorf("Error while updating orders timeout, er: %s", err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}
	util.SuccessResponse("success", c)
}
