package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/restructure"
	"stashfin.com/stashfin/magneto/util"
)

func GetRestructureDetails(c *gin.Context) {
	logger := util.CLog(c.Request)
	customerID := util.GetCustomerID(c)

	res, err := restructure.GetService().GetDetails(c.Request.Context(), customerID)
	if err != nil {
		logger.Errorf("Error while getting restructure details for customer: %d, err %s", customerID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(res, c)
}

func RestructureLoanSendOTP(c *gin.Context) {
	logger := util.CLog(c.Request)
	customerID := util.GetCustomerID(c)

	err := restructure.GetService().SendOtp(c.Request.Context(), customerID)
	if err != nil {
		logger.Errorf("Error while sending otp for customer: %d, err %s", customerID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("Successful", c)
}

func VerifyRestructure(c *gin.Context) {
	logger := util.CLog(c.Request)
	customerID := util.GetCustomerID(c)

	var req dtype.RestructureLoanVerifyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		util.ErrorResponse("Invalid Request Body", c, http.StatusBadRequest)
		return
	}

	if err := helper.ValidateStruct(&req); err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusBadRequest)
		return
	}

	err := restructure.GetService().VerifyRestructure(c.Request.Context(), customerID, req.OTP)
	if err != nil {
		logger.Errorf("Error while verifing restructure loan customer: %d, err %s", customerID, err)
		message, statusCode := helper.FormatErr(err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("Your Loan Replanning Application is in process. We will share the update within 48 hours.", c)
}
