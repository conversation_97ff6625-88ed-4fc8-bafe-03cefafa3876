package controller

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/auth"
	"stashfin.com/stashfin/magneto/pkg/scanpay"
	"stashfin.com/stashfin/magneto/util"
)

func SaveAccountDetails(c *gin.Context) {
	logger := util.CLog(c.Request)
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	var accountData dtype.Account
	err = util.ValidateRequestBody(c, &accountData)
	if err != nil {
		return
	}

	err = scanpay.GetBankAccountDetailService().Save(c.Request.Context(),
		&accountData, customerId)
	if err != nil {
		message, statusCode := helper.FormatErr(err)
		logger.Errorf("Error while saving bank account for customer: %d and Account Reference number: %s, err %s",
			customerId, accountData.AccRef, err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("Bank Details Added Successfully", c)
}

func SaveUpiStatusDetails(c *gin.Context) {
	logger := util.CLog(c.Request)
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	var upiStatusDetail dtype.UpiStatusDetail
	err = util.ValidateRequestBody(c, &upiStatusDetail)
	if err != nil {
		return
	}

	err = scanpay.GetBankAccountDetailService().SaveUpiStatus(c.Request.Context(), customerId, &upiStatusDetail)
	if err != nil {
		message, statusCode := helper.FormatErr(err)
		logger.Errorf("Error while saving customer upi details for customer: %d and profile id: %s, err %s",
			customerId, upiStatusDetail.ProfileId, err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("UPI Status Added Successfully", c)
}

func GetBankAccountDetails(c *gin.Context) {
	logger := util.CLog(c.Request)
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	var accounts dtype.Accounts
	err = scanpay.GetBankAccountDetailService().Get(c.Request.Context(), &accounts, customerId)
	if err != nil {
		message, statusCode := helper.FormatErr(err)
		logger.Errorf("Error while fetching account details for customer: %d, err %s", customerId, err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(accounts, c)
}

func GetTransHistory(c *gin.Context) {
	logger := util.CLog(c.Request)
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	err, page := util.GetIntParam(c, "page_no", 1)
	if err != nil {
		return
	}

	err, limit := util.GetIntParam(c, "page_size", 10)
	if err != nil {
		return
	}

	var transactions dtype.Transactions
	err = scanpay.GetTransactionService().Get(c.Request.Context(), customerId, page, limit,
		&transactions)
	if err != nil {
		message, statusCode := helper.FormatErr(err)
		logger.Errorf("Error while getting transaction history for customer: %d, err %s", customerId, err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(transactions, c)
}

func SaveTransaction(c *gin.Context) {
	logger := util.CLog(c.Request)
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	var transaction dtype.Transaction
	err = util.ValidateRequestBody(c, &transaction)
	if err != nil {
		return
	}

	err = scanpay.GetTransactionService().Save(c.Request.Context(), customerId, &transaction)
	if err != nil {
		message, statusCode := helper.FormatErr(err)
		logger.Errorf("Error while saving transaction for customer: %d and transaction sequence number: %s, err %s",
			customerId, transaction.TransSeqNo, err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("Transaction Saved Successfully", c)
}

func RemoveBankAccounts(c *gin.Context) {
	logger := util.CLog(c.Request)
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	accRefIdsString := c.Query("acc_ref_ids")
	if accRefIdsString == "" {
		util.ErrorResponse("Please enter acc_ref_ids", c, http.StatusBadRequest)
		return
	}

	accRefIds := strings.Split(accRefIdsString, ",")
	err = scanpay.GetBankAccountDetailService().RemoveBankAccounts(c.Request.Context(), accRefIds)
	if err != nil {
		message, statusCode := helper.FormatErr(err)
		logger.Errorf("Error while removeing bank account for customer: %d and account ref ids: %s, err %s",
			customerId, accRefIdsString, err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse("Bank Accounts removed successfully", c)
}

func GetComplaintReasonCodes(c *gin.Context) {
	logger := util.CLog(c.Request)
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	response, err := scanpay.GetScanpayComplaintService().GetComplaintReasonCodes(c.Request.Context())
	if err != nil {
		message, statusCode := helper.FormatErr(err)
		logger.Errorf("Error while getting complaint reason codes for customer: %d, err %s",
			customerId, err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(response, c)
}

func RaiseTransactionComplaint(c *gin.Context) {
	logger := util.CLog(c.Request)
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	var raiseTransactionComplaint dtype.RaiseTransactionComplaint

	err = util.ValidateRequestBody(c, &raiseTransactionComplaint)
	if err != nil {
		return
	}

	response, err := scanpay.GetScanpayComplaintService().RaiseTransactionComplaint(c.Request.Context(),
		customerId, &raiseTransactionComplaint)
	if err != nil {
		message, statusCode := helper.FormatErr(err)
		logger.Errorf("Error while raising a complaint for customer: %d and seq no: %s, err %s",
			customerId, raiseTransactionComplaint.OriSeqNo, err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(response, c)
}

func GetComplaintStatus(c *gin.Context) {
	logger := util.CLog(c.Request)
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	deviceId := c.GetHeader("device_id")
	var getComplaintStatus dtype.GetComplaintStatus

	err = util.ValidateRequestBody(c, &getComplaintStatus)
	if err != nil {
		return
	}

	response, err := scanpay.GetScanpayComplaintService().CheckComplaintStatus(c.Request.Context(),
		customerId, deviceId, &getComplaintStatus)
	if err != nil {
		message, statusCode := helper.FormatErr(err)
		logger.Errorf("Error while getting complaint status for customer: %d and seq no: %s, err %s",
			customerId, getComplaintStatus.OriSeqNo, err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	util.SuccessResponse(response, c)
}

func CheckTransactionStatus(c *gin.Context) {
	logger := util.CLog(c.Request)
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	deviceId := c.GetHeader("device_id")
	var req dtype.CheckTransactionStatus

	err = util.ValidateRequestBody(c, &req)
	if err != nil {
		return
	}

	status, err := scanpay.GetTransactionService().CheckPendingTransactionStatus(c.Request.Context(), customerId,
		req.OriSeqNo, deviceId)
	if err != nil {
		message, statusCode := helper.FormatErr(err)
		logger.Errorf("Error while checking transaction status for customer: %d, seq no: %s, err %s",
			customerId, req.OriSeqNo, err)
		util.ErrorResponse(message, c, statusCode)
		return
	}

	response := map[string]string{
		"status": status,
	}

	util.SuccessResponse(response, c)
}
