package controller

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"stashfin.com/stashfin/magneto/pkg/auth"
	"stashfin.com/stashfin/magneto/pkg/bigcity"
	"stashfin.com/stashfin/magneto/util"
)

func CreateBigcityVoucher(c *gin.Context) {
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	err, response := bigcity.GetBigcityVoucherService().CreateBigCityVoucher(
		c.Request.Context(), customerId)
	if err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusAccepted)
		return
	}

	util.SuccessResponse(response, c)
	return
}

func GetVoucherHistory(c *gin.Context) {
	err, customerId := auth.GetAuthService().ValidateCustomerLogin(c)
	if err != nil {
		return
	}

	err, page := util.GetIntParam(c, "page_no", 1)
	if err != nil {
		return
	}

	err, limit := util.GetIntParam(c, "page_size", 10)
	if err != nil {
		return
	}

	var statusTypes []string
	_, hasParam := c.GetQuery("status")
	if hasParam {
		status := c.Query("status")
		if !(status == "active" || status == "inactive") {
			util.ErrorResponse("type param should be active/inactive", c, http.StatusBadRequest)
			return
		}
		statusTypes = append(statusTypes, status)
	} else {
		statusTypes = append(statusTypes, "active")
		statusTypes = append(statusTypes, "inactive")
	}

	err, voucherHistories := bigcity.GetBigcityVoucherService().GetVoucherHistory(
		c.Request.Context(), customerId, page, limit, statusTypes)
	if err != nil {
		util.ErrorResponse(err.Error(), c, http.StatusAccepted)
		return
	}

	util.SuccessResponse(voucherHistories, c)
	return
}
