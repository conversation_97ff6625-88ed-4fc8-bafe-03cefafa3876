env="local"
cli_enable=false
service_secret_key="bEYPrHmqaODqJUTY1aRN2ys7N5YK3Ctj"
insurance_partner_api_call_timeout_seconds=30
restructure_loan_enabled=true
insurance_cashfree_gateway_product_uids=["KECU4F"]

[app]
name="magneto"
web_base_url = "https://dev.stashfin.com"
encryption_key = "naU4kwi81NKrtZglSW+QwjbbChYA/cnrWLVGoY6mZfI="

[db]
name="sttash_website_LIVE"
max_pools=5
max_idle=300
host="dev-db-cluster.cluster-c1z93jsyca9u.ap-south-1.rds.amazonaws.com"
username="apps"
password="3N3tP4gtFm7J7R1TkZfaJYSs"
port=3306

[sentry]
dsn="https://<EMAIL>/39"

[redis]
password=""
host="127.0.0.1"
db=3

[bigcity]
user_auth_url="https://api.stashfin.bigcityvoucher.co.in/v1/customer/auth"
order_create_url="https://api.stashfin.bigcityvoucher.co.in/v1/stashfin/redeemption"
api_key="29662ad5d4ee31d21af0ea3f6d516a0f"
username="stashfin"
secret="afce48362d5195ee0a37e1aa18f95931"
voucher_eligible_stashcash=2000
redeemption_url="https://redemption.stashfin.bigcityvoucher.co.in/signin"

[apiv2]
base_url="https://devapi.stashfin.com/v2/api"
secret_key="c67a0844433cd8f543373638fe9372835f0131935e3b4936a3a393aa19aa0bd6"
app_version="255"

[paymentservice]
url="http://payment-service.staging/internal"

[newrelic]
key=""

[icici]
private_key_path="config/stashfin_private.key"
public_key_path="config/UAT_Public_cert.txt"
base_url="https://apibankingonesandbox.icicibank.com"
api_key="********************************"
check_status_api_key="IPEYQiScqN5MqAeGaCxigXTb1dOlRBiG"

[scanpay]
pending_transaction_sync_size=100
complaint_reason_codes_cron_enable=false

[cron]
enable=false

[oneassist]
base_url="https://uat2.1atesting.in"
secret_key="Basic d3N0ZXN0Om9hc3lz"

[offer]
payment_gateway=1
loc_coupon_code="NEWLOC10"
cart_total_discounted_amount_limit=5000

[vouchergram]
base_url="https://send.bulkgv.net"
key="6d66fb7debfd15bf716bb14752b9603b"
iv_key="716bb14752b9603b"
username="ZVBPNPCHVMBUAQTZYOWPLTXVWXWYERDS"
password="]soLj$si!x6IL![KP~rkQ^sXG^hT3yJS"

[aws]
region="ap-south-1"
access_key_id = "********************"
secret_access_key = "ZB58Jydm72pFXGc9+yvpUuB5R77bm46ZKLinFgh2"
reward_service_queue_url = "https://sqs.ap-south-1.amazonaws.com/************/dev-rewards-service"