package crons

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/scanpay"
	"stashfin.com/stashfin/magneto/util"
)

func UpdatePendingTransactions() {
	ctx := context.Background()

	scanpay.GetTransactionService().UpdatePendingTransStatus(ctx)
}

func SaveComplaintReasonCodes() {

	if !config.GetConfigValues().ComplaintReasonCodesCronEnable {
		return
	}

	logger := util.Log
	ctx := context.Background()

	err := scanpay.GetScanpayComplaintService().SaveComplaintReasonCodes(ctx)
	logger.Errorf("Error while running cron Save Complaint Reason Codes: %s", err.Error())
}
