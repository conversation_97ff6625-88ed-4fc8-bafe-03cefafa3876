version: "3.7"

services:
    magneto:
        stdin_open: true
        tty: true
        build:
            context: .
            dockerfile: Dockerfile
        restart: always
        image: magneto:latest
        container_name: magneto
        environment:
            - DEBUG=False
            - OTEL_SERVICE_NAME=magneto-prod
            - OTEL_EXPORTER_OTLP_COMPRESSION=gzip

            # print traces on console
            - OTEL_LOG_LEVEL=debug
            # send traces to CubeAPM
            # - OTEL_EXPORTER_OTLP_TRACES_ENDPOINT=https://cubeapm-ingest.stashfin.com/v1/traces
        ports:
          - 8080:8080
        # command:
        #     - /bin/bash
        #     - -c
        #     - |
        #         sleep infinity
