package dtype

type AppConfig struct {
	AppName       string `koanf:"app.name"`
	WebBaseURL    string `koanf:"app.web_base_url"`
	EncryptionKey string `koanf:"app.encryption_key"`

	DBUsername string `koanf:"db.username"`
	DBPassword string `koanf:"db.password"`
	DBHost     string `koanf:"db.host"`
	DBPort     int    `koanf:"db.port"`
	DBName     string `koanf:"db.name"`
	DBMaxPools int    `koanf:"db.max_pools"`
	DBMaxIdle  int    `koanf:"db.max_idle"`

	SentryDSN   string  `koanf:"sentry.dsn"`
	NewRelicKey string  `koanf:"newrelic.key"`
	Environment EnvType `koanf:"env"`

	RedisHost     string `koanf:"redis.host"`
	RedisPassword string `koanf:"redis.password"`
	RedisDB       int    `koanf:"redis.db"`

	BigcityUserAuthUrl           string `koanf:"bigcity.user_auth_url"`
	BigcityOrderCreateUrl        string `koanf:"bigcity.order_create_url"`
	BigcityAPIKey                string `koanf:"bigcity.api_key"`
	BigcityAPIUsername           string `koanf:"bigcity.username"`
	BigcityAPISecret             string `koanf:"bigcity.secret"`
	VoucherEligibleStashcash     int    `koanf:"bigcity.voucher_eligible_stashcash"`
	BigcityVoucherRedeemptionUrl string `koanf:"bigcity.redeemption_url"`

	ApiV2BaseURL    string `koanf:"apiv2.base_url"`
	ApiV2SecretKey  string `koanf:"apiv2.secret_key"`
	ApiV2AppVersion string `koanf:"apiv2.app_version"`

	PaymentServiceURL string `koanf:"paymentservice.url"`

	ICICIPrivateKeyPath               string `koanf:"icici.private_key_path"`
	ICICIPublicKeyPath                string `koanf:"icici.public_key_path"`
	ICICIBaseURL                      string `koanf:"icici.base_url"`
	ICICIApiKey                       string `koanf:"icici.api_key"`
	ICICICheckStatusApiKey            string `koanf:"icici.check_status_api_key"`
	ScanpayPendingTransactionSyncSize int    `koanf:"scanpay.pending_transaction_sync_size"`
	ComplaintReasonCodesCronEnable    bool   `koanf:"scanpay.complaint_reason_codes_cron_enable"`

	CronEnable bool `koanf:"cron.enable"`
	CliEnable  bool `koanf:"cli_enable"`

	ServiceSecretKey string `koanf:"service_secret_key"`

	OneAssistBaseURL                      string   `koanf:"oneassist.base_url"`
	OneAssistSecretKey                    string   `koanf:"oneassist.secret_key"`
	OneAssistBaseURL2                     string   `koanf:"oneassist.base_url2"`
	OneAssistSecretKey2                   string   `koanf:"oneassist.secret_key2"`
	InsurancePartnerAPICallTimeoutSeconds int      `koanf:"insurance_partner_api_call_timeout_seconds"`
	RestructureEnabled                    bool     `koanf:"restructure_loan_enabled"`
	InsuranceCashFreeGatewayProductUIDs   []string `koanf:"insurance_cashfree_gateway_product_uids"`

	VoucherGramBaseURL  string `koanf:"vouchergram.base_url"`
	VoucherGramKey      string `koanf:"vouchergram.key"`
	VoucherGramIvKey    string `koanf:"vouchergram.iv_key"`
	VoucherGramUsername string `koanf:"vouchergram.username"`
	VoucherGramPassword string `koanf:"vouchergram.password"`

	OfferPaymentGateway            int    `koanf:"offer.payment_gateway"`
	OfferLocCouponCode             string `koanf:"offer.loc_coupon_code"`
	CartTotalDiscountedAmountLimit int    `koanf:"offer.cart_total_discounted_amount_limit"`

	AWSRegion                string `koanf:"aws.region"`
	AWSAccessKeyID           string `koanf:"aws.access_key_id"`
	AWSSecretAccessKey       string `koanf:"aws.secret_access_key"`
	RewardServiceAWSQueueUrl string `koanf:"aws.reward_service_queue_url"`
}
