package dtype

type BigcityVoucherCreateRequest struct {
	OrderTimestamp string     `json:"order_timestamp" validate:"required"`
	OrderTotal     int        `json:"order_total" validate:"required"`
	OrderItems     int        `json:"order_items" validate:"required"`
	LineItems      []LineItem `json:"line_items" validate:"required"`
}

type LineItem struct {
	OrderId  string `json:"order_id" validate:"required"`
	SKU      string `json:"SKY" validate:"required"`
	Quantity int    `json:"qty" validate:"required"`
}

type BigcityUserAuthRequest struct {
	APIKey   string `json:"api_key" validate:"required"`
	Username string `json:"username" validate:"required"`
	Secret   string `json:"secret" validate:"required"`
	Mobile   string `json:"mobile_no" validate:"required"`
}
