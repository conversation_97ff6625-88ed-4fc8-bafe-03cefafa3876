package dtype

type BigcityVoucherCreateResponse struct {
	OrderId       int64     `json:"order_id" validate:"required"`
	State         string    `json:"state" validate:"required"`
	RedemptionUrl string    `json:"redemption_url" validate:"required"`
	HowToAvail    string    `json:"how_to_avail" validate:"required"`
	Terms         string    `json:"terms" validate:"required"`
	Note          string    `json:"note" validate:"required"`
	Vouchers      []Voucher `json:"vouchers" validate:"required"`
	CreatedAt     string    `json:"created_at" validate:"required"`
	UpdatedAt     string    `json:"updated_at" validate:"required"`
}

type Voucher struct {
	VoucherCode string `json:"voucher_code" validate:"required"`
	Id          string `json:"id" validate:"required"`
	Validity    string `json:"validity" validate:"required"`
}

type BigcityUserAuthResponse struct {
	Token      string `json:"customer_token" validate:"required"`
	ExpiryTime string `json:"token_expiry" validate:"required"`
}

type VoucherHistories struct {
	TotalVouchers   int             `json:"total_vouchers" validate:"required"`
	ActiveVouchers  int             `json:"active_vouchers" validate:"required"`
	ExpiredVouchers int             `json:"expired_vouchers" validate:"required"`
	URL             string          `json:"url" validate:"required"`
	HasNextPage     bool            `json:"has_next_page" validate:"required"`
	Vouchers        []VoucherDetail `json:"vouchers" validate:"required"`
}

type VoucherDetail struct {
	VoucherCode       string `json:"voucher_code" validate:"required"`
	Validity          string `json:"validity" validate:"required"`
	Status            string `json:"status" validate:"required"`
	RedeemedStashcash int    `json:"redeemed_stashcash" validate:"required"`
}

type VoucherResponse struct {
	RedeemableStashCash      int    `json:"redeemable_stashcash" validate:"required"`
	RedeemableStashCashLimit int    `json:"redeemable_stashcash_limit" validate:"required"`
	TotalStashCash           int    `json:"total_stash_cash" validate:"required"`
	LockedStashCash          int    `json:"locked_stashcash" validate:"required"`
	VoucherCode              string `json:"voucher_code" validate:"required"`
	Validity                 string `json:"validity" validate:"required"`
	URL                      string `json:"url" validate:"required"`
}

type VoucherErrorResponse struct {
	Status  int    `json:"status" validate:"required"`
	Message string `json:"message" validate:"required"`
}
