package dtype

var (
	RID   string = "rid"
	CLOG  string = "clog"
	DBCON string = "dbcon"
)

const (
	CustomerID = "customerID"
	Insurance  = "insurance"
)

type EnvType string

const (
	ProdEnv    EnvType = "prod"
	StagingEnv EnvType = "staging"
	LocalEnv   EnvType = "local"
)

var (
	Environments = []EnvType{ProdEnv, StagingEnv, LocalEnv}
)

func (e EnvType) String() string {
	return string(e)
}

const OfferMaxAmountWithoutOkyc float32 = 2000.0
const OfferMaxTransactionAmount float32 = 50000.00
const OfferMaxTransactionAmountPerMonth float32 = 100000.00
const OfferMaxTransactionsPerMonth int = 10

type OkycStatus int

const (
	OkycInitStatus              OkycStatus = 0
	OkycDoneStatus              OkycStatus = 1
	OkycAadhaarNotMatchedStatus OkycStatus = 2
	OkycPendingStatus           OkycStatus = 3
	OkycFailedStatus            OkycStatus = 4
)

var GateWayModes map[int]string = map[int]string{
	1: "Razorpay",
	2: "Cashfree"}
