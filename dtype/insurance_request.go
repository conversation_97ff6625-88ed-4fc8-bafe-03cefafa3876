package dtype

import "gorm.io/datatypes"

type OTPRequest struct {
	Name   string `json:"name" form:"name" validate:"required,validName"`
	Gender string `json:"gender" form:"gender" validate:"required"`
	Mobile int    `json:"mobile" form:"mobile" validate:"required"`
}

type VerifyOTPRequest struct {
	Mobile     int    `json:"mobile" form:"mobile" validate:"required"`
	OTP        int    `json:"otp" form:"otp" validate:"required"`
	ProductUID string `json:"product_uid" form:"product_uid" validate:"required"`
	Name       string `json:"name" form:"name" validate:"required,validName"`
}

type SaveApplicationDetailsRequestBody struct {
	UserDetails struct {
		Gender              string  `json:"gender" form:"gender" validate:"required"`
		Name                string  `json:"name" form:"name" validate:"required,validName"`
		DateOfBirth         string  `json:"dob" form:"dob" validate:"required,validDate"`
		Address             string  `json:"address" form:"address" validate:"required,validAddress"`
		Pincode             string  `json:"pincode" form:"pincode" validate:"required,validPincode"`
		Email               string  `json:"email" form:"email" validate:"required,email"`
		NomineeName         string  `json:"nominee_name" form:"nominee_name,default=nil" validate:"omitempty,validName"`
		NomineeDOB          string  `json:"nominee_dob" form:"nominee_dob" validate:"omitempty,validDate"`
		NomineeRelation     int     `json:"nominee_relation" form:"nominee_relation" validate:"omitempty,gte=1,lte=6"`
		FamilyConstruct     float64 `json:"family_construct" form:"family_construct" validate:"omitempty,gte=1,lte=4"`
		DeductibleAmount    float64 `json:"deductible_amount" form:"deductible_amount" validate:"omitempty"`
		CoverageAmount      float64 `json:"coverage_amount" form:"coverage_amount"`
		Construct           float64 `json:"construct" form:"construct"`
		ProductCovered      float64 `json:"product_covered" form:"product_covered"`
		OfferDiscount       string  `json:"offerDiscount"`
		MembershipStartDate string  `json:"membership_start_date" form:"membership_start_date" validate:"omitempty,validDate"`
	} `json:"user_details" form:"user_details"`
}

type SaveFeedbackRequest struct {
	QuestionID int64  `json:"question_id" validate:"required"`
	Response   int64  `json:"response" validate:"required"`
	Remarks    string `json:"remarks"`
}

type Condition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

type Rule struct {
	Conditions  []Condition    `json:"conditions"`
	ResultValue datatypes.JSON `json:"result_value"`
}

type RulesConfig struct {
	Rules   []Rule         `json:"rules"`
	Default datatypes.JSON `json:"default"`
}

type ExternalApplicationRequest struct {
	Name         string `json:"name" validate:"required,validName"`
	PolicyNumber string `json:"policy_number" validate:"required"`
	PhoneNumber  string `json:"phone_number" validate:"required"`
	InsurerName  string `json:"insurer_name" validate:"required"`
}
