package dtype

type VerifyOTPResponse struct {
	IsRegistered bool   `json:"is_registered"`
	IsEligible   bool   `json:"is_eligible"`
	AccessToken  string `json:"access_token"`
}

type InsuranceFeedbackOption struct {
	ID   int64  `json:"id"`
	Text string `json:"text"`
}

type InsuranceFeedbackQuestion struct {
	ID   int64  `json:"id"`
	Text string `json:"text"`
}

type InsuranceFeedbackQuestionResponse struct {
	IsFeedbackRequired bool                       `json:"is_feedback_required"`
	Question           *InsuranceFeedbackQuestion `json:"question,omitempty"`
	Options            []InsuranceFeedbackOption  `json:"options,omitempty"`
}

type InsuranceHomeResponse struct {
	Title        string              `json:"title"`
	SubTitle     string              `json:"sub_title"`
	TopBanner    []BannerItem        `json:"top_banner"`
	Categories   []InsuranceCategory `json:"categories"`
	BottomBanner *BannerItem         `json:"bottom_banner,omitempty"`
}

type BannerItem struct {
	Name        string `json:"name"`
	ImageURL    string `json:"image_url"`
	RedirectURL string `json:"redirect_url"`
}

type InsuranceCategory struct {
	Logo       string `json:"logo"`
	Name       string `json:"name"`
	UID        string `json:"uid"`
	ActionType string `json:"action_type"`
	Action     string `json:"action"`
}

type InsuranceSubCatagoryResponse struct {
	Title         string              `json:"title"`
	SubTitle      string              `json:"sub_title"`
	SubCategories []InsuranceCategory `json:"sub_categories"`
}

type InsuranceListingResponse struct {
	Title     string                    `json:"title"`
	SubTitle  string                    `json:"sub_title"`
	Insurance []InsuranceListingProduct `json:"insurance"`
}

type InsuranceListingProduct struct {
	Logo       string   `json:"logo"`
	Name       string   `json:"name"`
	UID        string   `json:"uid"`
	ActionType string   `json:"action_type"`
	Action     string   `json:"action"`
	KeyPoints  []string `json:"key_points,omitempty"`
	Comment    string   `json:"comment,omitempty"`
}

type InsuranceBannerResponse struct {
	Banner []BannerItem `json:"banner"`
}

type InsuraneListingMetaData struct {
	KeyPoints []string `json:"key_points"`
	Comment   string   `json:"comment"`
}
