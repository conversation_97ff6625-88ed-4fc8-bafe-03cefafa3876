package dtype

type CreateInsuranceApplicationRequestBody struct {
	ProductUID string `json:"product_uid" form:"product_uid" binding:"required"`
}

type InternalInsuranceApplicationRequestBody struct {
	ProductUID string `json:"product_uid" form:"product_uid" binding:"required"`
	CustomerID int64  `json:"customer_id" form:"customer_id" binding:"required"`
}

type CreateCustomerLeadRequestBody struct {
	Type  int64  `json:"type" form:"type" binding:"required"`
	Name  string `json:"name" form:"name" binding:"required"`
	Email string `json:"email" form:"email" binding:"required,email"`
}

type RequestOTP struct {
	Phone    int `json:"phone" form:"phone" validate:"required"`
	Register int `json:"register" form:"register"`
}

type LoginRequest struct {
	Phone     int    `json:"phone" form:"phone" validate:"required"`
	OTP       int    `json:"otp" form:"otp" validate:"required"`
	Register  int    `json:"register" form:"register"`
	UTMSource string `json:"utm_source" form:"utm_source"`
	AppType   string `json:"app_type" form:"app_type"`
}
