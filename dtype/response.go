package dtype

import "gorm.io/datatypes"

type BaseResponse struct {
	Success      bool        `json:"success"`
	Data         interface{} `json:"data"`
	ErrorMessage string      `json:"error_message"`
}

type ProductData struct {
	Logo           string `json:"logo"`
	Title          string `json:"title"`
	SubTitle       string `json:"sub_title"`
	Type           string `json:"type"`
	CoverageAmount int    `json:"coverage_amount"`
	Tenure         string `json:"tenure"`
	Premium        int    `json:"premium"`
	ProductCovered int64  `json:"product_covered"`
}

type ProductDataWithTerms struct {
	ProductData
	TermsAndConditions string `json:"terms_and_conditions"`
}

type Metadata struct {
	Title string `json:"title"`
}

type ExtraDetails struct {
	Metadata Metadata `json:"metadata"`
	Data     any      `json:"data"`
}

type ProductDetailsData struct {
	Metadata Metadata             `json:"metadata"`
	Data     ProductDataWithTerms `json:"data"`
}

type ProductDetailResponseData struct {
	ProductDetails  ProductDetailsData `json:"product_details"`
	WhatsCovered    *ExtraDetails      `json:"whats_covered"`
	WhatsNotCovered *ExtraDetails      `json:"whats_not_covered"`
	KeyCoverage     *ExtraDetails      `json:"key_coverage_and_benifits"`
	Documents       *ExtraDetails      `json:"documents"`
}

type CreateInsuranceApplicationData struct {
	Gender          string `json:"gender,omitempty"`
	Name            string `json:"name"`
	DateOfBirth     string `json:"dob"`
	Address         string `json:"address"`
	Pincode         string `json:"pincode"`
	Email           string `json:"email"`
	NomineeName     string `json:"nominee_name,omitempty"`
	NomineeDOB      string `json:"nominee_dob,omitempty"`
	NomineeRelation int    `json:"nominee_relation,omitempty"`
	FamilyConstruct int    `json:"family_construct,omitempty"`
	ApplicationUID  string `json:"application_uid"`
}

type CustomerDetailData struct {
	Gender      string `json:"gender"`
	Name        string `json:"name"`
	DateOfBirth string `json:"dob"`
	Address     string `json:"address"`
	Pincode     string `json:"pincode"`
	Email       string `json:"email"`
}

type PaymentLinkData struct {
	PaymentLink string `json:"payment_link"`
}

type TransactionDetails struct {
	ApplicationID      string `json:"application_id"`
	Name               string `json:"name"`
	TransactionID      string `json:"transaction_id"`
	MembershipNumber   string `json:"membership_number,omitempty"`
	StartDate          string `json:"start_date,omitempty"`
	EndDate            string `json:"end_date,omitempty"`
	InvoiceDownloadURL string `json:"invoice_download_url,omitempty"`
}

type PaymentStatusData struct {
	ProductDetails     ProductData        `json:"product_details"`
	TransactionDetails TransactionDetails `json:"transaction_details"`
}

type PaymentStatusResponse struct {
	Status int               `json:"status"`
	Data   PaymentStatusData `json:"data"`
}

type Field struct {
	Type           string         `json:"type"`
	Key            string         `json:"key"`
	Label          string         `json:"label"`
	Validation     string         `json:"validation,omitempty"`
	Placeholder    string         `json:"placeholder,omitempty"`
	InitialValue   interface{}    `json:"initial_value"`
	MaxLength      int            `json:"max_length,omitempty"`
	MaxValue       string         `json:"max_value,omitempty"`
	MinValue       string         `json:"min_value,omitempty"`
	Editable       bool           `json:"editable"`
	Options        datatypes.JSON `json:"options,omitempty"`
	AdditionalInfo datatypes.JSON `json:"additional_info,omitempty"`
}

type CreateGenericApplicationResponse struct {
	Fields         []Field `json:"fields"`
	ApplicationUID string  `json:"application_uid"`
}

type PrefilledData struct {
	Name  string `json:"name"`
	Email string `json:"email"`
}

type LeadDetailResponse struct {
	IsRequired    bool           `json:"is_required"`
	PreFilledData *PrefilledData `json:"pre_filled_data"`
}

type LoginResponse struct {
	AccessToken string `json:"access_token"`
}

type OrdersData struct {
	OrderID string `json:"order_id"`
	Msg     string `json:"msg"`
}
type FetchOrdersCouponCodesResponse struct {
	OrdersData []OrdersData `json:"orders_data"`
}
