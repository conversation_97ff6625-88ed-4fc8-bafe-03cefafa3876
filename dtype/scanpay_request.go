package dtype

type Accounts struct {
	AccountPayloads []Account `json:"accounts" validate:"required"`
}

type Account struct {
	AccPrimary         int    `json:"acc_primary"`
	BankId             int32  `json:"bank_id" validate:"required"`
	BankMMID           string `json:"bank_mmid" validate:"required"`
	AccNo              string `json:"acc_no" validate:"required"`
	AccName            string `json:"acc_name" validate:"required"`
	AccType            string `json:"acc_type" validate:"required"`
	AccStatus          string `json:"acc_status" validate:"required"`
	AccRef             string `json:"account_ref" validate:"required"`
	AccBankName        string `json:"account_bank_name" validate:"required"`
	BankIFSC           string `json:"bank_ifsc" validate:"required"`
	BankMbeba          string `json:"bank_mbeba" validate:"required"`
	AllowedCredentials string `json:"allowed_credentials" validate:"required"`
}

type UpiStatusDetail struct {
	UpiStatus string `json:"upi_status" validate:"required"`
	UpiId     string `json:"upi_id" validate:"required"`
	ProfileId string `json:"profile_id" validate:"required"`
}

type RaiseTransactionComplaint struct {
	OriSeqNo   string `json:"ori_seq_no" validate:"required"`
	ReasonCode string `json:"reason_code"`
}

type GetComplaintStatus struct {
	OriSeqNo string `json:"ori_seq_no" validate:"required"`
}

type CheckTransactionStatus struct {
	OriSeqNo string `json:"ori_seq_no" validate:"required"`
}

type ICICIPaymentCallbackRequest struct {
	ProfileId         string `json:"ProfileId" validate:"required"`
	Amount            string `json:"Amount" validate:"required"`
	OriginalTxnId     string `json:"OriginalTxnId" validate:"required"`
	TxnStatus         string `json:"TxnStatus" validate:"required"`
	TxnCompletionDate string `json:"TxnCompletionDate" validate:"required"`
	TxnType           string `json:"TxnType" validate:"required"`
	PayerData         Payer  `json:"Payer" validate:"required"`
	PayeeData         Payee  `json:"Payee" validate:"required"`
	Rrn               string `json:"Rrn" validate:"required"`
}

type Payer struct {
	AccountNo string `json:"AccountNo" validate:"required"`
	Ifsc      string `json:"Ifsc" validate:"required"`
	Name      string `json:"Name" validate:"required"`
}

type Payee struct {
	Name     string `json:"Name" validate:"required"`
	RespCode string `json:"RespCode" validate:"required"`
}
