package dtype

type Transactions struct {
	TransactionHisotries []Transaction `json:"transaction_list" validate:"required"`
	Has_next_page        bool          `json:"has_next_page" validate:"required"`
}

type Transaction struct {
	TransAmount   float32 `json:"trans_amount" validate:"required"`
	TransStatus   string  `json:"trans_status" validate:"required,oneof=success failed pending"`
	PayeeName     string  `json:"payee_name" validate:"required"`
	PaidFrom      string  `json:"paid_from" validate:"required"`
	TransId       string  `json:"trans_id" validate:"required"`
	TransAccount  string  `json:"trans_account" validate:"required"`
	TransSeqNo    string  `json:"trans_seq_no" validate:"required"`
	TransTime     string  `json:"trans_time" validate:"required"`
	TransType     string  `json:"trans_type" validate:"required"`
	TransRemark   string  `json:"trans_remark"`
	TransBankMMID string  `json:"trans_bank_mmid" validate:"required"`
	IsComplaint   bool    `json:"is_complaint"`
}

type ComplaintReasonCodesResponse struct {
	ComplaintReasonCodes []ComplaintReasonCodeData `json:"complaint_reason_codes"`
}

type ComplaintReasonCodeData struct {
	Description string `json:"description"`
	ReasonCode  string `json:"reason_code"`
}
