package helper

import (
	"errors"
	"fmt"
	"net/http"
)

type customError struct {
	Code    int
	Message string
}

func (e customError) Error() string {
	return fmt.Sprintf("Code: %d, Message: %s", e.Code, e.Message)
}

func NewCustomError(message string, code int) *customError {
	return &customError{
		Code:    code,
		Message: message,
	}
}

func BadRequestError(message string) *customError {
	return &customError{
		Code:    http.StatusBadRequest,
		Message: message,
	}
}

func NotFoundError(message string) *customError {
	return &customError{
		Code:    http.StatusNotFound,
		Message: message,
	}
}

func FormatErr(err error) (message string, statusCode int) {
	var customErr *customError
	if errors.As(err, &customErr) {
		return customErr.Message, customErr.Code
	}

	return "Request Failed", http.StatusInternalServerError
}
