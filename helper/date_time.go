package helper

import "time"

const (
	DateStringParserLayout = "02-Jan-2006"
	DDMMYYYYParserLayout   = "02-01-2006"
)

// DateStringToTime take string in format "12-Nov-2023"
func DateStringToTime(dateStr string) (*time.Time, error) {
	date, err := time.Parse(DateStringParserLayout, dateStr)
	if err != nil {
		return &time.Time{}, err
	}
	return &date, nil
}

func CalculateBirthDates(minAge, maxAge int) (string, string) {
	layout := "02-01-2006"
	currentDate := time.Now()

	currentYear, currentMonth, currentDay := currentDate.Date()

	minBirthYear := currentYear - minAge
	maxBirthYear := currentYear - maxAge - 1

	minBirthDate := time.Date(minBirthYear, currentMonth, currentDay, 0, 0, 0, 0, time.UTC)
	maxBirthDate := time.Date(maxBirthYear, currentMonth, currentDay, 0, 0, 0, 0, time.UTC)

	minDateStr := minBirthDate.Format(layout)
	maxDateStr := maxBirthDate.Format(layout)

	return minDateStr, maxDateStr
}

func CalculateAge(dob time.Time) int {
	today := time.Now()
	years := today.Year() - dob.Year()

	if today.YearDay() < dob.YearDay() {
		years--
	}
	return years
}
