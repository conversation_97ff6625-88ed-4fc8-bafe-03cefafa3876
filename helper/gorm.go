package helper

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
)

func GenericUpdate[T any](ctx context.Context, obj *T, updateFields []string) error {
	if len(updateFields) == 0 {
		updateFields = []string{"*"}
	}

	db := config.GetConnectionCtx(ctx)
	err := db.WithContext(ctx).Select(updateFields).Updates(obj).Error
	return err
}

func GenericCreate[T any](ctx context.Context, obj *T) (*T, error) {
	db := config.GetConnectionCtx(ctx)
	err := db.WithContext(ctx).
		Create(obj).Error

	return obj, err
}

func GenericGet[T any](ctx context.Context, condition *T, joins ...string) (*T, error) {
	db := config.GetConnectionCtx(ctx)

	var obj T
	query := db.WithContext(ctx).
		Model(&obj).
		Where(condition)

	for _, join := range joins {
		query = query.Joins(join)
	}

	err := query.First(&obj).Error

	return &obj, err
}

func GenericGetLast[T any](ctx context.Context, condition *T) (*T, error) {
	db := config.GetConnectionCtx(ctx)

	var obj T
	err := db.WithContext(ctx).
		Where(condition).
		Last(&obj).Error

	return &obj, err
}

func GenericFilter[T any](ctx context.Context, condition *T, joins ...string) ([]T, error) {
	db := config.GetConnectionCtx(ctx)

	var objs []T
	var model T

	query := db.WithContext(ctx).
		Model(&model).
		Where(condition)

	for _, join := range joins {
		query = query.Joins(join)
	}

	err := query.Scan(&objs).Error

	return objs, err
}

func GenericDelete[T any](ctx context.Context, obj *T) error {
	db := config.GetConnectionCtx(ctx)
	return db.WithContext(ctx).Delete(obj).Error
}
