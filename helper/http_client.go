package helper

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"

	"stashfin.com/stashfin/magneto/util"
)

type Client struct {
	HttpClient *http.Client
}

func NewHttpClient() *Client {
	// Create a custom Transport with InsecureSkipVerify set to true/false based on config
	tr := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: false}, // Disable certificate verification if true``
	}

	// Create a custom HTTP client with the custom Transport
	client := &http.Client{
		Transport: tr,
		Timeout:   0, // Request timeout
	}

	return &Client{HttpClient: client}
}

func GetDefaultHeader() map[string]string {
	return map[string]string{
		"Content-Type": "application/json",
	}
}

func addHeaderToRequest(req *http.Request, headers map[string]string) {
	for k, v := range headers {
		req.Header.Add(k, v)
	}
}

func (c *Client) Get(url string, headers map[string]string, result interface{}) error {
	client := &http.Client{}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %s", err)
	}

	addHeaderToRequest(req, headers)
	res, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error while making request: %s", err)
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %s", err)
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return fmt.Errorf("error unmarshalling response body: %s", err)
	}
	return nil
}

func (c *Client) GetWithDefaultHeader(url string, result interface{}) error {
	return c.Get(url, GetDefaultHeader(), &result)
}

func (c *Client) Post(url string, headers map[string]string, body map[string]any, result interface{}) error {
	client := &http.Client{}

	reqBody, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("error marshaling request body: %s", err)
	}

	req, err := http.NewRequest("POST", url, bytes.NewReader(reqBody))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %s", err)
	}

	addHeaderToRequest(req, headers)
	res, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("error while making request: %s", err)
	}
	defer res.Body.Close()

	responseBody, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %s", err)
	}

	util.Log.Infof("\nresponse: %s", string(responseBody))

	err = json.Unmarshal(responseBody, &result)
	if err != nil {
		return fmt.Errorf("error unmarshalling response body: %s", err)
	}

	return nil
}

func (c *Client) PostWithFormData(url string, headers map[string]string, body string, result interface{}) error {
	req, err := http.NewRequest("POST", url, strings.NewReader(body))
	if err != nil {
		return fmt.Errorf("error creating HTTP request: %s", err)
	}

	headers["Content-Type"] = "application/x-www-form-urlencoded"
	addHeaderToRequest(req, headers)

	util.Log.Infof("request:-  url: %s,  body: %s, header: %s", url, body, headers)

	res, err := c.HttpClient.Do(req)
	if err != nil {
		return fmt.Errorf("error while making request: %s", err)
	}
	defer res.Body.Close()

	responseBody, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("error reading response body: %s", err)
	}
	util.Log.Infof("responseBody: %s", responseBody)

	err = json.Unmarshal(responseBody, &result)
	if err != nil {
		return fmt.Errorf("error unmarshalling response body: %s", err)
	}

	return nil
}
