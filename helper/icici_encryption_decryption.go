package helper

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"io/ioutil"
	"strings"

	"stashfin.com/stashfin/magneto/config"
)

func GetICICIDecryptedResponse(encryptedData string, encryptedKey string) (map[string]interface{}, error) {

	clientPrivateKeyPath := config.GetConfigValues().ICICIPrivateKeyPath
	iv := getFirst16Bytes(encryptedData)
	sessionKey := make([]byte, 16)
	rand.Read(sessionKey)

	decryptedKey, err := rsaDecrypt(encryptedKey, clientPrivateKeyPath)
	if err != nil {
		return nil, err
	}

	decryptedResponse, err := aesDecrypt(encryptedData, decryptedKey, iv)
	if err != nil {
		return nil, err
	}

	cleanDecryptRespone := cleanText(string(decryptedResponse))

	var response map[string]interface{}
	err = json.Unmarshal([]byte(cleanDecryptRespone), &response)
	if err != nil {
		return nil, err
	}

	return response, nil
}

func getFirst16Bytes(base64String string) []byte {
	decoded, _ := base64.StdEncoding.DecodeString(base64String)
	return decoded[:16]
}

func rsaDecrypt(encryptedKey string, privateKeyPath string) ([]byte, error) {
	privateKeyBytes, err := ioutil.ReadFile(privateKeyPath)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(privateKeyBytes)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block containing private key")
	}

	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	encryptedKeyBytes, err := base64.StdEncoding.DecodeString(encryptedKey)
	if err != nil {
		return nil, err
	}

	decryptedKey, err := rsa.DecryptPKCS1v15(rand.Reader, privateKey, encryptedKeyBytes)
	if err != nil {
		return nil, err
	}

	return decryptedKey, nil
}

func aesDecrypt(encryptedData string, sessionKey []byte, iv []byte) ([]byte, error) {
	blockCipher, err := aes.NewCipher(sessionKey)
	if err != nil {
		return nil, err
	}

	cipherText, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, err
	}

	decryptedData := make([]byte, len(cipherText))
	cbcDecrypter := cipher.NewCBCDecrypter(blockCipher, iv)
	cbcDecrypter.CryptBlocks(decryptedData, cipherText)

	return decryptedData, nil
}

func cleanText(input string) string {
	firstIndex := strings.Index(input, "{")
	lastIndex := strings.LastIndex(input, "}")

	return input[firstIndex : lastIndex+1]
}

func GetICICIEncryptedRequest(data map[string]string) (map[string]any, error) {
	pubKeyBytes, err := ioutil.ReadFile(config.GetConfigValues().ICICIPublicKeyPath)
	if err != nil {
		return nil, err
	}

	block, _ := pem.Decode(pubKeyBytes)
	if block == nil {
		return nil, fmt.Errorf("Failed to decode PEM block containing public key")
	}

	pubKey, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, err
	}

	rsaPubKey, ok := pubKey.PublicKey.(*rsa.PublicKey)
	if !ok {
		return nil, fmt.Errorf("The public key is not an RSA public key")				
	}

	sessionKey := make([]byte, 16)
	if _, err := rand.Read(sessionKey); err != nil {
		return nil, err
	}

	encryptedKey, err := rsa.EncryptPKCS1v15(rand.Reader, rsaPubKey, sessionKey)
	if err != nil {
		return nil, err
	}

	iv := make([]byte, 16)
	if _, err := rand.Read(iv); err != nil {
		return nil, err
	}

	request, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	blockCipher, err := aes.NewCipher(sessionKey)
	if err != nil {
		return nil, err
	}

	paddedRequest := pad(request, aes.BlockSize)
	encryptedData := make([]byte, len(paddedRequest))
	cbcEncrypter := cipher.NewCBCEncrypter(blockCipher, iv)
	cbcEncrypter.CryptBlocks(encryptedData, paddedRequest)

	encryptedKeyBase64 := base64.StdEncoding.EncodeToString(encryptedKey)
	ivBase64 := base64.StdEncoding.EncodeToString(iv)
	encryptedDataBase64 := base64.StdEncoding.EncodeToString(encryptedData)

	request_data := map[string]any{
		"requestId":            "",
		"service":              "LOP",
		"encryptedKey":         encryptedKeyBase64,
		"oaepHashingAlgorithm": "NONE",
		"iv":                   ivBase64,
		"encryptedData":        encryptedDataBase64,
		"clientInfo":           "",
		"optionalParam":        "",
	}

	return request_data, nil
}

func pad(data []byte, blockSize int) []byte {
	padding := blockSize - (len(data) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}