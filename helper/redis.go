package helper

import (
	"context"
	"encoding/json"
	"errors"
	"time"

	"github.com/redis/go-redis/v9"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/util"
)

func CacheSet(ctx context.Context, key string, value any, expiration time.Duration) error {
	rc := config.GetRedis()
	return rc.Set(ctx, key, value, expiration).Err()
}

func CacheSetObj(ctx context.Context, key string, value any, expiration time.Duration) error {
	rc := config.GetRedis()

	cacheEntry, err := json.Marshal(value)
	if err != nil {
		return err
	}

	err = rc.Set(ctx, key, cacheEntry, expiration).Err()
	if err != nil {
		return err
	}
	return nil
}

func CacheGet(ctx context.Context, key string, target any) error {
	rc := config.GetRedis()

	val, err := rc.Get(ctx, key).Result()
	if err != nil {
		return err
	}

	err = json.Unmarshal([]byte(val), &target)
	if err != nil {
		return err
	}
	return nil
}

func CacheFunc[T any](ctx context.Context, key string, cacheFunc func() (T, error), expiration time.Duration) (T, error) {
	logger := util.CLogCtx(ctx)

	var result T
	err := CacheGet(ctx, key, &result)
	if err == nil {
		return result, nil
	}

	if !errors.Is(err, redis.Nil) {
		logger.Error("redis_fetch_err")
	}

	result, err = cacheFunc()
	if err != nil {
		return result, err
	}

	err = CacheSet(ctx, key, result, expiration)
	if err != nil {
		logger.Error("redis_save_err")
	}
	return result, nil
}
