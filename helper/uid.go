package helper

import (
	"context"

	"stashfin.com/stashfin/magneto/util"
)

const (
	UIDSize8 = 8
	UIDSize6 = 6
)

type UniqueChecker interface {
	IsUniqueUID(ctx context.Context, uid string) (bool, error)
}

func GenerateUniqueUID(ctx context.Context, repo <PERSON><PERSON><PERSON><PERSON><PERSON>, uidSize int) (string, error) {
	uid, err := util.GenerateUID(uidSize)
	if err != nil {
		return "", err
	}

	isUnique, err := repo.IsUniqueUID(ctx, uid)
	if err != nil {
		return "", err
	}

	if !isUnique {
		return GenerateUniqueUID(ctx, repo, uidSize)
	}

	return uid, nil
}
