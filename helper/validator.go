package helper

import (
	"errors"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/go-playground/validator"
)

var validate *validator.Validate

const (
	validNameRegex    = "^[A-Za-z ]+$"
	validPincodeRegex = "^[1-9][0-9]{5}$"
	validAddressRegex = "^.*[a-zA-Z0-9]+.*$"
)

func init() {
	validate = validator.New()
	validate.RegisterValidation("validName", validName)
	validate.RegisterValidation("validDate", isValidDateFormat)
	validate.RegisterValidation("validPincode", isValidPincodeFormat)
	validate.RegisterValidation("validAddress", isValidAddressFormat)
}

func ValidateStruct(s any) error {
	err := validate.Struct(s)
	if err == nil {
		return nil
	}

	errs, ok := err.(validator.ValidationErrors)
	if !ok {
		return errors.New("invalid request structure")
	}

	var errMsg string
	for idx, e := range errs {
		errMsg += msgForTag(e)
		if idx < len(errs)-1 {
			errMsg += " | "
		}
	}

	return errors.New(errMsg)
}

func msgForTag(fe validator.FieldError) string {
	switch fe.Tag() {
	case "required":
		return fmt.Sprintf("%s is required", fe.Field())
	case "phone":
		return fmt.Sprintf("%s is not a valid phone number", fe.Field())
	default:
		return fmt.Sprintf("Invalid field %s", fe.Field())
	}
}

func validName(fl validator.FieldLevel) bool {
	name := fl.Field().String()

	if len(name) == 0 || strings.TrimSpace(name) == "" {
		return false
	}

	regex := regexp.MustCompile(validNameRegex)
	return regex.MatchString(name)
}

func isValidDateFormat(fl validator.FieldLevel) bool {
	dateStr := fl.Field().String()
	_, err := time.Parse("02-01-2006", dateStr)
	return err == nil
}

func isValidPincodeFormat(fl validator.FieldLevel) bool {
	pin := fl.Field().String()
	regex := regexp.MustCompile(validPincodeRegex)
	return regex.MatchString(pin)
}

func isValidAddressFormat(fl validator.FieldLevel) bool {
	address := fl.Field().String()
	regex := regexp.MustCompile(validAddressRegex)
	return regex.MatchString(address)
}
