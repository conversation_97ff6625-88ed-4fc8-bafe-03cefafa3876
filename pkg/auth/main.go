package auth

import (
	"context"
	"stashfin.com/stashfin/magneto/pkg/auth/model"

	"github.com/gin-gonic/gin"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/auth/service"
)

type AuthService interface {
	ValidateCustomerLogin(ctx *gin.Context) (error, int64)
	ValidateAuthToken(ctx context.Context, authToken string) (int64, error)
	RequestOTP(ctx context.Context, phone, register int) error
	Login(ctx context.Context, req *dtype.LoginRequest) (*dtype.LoginResponse, error)
}

var instance AuthService = &service.AuthInterface{}

func GetAuthService() AuthService {
	return instance
}

type PartnerService interface {
	GetPartnersInfo(ctx context.Context, code string) (*model.PartnersModel, error)
}

var partnerInstance PartnerService = &service.PartnerInterface{}

func GetPartnerService() PartnerService {
	return partnerInstance
}
