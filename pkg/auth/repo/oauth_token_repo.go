package repo

import (
	"context"
	"time"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/auth/model"
)

type AuthRepo interface {
	GetCustomerId(authToken string, deviceId string, ctx context.Context) (int64, error)
	GetCustomerIdFromAuthToken(ctx context.Context, authToken string) (int64, error)
}

type AuthDB struct{}

var (
	authRepoInstance AuthRepo = &AuthDB{}
	authModel        model.AuthModel
)

func GetAuthRepo() AuthRepo {
	return authRepoInstance
}

func (*AuthDB) GetCustomerId(authToken string, deviceId string, ctx context.Context) (int64, error) {
	db := config.GetConnectionCtx(ctx)
	if err := db.WithContext(ctx).Where("device_id = ? AND token = ? AND published = 1 AND update_time > ?",
		deviceId, authToken, time.Now().Add(-time.Hour)).First(&authModel).Error; err != nil {
		return -1, err
	}
	return authModel.UserId, nil
}

func (*AuthDB) GetCustomerIdFromAuthToken(ctx context.Context, authToken string) (int64, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.AuthModel
	err := db.WithContext(ctx).
		Where(&model.AuthModel{Token: authToken, Published: 1}).
		Where("update_time > ?", time.Now().Add(-time.Hour)).
		Last(&obj).Error
	if err != nil {
		return 0, err
	}
	return obj.UserId, nil
}
