package repo

import (
	"context"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/auth/model"
)

type PartnerRepo interface {
	GetPartnerSecretKey(ctx context.Context, code string) (*model.PartnersModel, error)
}

type PartnerDB struct{}

var (
	partnerRepoInstance PartnerRepo = &PartnerDB{}
)

func GetPartnerRepo() PartnerRepo {
	return partnerRepoInstance
}

func (p *PartnerDB) GetPartnerSecretKey(ctx context.Context, code string) (*model.PartnersModel, error) {
	db := config.GetConnectionCtx(ctx)
	var obj model.PartnersModel
	if err := db.WithContext(ctx).Where("code = ?",
		code).First(&obj).Error; err != nil {
		return nil, err
	}
	return &obj, nil
}
