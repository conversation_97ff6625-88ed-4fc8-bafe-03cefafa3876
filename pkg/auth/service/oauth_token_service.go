package service

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	"stashfin.com/stashfin/magneto/pkg/auth/repo"
	"stashfin.com/stashfin/magneto/util"
)

type AuthInterface struct{}

func (*AuthInterface) ValidateCustomerLogin(c *gin.Context) (error, int64) {
	authToken := c.Get<PERSON>eader("auth_token")
	deviceId := c.GetHeader("device_id")

	customerId, err := repo.GetAuthRepo().GetCustomerId(authToken, deviceId, c.Request.Context())
	if err != nil {
		util.ErrorResponse("Invalid Credentials", c, http.StatusBadRequest)
		return err, -1
	}

	return nil, customerId
}

func (*AuthInterface) ValidateAuthToken(ctx context.Context, authToken string) (int64, error) {
	return repo.GetAuthRepo().GetCustomerIdFromAuthToken(ctx, authToken)
}
