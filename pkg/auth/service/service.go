package service

import (
	"context"
	"errors"
	"net/http"

	"gorm.io/gorm"
	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer"
)

func (s *AuthInterface) RequestOTP(ctx context.Context, phone, register int) error {
	_, err := customer.GetCustomerService().GetIDFromPhone(ctx, phone)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if err != nil && register == 0 {
		return helper.NewCustomError("You're not register with us.", http.StatusBadRequest)
	}

	if err != nil && register == 1 {
		return client.GetApiV2Client().RequestOTPBeforeRegistration(ctx, phone)
	}

	return client.GetApiV2Client().RequestOTP(ctx, phone)
}

func (s *AuthInterface) Login(ctx context.Context, req *dtype.LoginRequest) (*dtype.LoginResponse, error) {

	_, err := customer.GetCustomerService().GetIDFromPhone(ctx, req.Phone)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil && req.Register == 0 {
		return nil, helper.NewCustomError("You're not register with us.", http.StatusBadRequest)
	}

	if err != nil && req.Register == 1 {
		authToken, err := client.GetApiV2Client().VerifyOTPAndRegisterUser(ctx, req.Phone, req.OTP, req.UTMSource)
		if err != nil {
			return nil, err
		}
		return &dtype.LoginResponse{
			AccessToken: authToken,
		}, nil
	}

	authToken, err := client.GetApiV2Client().VerifyOTP(ctx, req.Phone, req.OTP)
	if err != nil {
		return nil, err
	}

	return &dtype.LoginResponse{
		AccessToken: authToken,
	}, nil
}
