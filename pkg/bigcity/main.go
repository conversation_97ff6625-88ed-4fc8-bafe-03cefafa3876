package bigcity

import (
	"context"

	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/bigcity/service"
)

type BigcityAuthService interface {
	GetUserAuthToken(context.Context, int64) (error, string)
	CreateUserAuthToken(context.Context, int64) (error, string)
}

type BigcityVoucherService interface {
	CreateBigCityVoucher(context.Context, int64) (error, *dtype.VoucherResponse)
	GetVoucherHistory(context.Context, int64, int, int, []string) (error, *dtype.VoucherHistories)
}

var bigcityAuthServiceInstance = &service.BigcityAuthInterface{}
var bigcityVoucherInstance BigcityVoucherService = service.NewBigcityVoucherService(bigcityAuthServiceInstance)

func GetBigcityAuthService() BigcityAuthService {
	return bigcityAuthServiceInstance
}

func GetBigcityVoucherService() BigcityVoucherService {
	return bigcityVoucherInstance
}
