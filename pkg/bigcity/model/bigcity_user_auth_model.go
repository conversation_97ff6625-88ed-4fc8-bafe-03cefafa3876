package model

import "time"

type BigcityUserAuthModel struct {
	Id         int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CustomerId int64     `gorm:"column:customer_id"`
	Token      string    `gorm:"column:token"`
	ExpiryTime time.Time `gorm:"column:expiry_time"`
	CreateDate time.Time `gorm:"column:create_date;autoCreateTime"`
	UpdateDate time.Time `gorm:"column:update_date;autoUpdateTime"`
}

func (BigcityUserAuthModel) TableName() string {
	return "bigcity_user_auth"
}
