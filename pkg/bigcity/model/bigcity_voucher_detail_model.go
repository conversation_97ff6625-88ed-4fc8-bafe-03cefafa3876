package model

import "time"

type BigcityVoucherModel struct {
	Id                int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CustomerId        int64     `gorm:"column:customer_id"`
	RedeemedStashcash int       `gorm:"column:redeemed_stashcash"`
	OrderId           string    `gorm:"column:order_id"`
	VoucherCode       string    `gorm:"column:voucher_code"`
	Status            string    `gorm:"column:status"`
	Validity          time.Time `gorm:"column:validity"`
	CreateDate        time.Time `gorm:"column:create_date;autoCreateTime"`
	UpdateDate        time.Time `gorm:"column:update_date;autoUpdateTime"`
}

func (BigcityVoucherModel) TableName() string {
	return "bigcity_voucher_detail"
}
