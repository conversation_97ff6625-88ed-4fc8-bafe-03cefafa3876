package model

import "time"

type StashLoyalityCash struct {
	Id                 int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CustomerId         int64     `gorm:"column:customer_id"`
	Cash               float32   `gorm:"column:cash"`
	TotalCash          float32   `gorm:"column:total_cash"`
	Narration          string    `gorm:"column:narration"`
	NarrationId        string  	 `gorm:"column:narration_id"`
	Category           int8      `gorm:"column:category"`
	Date               time.Time `gorm:"column:date"`
	CreateDate         time.Time `gorm:"column:create_date;autoCreateTime"`
	UpdateDate         time.Time `gorm:"column:update_date;autoUpdateTime"`
}

func (StashLoyalityCash) TableName() string {
	return "stash_loyalty_cash"
}
