package repo

import (
	"context"
	"errors"
	"strconv"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/bigcity/model"
)

type BigcityUserAuthRepo interface {
	Get(context.Context, int64) (*model.BigcityUserAuthModel, error)
	Save(context.Context, *model.BigcityUserAuthModel) error
}

type BigcityUserAuthDB struct {
}

var bigcityUserAuthInstance BigcityUserAuthRepo = &BigcityUserAuthDB{}

func GetBigcityUserAuthRepo() BigcityUserAuthRepo {
	return bigcityUserAuthInstance
}

func (*BigcityUserAuthDB) Save(ctx context.Context,
	bigcityUserAuth *model.BigcityUserAuthModel) error {
	db := config.GetConnectionCtx(ctx)

	bigcityUserAuthSaved, err := bigcityUserAuthInstance.Get(ctx, bigcityUserAuth.CustomerId)
	if err != nil {
		return err
	} else if bigcityUserAuthSaved.Id != 0 {
		bigcityUserAuthSaved.ExpiryTime = bigcityUserAuth.ExpiryTime
		bigcityUserAuthSaved.Token = bigcityUserAuth.Token
		bigcityUserAuthSaved.CustomerId = bigcityUserAuth.CustomerId
		bigcityUserAuth = bigcityUserAuthSaved
	}

	err = db.Save(bigcityUserAuth).Error
	if err != nil {
		return errors.New("Error while saving data in bigcity user auth: " + err.Error())
	}

	return nil
}

func (*BigcityUserAuthDB) Get(ctx context.Context, customerId int64) (
	*model.BigcityUserAuthModel, error) {
	db := config.GetConnectionCtx(ctx)
	var bigcityUserAuth model.BigcityUserAuthModel

	err := db.Where("customer_id = ?", customerId).Find(&bigcityUserAuth).Error
	if err != nil {
		return nil, errors.New("Error while getting data from bigcity user auth table for customer id " + strconv.FormatInt(customerId, 10) + ": " + err.Error())
	}

	return &bigcityUserAuth, nil
}
