package repo

import (
	"context"
	"errors"
	"strconv"
	"strings"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/bigcity/model"
)

type BigcityVoucherDetailRepo interface {
	Save(context.Context, *model.BigcityVoucherModel) error
	Get(context.Context, int64, int, int, []string) (error, *[]model.BigcityVoucherModel, bool)
	MarkVouchersInactive(context.Context, []string, *[]model.BigcityVoucherModel) error
	GetAll(context.Context, int64) (error, *[]model.BigcityVoucherModel)
}

type BigcityVoucherDetailDB struct {
}

var bigcityVoucherDetailInstance BigcityVoucherDetailRepo = &BigcityVoucherDetailDB{}

func GetBigcityVoucherDetailRepo() BigcityVoucherDetailRepo {
	return bigcityVoucherDetailInstance
}

func (*BigcityVoucherDetailDB) Save(ctx context.Context,
	bigcityVoucherDetail *model.BigcityVoucherModel) error {
	db := config.GetConnectionCtx(ctx)

	err := db.Save(bigcityVoucherDetail).Error
	if err != nil {
		return errors.New("Error while saving data in bigcity_voucher_detail")
	}

	return nil
}

func (*BigcityVoucherDetailDB) Get(ctx context.Context, customerId int64,
	page int, limit int, statusType []string) (error, *[]model.BigcityVoucherModel, bool) {
	db := config.GetConnectionCtx(ctx)
	var bigcityVoucherDetails []model.BigcityVoucherModel
	offset := (page - 1) * limit

	err := db.Where("customer_id = ? and status in ?", customerId, statusType).Order(
		"validity DESC").Offset(offset).Limit(limit).Find(&bigcityVoucherDetails).Error
	if err != nil {
		return errors.New("Error while getting data in bigcity_voucher_detail for customer id: " + 
		strconv.FormatInt(customerId, 10) + ": " + err.Error()), nil, false
	}

	has_next_page := len(bigcityVoucherDetails) == limit

	return nil, &bigcityVoucherDetails, has_next_page
}

func (*BigcityVoucherDetailDB) MarkVouchersInactive(ctx context.Context,
	expiredVouchers []string, models *[]model.BigcityVoucherModel) error {
	db := config.GetConnectionCtx(ctx)
	
	err := db.Model(models).Where("voucher_code in ?", expiredVouchers).Updates(
		map[string]interface{}{"status": "inactive"}).Error
	if err != nil {
		return errors.New("Error while marking inactive for expired vouchers: " + 
		strings.Join(expiredVouchers, ","))
	}
	return nil
}

func (*BigcityVoucherDetailDB) GetAll(ctx context.Context, customerId int64) (error, *[]model.BigcityVoucherModel) {
	db := config.GetConnectionCtx(ctx)
	var bigcityVoucherDetails []model.BigcityVoucherModel

	err := db.Where("customer_id = ?", customerId).Find(&bigcityVoucherDetails).Error
	if err != nil {
		return errors.New("Error while getting all vouchers from DB for customer id: " + 
		strconv.FormatInt(customerId, 10) + ": " + err.Error()), nil
	}

	return nil, &bigcityVoucherDetails
}
