package repo

import (
	"context"
	"errors"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/bigcity/model"
)

type StashLoyalityCashRepo interface {
	UpdateStashCash(context.Context, *model.StashLoyalityCash) error
	GetStashCash(context.Context, int64) (error, *model.StashLoyalityCash)
}

type StashLoyalityCashDB struct {
}

var stashLoyalityCashInstance StashLoyalityCashRepo = &StashLoyalityCashDB{}

func GetStashLoyalityCashRepo() StashLoyalityCashRepo {
	return stashLoyalityCashInstance
}

func (*StashLoyalityCashDB) UpdateStashCash(ctx context.Context, stashLoyalityCash *model.StashLoyalityCash) error {
	db := config.GetConnectionCtx(ctx)

	err := db.Save(stashLoyalityCash).Error
	if err != nil {
		return errors.New("Error while saving stashcash: " + err.Error())
	}

	return nil
}

func (*StashLoyalityCashDB) GetStashCash(ctx context.Context, customerId int64) (error, *model.StashLoyalityCash) {
	db := config.GetConnectionCtx(ctx)
	var stashLoyalityCash model.StashLoyalityCash

	err := db.Where("customer_id = ?", customerId).Order("id desc").First(
		&stashLoyalityCash).Error
	if err != nil {
		return errors.New("Error while getting data from stash loyality cash: " + err.Error()), nil
	}

	return nil, &stashLoyalityCash
}
