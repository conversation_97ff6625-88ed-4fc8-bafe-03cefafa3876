package service

import (
	"context"
	"time"

	"stashfin.com/stashfin/magneto/pkg/bigcity/repo"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/request"
)

type BigcityAuthInterface struct {
}

func (b *BigcityAuthInterface) GetUserAuthToken(ctx context.Context, customerId int64) (
	error, string) {
	bigcityUserAuth, err := repo.GetBigcityUserAuthRepo().Get(ctx, customerId)
	if err != nil {
		return err, ""
	} else if bigcityUserAuth.Id == 0 || !bigcityUserAuth.ExpiryTime.After(time.Now().Add(time.Hour)) {
		err, token := b.CreateUserAuthToken(ctx, customerId)
		if err != nil {
			return err, ""
		}
		return nil, token
	}

	return nil, bigcityUserAuth.Token
}

func (*BigcityAuthInterface) CreateUserAuthToken(ctx context.Context, customerId int64) (error, string) {
	err, phone := customer.GetCustomerService().GetCustomerPhone(ctx, customerId)
	if err != nil {
		return err, ""
	}

	bigcityUserAuthRequest := createBigcityUserAuthRequest(customerId, phone)

	err, bigcityUserAuthResponse := request.GetBigcityUserAuthtoken(bigcityUserAuthRequest, ctx)
	if err != nil {
		return err, ""
	}

	bigcityUserAuthModel, err := createBigcityUserAuthModel(customerId, bigcityUserAuthResponse)
	if err != nil {
		return err, ""
	}

	err = repo.GetBigcityUserAuthRepo().Save(ctx, bigcityUserAuthModel)
	if err != nil {
		return err, ""
	}

	return nil, bigcityUserAuthResponse.Token
}
