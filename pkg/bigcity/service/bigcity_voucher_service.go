package service

import (
	"context"
	"errors"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/bigcity/model"
	"stashfin.com/stashfin/magneto/pkg/bigcity/repo"
	"stashfin.com/stashfin/magneto/request"
)

type BigcityVoucherInterface struct {
	authService *BigcityAuthInterface
}

func NewBigcityVoucherService(authService *BigcityAuthInterface) *BigcityVoucherInterface {
	return &BigcityVoucherInterface{
		authService: authService,
	}
}

func (b *BigcityVoucherInterface) CreateBigCityVoucher(ctx context.Context, customerId int64) (
	error, *dtype.VoucherResponse) {

	err, isEligible, stashLoyalityCash := isEligibleForVoucher(ctx, customerId)
	if err != nil {
		return err, nil
	} else if !isEligible {
		return errors.New("Don't have enough stashcash for redeem voucher"), nil
	}

	err, authToken := b.authService.GetUserAuthToken(ctx, customerId)
	if err != nil {
		return err, nil
	}

	payload := createBigcityVoucherRequest()
	err, response := request.BigcityCreateVoucher(payload, authToken, ctx)
	if err != nil && err.Error() == "Token Expired" {
		err, authToken = b.authService.CreateUserAuthToken(ctx, customerId)
		if err != nil {
			return err, nil
		}
		err, response = request.BigcityCreateVoucher(payload, authToken, ctx)
	} else if err != nil {
		return err, nil
	}

	err, bigcityVoucherDetail := createBigcityVoucherModel(response, customerId)
	if err != nil {
		return err, nil
	}

	err = repo.GetBigcityVoucherDetailRepo().Save(ctx, bigcityVoucherDetail)
	if err != nil {
		return err, nil
	}

	newStashLoyalityCash := updateStashLoyalityCash(stashLoyalityCash)
	err = repo.GetStashLoyalityCashRepo().UpdateStashCash(ctx, newStashLoyalityCash)
	if err != nil {
		return err, nil
	}

	err, voucherResponse := createVoucherResponse(response, newStashLoyalityCash)

	return nil, voucherResponse
}

func isEligibleForVoucher(ctx context.Context, customerId int64) (error, bool, *model.StashLoyalityCash) {
	err, stashLoyalityCash := repo.GetStashLoyalityCashRepo().GetStashCash(ctx, customerId)
	if err != nil {
		return err, false, nil
	}

	if stashLoyalityCash.TotalCash < float32(config.GetConfigValues().VoucherEligibleStashcash) {
		return nil, false, nil
	}

	return nil, true, stashLoyalityCash
}

func (*BigcityVoucherInterface) GetVoucherHistory(ctx context.Context,
	customerId int64, page int, limit int, statusTypes []string) (error, *dtype.VoucherHistories) {
	err, bigcityVoucherDetails, has_next_page := repo.GetBigcityVoucherDetailRepo().Get(
		ctx, customerId, page, limit, statusTypes)
	if err != nil {
		return err, nil
	}

	err, voucherHistories, expiredVoucher := createVoucherHistories(bigcityVoucherDetails,
		has_next_page)
	if err != nil {
		return err, nil
	}

	repo.GetBigcityVoucherDetailRepo().MarkVouchersInactive(ctx, expiredVoucher,
		bigcityVoucherDetails)

	err = getAllVouchers(ctx, voucherHistories, customerId)
	if err != nil {
		return err, nil
	}

	return nil, voucherHistories
}

func getAllVouchers(ctx context.Context, voucherHistories *dtype.VoucherHistories, customerId int64) error {
	err, bigcityVoucherDetails := repo.GetBigcityVoucherDetailRepo().GetAll(ctx, customerId)
	if err != nil {
		return err
	}

	activeVouchers := 0
	expiredVouchers := 0
	for _, voucher := range *bigcityVoucherDetails {
		if voucher.Status == "active" {
			activeVouchers += 1
		} else {
			expiredVouchers += 1
		}
	}

	voucherHistories.ActiveVouchers = activeVouchers
	voucherHistories.ExpiredVouchers = expiredVouchers
	voucherHistories.TotalVouchers = activeVouchers + expiredVouchers
	return nil

}
