package service

import (
	"errors"
	"time"

	"github.com/google/uuid"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/bigcity/model"
	"stashfin.com/stashfin/magneto/util"
)

const BIG_CITY_NARRATION_TITLE = "Redeemed Stashcash"
const BIGCITY_NARRATION_ID = "40"
const STASH_LOYALTY_CASH_CATEGORY = 1

func createBigcityUserAuthRequest(customerId int64, phone string) *dtype.BigcityUserAuthRequest {
	var bigcityUserAuthRequest dtype.BigcityUserAuthRequest

	bigcityUserAuthRequest.APIKey = config.GetConfigValues().BigcityAPIKey
	bigcityUserAuthRequest.Username = config.GetConfigValues().BigcityAPIUsername
	bigcityUserAuthRequest.Secret = config.GetConfigValues().BigcityAPISecret
	bigcityUserAuthRequest.Mobile = phone

	return &bigcityUserAuthRequest
}

func createBigcityUserAuthModel(customerId int64,
	bigcityAuthUserResponse *dtype.BigcityUserAuthResponse) (*model.BigcityUserAuthModel, error) {
	var bigcityUserAuthModel model.BigcityUserAuthModel
	err, istTime := util.StringToIST(bigcityAuthUserResponse.ExpiryTime)
	if err != nil {
		return nil, errors.New("Error while converting timestring to IST, Error: " + err.Error())
	}

	bigcityUserAuthModel.CustomerId = customerId
	bigcityUserAuthModel.Token = bigcityAuthUserResponse.Token
	bigcityUserAuthModel.ExpiryTime = istTime

	return &bigcityUserAuthModel, nil
}

func createVoucherHistories(bigcityVoucherDetails *[]model.BigcityVoucherModel,
	has_next_page bool) (error,
	*dtype.VoucherHistories, []string) {
	var voucherHistories dtype.VoucherHistories
	var expiredVoucher []string

	voucherHistories.URL = config.GetConfigValues().BigcityVoucherRedeemptionUrl
	voucherHistories.HasNextPage = has_next_page
	for _, voucher := range *bigcityVoucherDetails {
		var voucherDetail dtype.VoucherDetail
		validityDate := voucher.Validity

		if validityDate.After(time.Now()) {
			voucherDetail.Status = "active"
		} else {
			voucherDetail.Status = "inactive"
			voucher.Status = "inactive"
			expiredVoucher = append(expiredVoucher, voucher.VoucherCode)
		}

		voucherDetail.Validity = util.ConvertDateIntoDateString(validityDate)
		voucherDetail.RedeemedStashcash = voucher.RedeemedStashcash
		voucherDetail.VoucherCode = voucher.VoucherCode
		voucherHistories.Vouchers = append(voucherHistories.Vouchers, voucherDetail)
	}

	return nil, &voucherHistories, expiredVoucher
}

func updateStashLoyalityCash(stashLoyalityCash *model.StashLoyalityCash) *model.StashLoyalityCash {
	var newStashLoyalityCash model.StashLoyalityCash
	newStashLoyalityCash.Cash = float32(config.GetConfigValues().VoucherEligibleStashcash) * -1
	newStashLoyalityCash.TotalCash = stashLoyalityCash.TotalCash - float32(config.GetConfigValues().VoucherEligibleStashcash)
	newStashLoyalityCash.Narration = BIG_CITY_NARRATION_TITLE
	newStashLoyalityCash.NarrationId = BIGCITY_NARRATION_ID
	newStashLoyalityCash.Category = STASH_LOYALTY_CASH_CATEGORY
	newStashLoyalityCash.CustomerId = stashLoyalityCash.CustomerId
	newStashLoyalityCash.Date = time.Now()

	return &newStashLoyalityCash
}

func createBigcityVoucherRequest() *dtype.BigcityVoucherCreateRequest {
	var req dtype.BigcityVoucherCreateRequest
	var lineItem dtype.LineItem

	lineItem.OrderId = uuid.NewString()
	lineItem.Quantity = 1
	lineItem.SKU = "SKU1"

	req.OrderTotal = config.GetConfigValues().VoucherEligibleStashcash
	req.OrderItems = 1
	req.LineItems = append(req.LineItems, lineItem)
	req.OrderTimestamp = util.TimeToString(time.Now())

	return &req
}

func createVoucherResponse(response *dtype.BigcityVoucherCreateResponse,
	stashCashModel *model.StashLoyalityCash) (error, *dtype.VoucherResponse) {
	var voucherResponse dtype.VoucherResponse
	stashcashEligibilty := config.GetConfigValues().VoucherEligibleStashcash

	voucherResponse.URL = config.GetConfigValues().BigcityVoucherRedeemptionUrl
	voucherResponse.Validity = response.Vouchers[0].Validity
	voucherResponse.VoucherCode = response.Vouchers[0].VoucherCode
	voucherResponse.TotalStashCash = int(stashCashModel.TotalCash)
	voucherResponse.RedeemableStashCashLimit = stashcashEligibilty
	if voucherResponse.TotalStashCash < stashcashEligibilty {
		voucherResponse.LockedStashCash = voucherResponse.TotalStashCash
		voucherResponse.RedeemableStashCash = 0
	} else {
		voucherResponse.LockedStashCash = voucherResponse.TotalStashCash - stashcashEligibilty
		voucherResponse.RedeemableStashCash = stashcashEligibilty
	}

	return nil, &voucherResponse
}

func createBigcityVoucherModel(res *dtype.BigcityVoucherCreateResponse, cusotmerId int64) (
	error, *model.BigcityVoucherModel) {
	var bigcityVoucherDetail model.BigcityVoucherModel

	bigcityVoucherDetail.CustomerId = cusotmerId

	err, validityDate := util.ConvertDateStringIntoDate(res.Vouchers[0].Validity)
	if err != nil {
		return errors.New("Error in converting Voucher Validity Into Date"), nil
	}

	bigcityVoucherDetail.Validity = validityDate
	bigcityVoucherDetail.OrderId = res.Vouchers[0].Id
	bigcityVoucherDetail.VoucherCode = res.Vouchers[0].VoucherCode
	bigcityVoucherDetail.Status = "active"
	bigcityVoucherDetail.RedeemedStashcash = config.GetConfigValues().VoucherEligibleStashcash

	return nil, &bigcityVoucherDetail
}
