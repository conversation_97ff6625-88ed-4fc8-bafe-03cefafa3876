package cli

import (
	"stashfin.com/stashfin/magneto/pkg/cli/repo"
	"stashfin.com/stashfin/magneto/pkg/cli/service"
)

var (
	serviceInstance *service.Service
)

func InitializeServices() {
	communicationLogRepo := repo.NewCommunicationLogRepo()
	cliOfferTemplateDataRepo := repo.NewCliOfferTemplateDataRepo()

	serviceInstance = service.NewService(
		service.NewRepo(
			communicationLogRepo,
			cliOfferTemplateDataRepo,
		),
	)
}

func GetService() *service.Service {
	return serviceInstance
}
