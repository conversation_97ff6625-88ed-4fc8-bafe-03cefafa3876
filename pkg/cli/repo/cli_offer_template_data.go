package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/cli/model"
)

type CliOfferTemplateDataRepo struct {}

func NewCliOfferTemplateDataRepo() *CliOfferTemplateDataRepo {
	return &CliOfferTemplateDataRepo{}
}

func (*CliOfferTemplateDataRepo) GetByCustomerId(ctx context.Context, customer_id int64) (
	*model.CliOfferTemplateDataModel, error) {
	db := config.GetConnectionCtx(ctx)
	var model model.CliOfferTemplateDataModel

	err := db.Where("customer_id = ?", customer_id).First(&model).Error
	if err != nil {
		return nil, err
	}

	return &model, nil
}
