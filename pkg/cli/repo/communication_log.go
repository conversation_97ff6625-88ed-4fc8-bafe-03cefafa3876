package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/cli/model"
)

const (
	CLI_TEMPLATE_ID = "cli_confirm%"
	SUCCESS         = 2
	CLI_START_DATE  = "2023-07-27"
)

type CommunicationLogRepo struct{}

func NewCommunicationLogRepo() *CommunicationLogRepo {
	return &CommunicationLogRepo{}
}

func (*CommunicationLogRepo) GetCliTemplateByCustomerId(ctx context.Context, customerId int64) (
	[]model.CommunicationLogModel, error) {
	var models []model.CommunicationLogModel
	db := config.GetConnectionCtx(ctx)

	err := db.Where("template_id like ? and customer_id = ? and is_success = ? and create_date > ?",
		CLI_TEMPLATE_ID, customerId, SUCCESS, CLI_START_DATE).Order("id DESC").Find(&models).Error
	if err != nil {
		return models, err
	}

	return models, err
}
