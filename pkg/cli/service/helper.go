package service

import "time"

func checkIfCliExpired(expiryDate time.Time) bool {

	expiryTime := time.Date(expiryDate.Year(), expiryDate.Month(), expiryDate.Day(), 0, 0, 0, 0, expiryDate.Location())
	currentTime := time.Now()
	currentDate := time.Date(currentTime.Year(), currentTime.Month(), currentTime.Day(), 0, 0, 0, 0, expiryDate.Location())

	if expiryTime.Before(currentDate) {
		return true
	}

	return false
}