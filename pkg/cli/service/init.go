package service

import "stashfin.com/stashfin/magneto/pkg/cli/repo"

type Repo struct {
	communicationLogRepo     *repo.CommunicationLogRepo
	cliOfferTemplateDataRepo *repo.CliOfferTemplateDataRepo
}

func NewRepo(
	communicationLogRepo *repo.CommunicationLogRepo,
	cliOfferTemplateDataRepo *repo.CliOfferTemplateDataRepo,
) *Repo {
	return &Repo {
		communicationLogRepo: communicationLogRepo,
		cliOfferTemplateDataRepo: cliOfferTemplateDataRepo,
	}
}

type Service struct {
	repo *Repo
}

func NewService(repo *Repo) *Service {
	return &Service{
		repo: repo,
	}
}
