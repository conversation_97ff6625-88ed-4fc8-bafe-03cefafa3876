package service

import (
	"context"
	"errors"
	"net/http"

	"gorm.io/gorm"
	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer"
)

const (
	SERVICE_DISABLED       = "You are not eligible for credit limit increase"
	CLI_NOT_ELIGIBLE_ERROR = "Your credit limit increase will have to wait, as you did not meet our internal criteria."
	CLI_USER_HOLD_ERROR    = "Looks like your credit limit was increased recently. Please wait for a couple of months before requesting again"
	CLI_SUCCESS_MESSAGE    = "Your credit limit has been increased to"
)

func (s *Service) SendOtp(ctx context.Context, mobile int) error {
	_, err := customer.GetCustomerService().GetIDFromPhone(ctx, mobile)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return helper.NewCustomError(CLI_NOT_ELIGIBLE_ERROR, http.StatusCreated)
	} else if err != nil {
		return err
	}

	return client.GetApiV2Client().RequestOTP(ctx, mobile)
}

func (s *Service) VerifyCli(ctx context.Context, mobile int, otp int) (*dtype.VerifyCliResponse, error) {

	var response dtype.VerifyCliResponse

	if !config.GetConfigValues().CliEnable {
		return nil, helper.NewCustomError(SERVICE_DISABLED, http.StatusCreated)
	}

	customerId, err := customer.GetCustomerService().GetIDFromPhone(ctx, mobile)
	if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, helper.NewCustomError(CLI_NOT_ELIGIBLE_ERROR, http.StatusCreated)
	} else if err != nil {
		return nil, err
	}

	_, err = client.GetApiV2Client().VerifyOTP(ctx, mobile, otp)
	if err != nil {
		return &response, err
	}

	response.IsOtpVerified = true
	err = s.checkCliEligible(ctx, customerId)
	if err != nil {
		return &response, err
	}
	response.IsCliEligible = true

	isCliUpdated, isArmyCustomer, sentinalIncreasedLimit, isSentinalBase, err := customer.GetCustomerService().UpdateCustomerCliFLag(ctx, customerId)
	if err != nil {
		return &response, err
	}

	if isCliUpdated {
		isLocUpdated, updatedLoc, err := customer.GetCustomerService().UpdateLocLimit(ctx, customerId, isArmyCustomer, sentinalIncreasedLimit, isSentinalBase)
		if err != nil {
			return &response, err
		}

		if isLocUpdated {
			response.Message = CLI_SUCCESS_MESSAGE
			response.Amount = updatedLoc
			return &response, nil
		} else {
			return &response, helper.NewCustomError(CLI_USER_HOLD_ERROR, http.StatusCreated)
		}
	}

	return &response, helper.NewCustomError("Uh Oh !!The offer is not available to you.", http.StatusCreated)
}

func (s *Service) checkCliEligible(ctx context.Context, customerId int64) error {
	communicationLogModels, err := s.repo.communicationLogRepo.GetCliTemplateByCustomerId(ctx, customerId)
	if err != nil {
		return err
	}

	if len(communicationLogModels) > 0 {
		cliOfferTemplateDataModel, err := s.repo.cliOfferTemplateDataRepo.GetByCustomerId(ctx, customerId)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		} else if err != nil {
			return helper.NewCustomError(CLI_NOT_ELIGIBLE_ERROR, http.StatusCreated)
		}

		if checkIfCliExpired(cliOfferTemplateDataModel.BaseExpiry) {
			return helper.NewCustomError(CLI_NOT_ELIGIBLE_ERROR, http.StatusCreated)
		}
		return nil
	}

	return helper.NewCustomError(CLI_NOT_ELIGIBLE_ERROR, http.StatusCreated)
}
