package customer

import (
	"context"
	"time"

	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
	"stashfin.com/stashfin/magneto/pkg/customer/service"
)

type CustomerService interface {
	GetCustomerPhone(context.Context, int64) (error, string)
	GetIDFromPhone(ctx context.Context, phone int) (int64, error)
	GetCustomerDetail(context.Context, int64) (*dtype.CustomerDetailData, error)
	GetCustomerData(context.Context, int64) (*model.CustomerDetailModel, error)
	CreateCustomerWithPhoneAndName(ctx context.Context, firstName string, phoneNumber, source string) (int64, error)
	GetLatestDeviceIdByTime(context.Context, int64, time.Time) (string, error)
	UpdateCustomerCliFLag(context.Context, int64) (bool, bool, int, bool, error)
	UpdateLocLimit(context.Context, int64, bool, int, bool) (bool, int, error)
	ValidateUserOTP(ctx context.Context, userID int64, otp int) error
	CreateCustomerLead(ctx context.Context, customerID int64, fields map[string]interface{}) error
	GetLeadDetail(ctx context.Context, customerId, leadTypeId int64) (*dtype.LeadDetailResponse, error)
	IsOfferCustomer(ctx context.Context, customerId int64) (bool, error)
	IsCustomerLOCOpen(ctx context.Context, customerId int64) (bool, error)
	UpdateUTM(ctx context.Context, customerID int64, utm string) error
	IsCustomerOfferDiscountEligible(ctx context.Context, customerID int64) (bool, error)
	UpdateCustomerOfferDiscountClicked(ctx context.Context, customerId int64, status bool) error
	GetOkycCompletedStatus(ctx context.Context, customerId int64) (bool, error)
}

type UpiService interface {
	Save(context.Context, *dtype.UpiStatusDetail, int64) error
}

var (
	customerServiceInstance CustomerService = &service.CustomerInterface{}
	upiInstance             UpiService      = &service.UpiInterface{}
)

func GetCustomerService() CustomerService {
	return customerServiceInstance
}

func GetUpiService() UpiService {
	return upiInstance
}
