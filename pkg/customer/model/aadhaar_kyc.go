package model

import (
	"time"

	"stashfin.com/stashfin/magneto/dtype"
)

type AadhaarKycModel struct {
	Id            int64            `gorm:"column:id"`
	CustomerId    int64            `gorm:"column:customer_id"`
	TransactionId string           `gorm:"column:transaction_id"`
	Url           string           `gorm:"column:url"`
	Payload       string           `gorm:"column:payload;default:null"`
	ReturnData    string           `gorm:"column:return_data;default:null"`
	Status        dtype.OkycStatus `gorm:"column:status"`
	CreateDate    time.Time        `gorm:"column:create_date"`
	UpdateDate    time.Time        `gorm:"column:update_date;default:null"`
	IsXmlDone     int              `gorm:"column:is_xml_done"`
}

func (AadhaarKycModel) TableName() string {
	return "st_customer_aadhaar_kyc"
}
