package model

import "time"

type CustomerDetailModel struct {
	CustomerId int64     `gorm:"column:id"`
	Phone      string    `gorm:"column:phone"`
	FirstName  string    `gorm:"column:first_name"`
	MiddleName string    `gorm:"column:middle_name"`
	LastName   string    `gorm:"column:last_name"`
	Email      string    `gorm:"column:email"`
	DOB        time.Time `gorm:"column:dob"`
	PinCode    string    `gorm:"column:pin_code"`
	Gender     string    `gorm:"column:gender"`
	Source     string    `gorm:"column:source"`
}

func (CustomerDetailModel) TableName() string {
	return "st_customer_detail"
}

func (c *CustomerDetailModel) GetGender() string {
	switch c.Gender {
	case "m":
		return "male"
	case "f":
		return "female"
	default:
		return ""
	}
}

func (c *CustomerDetailModel) GetFullName() string {
	name := ""
	if c.FirstName != "" {
		name += c.FirstName
	}

	if c.MiddleName != "" {
		name += " " + c.<PERSON>Name
	}

	if c.LastName != "" {
		name += " " + c.<PERSON>Name
	}

	return name
}
