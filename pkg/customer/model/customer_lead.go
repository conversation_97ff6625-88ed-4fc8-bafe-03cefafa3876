package model

type CustomerLead struct {
	ID          int64     `gorm:"primaryKey;column:id;autoIncrement"`
	CustomerID  int64     `gorm:"column:customer_id;not null"`
	Name        string    `gorm:"column:name;size:100"`
	PhoneNumber string    `gorm:"column:phone_number;size:20"`
	Email       string    `gorm:"column:email;size:50"`
	LeadType    *LeadType `gorm:"foreignKey:lead_type_id"`
	LeadTypeID  int64     `gorm:"column:lead_type_id"`
	UTMSource   string    `gorm:"column:utm_source"`
	UTMMedium   string    `gorm:"column:utm_medium"`
	UTMCampaign string    `gorm:"column:utm_campaign"`
}

func (*CustomerLead) TableName() string {
	return "customer_lead"
}
