package model

import "time"

type CustomerOTP struct {
	ID           uint64     `gorm:"column:id;primaryKey"`
	CustomerID   int64      `gorm:"column:customer_id"`
	Login        int        `gorm:"column:login"`
	BlockedUntil *time.Time `gorm:"column:blocked_until"`
	WrongAttempt int        `gorm:"column:wrong_attempt"`
	LoginExpire  *time.Time `gorm:"column:login_expire"`
}

func (*CustomerOTP) TableName() string {
	return "st_customer_otps"
}
