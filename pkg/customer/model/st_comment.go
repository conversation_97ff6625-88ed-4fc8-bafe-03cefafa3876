package model

import "time"

type StCommentModel struct {
	LoanId       int64     `gorm:"column:loan_id"`
	CustomerId   int64     `gorm:"column:customer_id"`
	StatusTypeId int       `gorm:"column:status_type_id"`
	StatusId     int       `gorm:"column:status_id"`
	Comment      string    `gorm:"column:comment"`
	AddUserId    int       `gorm:"column:add_user_id"`
	CreatedDate  time.Time `gorm:"column:create_date"`
}

func (StCommentModel) TableName() string {
	return "st_comment"
}
