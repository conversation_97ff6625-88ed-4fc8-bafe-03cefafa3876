package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
)

type CustomerAddressRepo struct {
}

var customerAddressRepo *CustomerAddressRepo = &CustomerAddressRepo{}

func GetCustomerAddressRepo() *CustomerAddressRepo {
	return customerAddressRepo
}

func (*CustomerAddressRepo) GetCurrentAddress(ctx context.Context, userID int64) (*model.CustomerAddress, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.CustomerAddress
	err := db.WithContext(ctx).
		Where(&model.CustomerAddress{UserID: userID, AddressID: model.CurrentAddress}).
		Last(&obj).Error

	if err != nil {
		return nil, err
	}

	return &obj, nil
}
