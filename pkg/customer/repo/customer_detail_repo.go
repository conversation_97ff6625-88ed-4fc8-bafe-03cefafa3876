package repo

import (
	"context"
	"errors"
	"net/http"
	"strconv"

	"gorm.io/gorm"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
)

type CustomerDetailRepo interface {
	Save(context.Context, *model.CustomerDetailModel) error
	Get(context.Context, int64) (error, *model.CustomerDetailModel)
	GetByPhone(ctx context.Context, phone int) (*model.CustomerDetailModel, error)
	CreateWithPhoneAndName(ctx context.Context, firstName string, phoneNumber, source string) (*model.CustomerDetailModel, error)
	Update(ctx context.Context, obj *model.CustomerDetailModel, updateFields []string) error
}

type CustomerDetailDB struct{}

var customerDetailRepoInstance CustomerDetailRepo = &CustomerDetailDB{}

func GetCustomerDetailRepo() CustomerDetailRepo {
	return customerDetailRepoInstance
}

func (*CustomerDetailDB) Save(ctx context.Context,
	customerDetailModel *model.CustomerDetailModel) error {
	// db := config.GetConnectionCtx(ctx)

	var err error
	if err != nil {
		return errors.New("Error while saving data in st_customer_detail: " + err.Error())
	}

	return nil
}

func (*CustomerDetailDB) Get(ctx context.Context, customerId int64) (error, *model.CustomerDetailModel) {
	db := config.GetConnectionCtx(ctx)
	var customerDetail model.CustomerDetailModel

	err := db.Where("id = ?", customerId).First(&customerDetail).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return helper.NewCustomError("customer not present", http.StatusBadRequest), nil
		}
		return errors.New("Error while getting data from st_customer_detail for customer_id: " + strconv.FormatInt(customerId, 10) + ": " + err.Error()), nil
	}

	return nil, &customerDetail
}

func (*CustomerDetailDB) GetByPhone(ctx context.Context, phone int) (*model.CustomerDetailModel, error) {
	db := config.GetConnectionCtx(ctx)
	if db == nil {
		return nil, errors.New("database connection is nil")
	}
	var obj model.CustomerDetailModel
	err := db.WithContext(ctx).
		Where(&model.CustomerDetailModel{Phone: strconv.Itoa(phone)}).
		First(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

// This is only for insurance non-register customer only. Please don't use this.
func (*CustomerDetailDB) CreateWithPhoneAndName(ctx context.Context, firstName string, phoneNumber, source string) (*model.CustomerDetailModel, error) {
	db := config.GetConnectionCtx(ctx)

	obj := model.CustomerDetailModel{
		FirstName: firstName,
		Phone:     phoneNumber,
		Source:    source,
	}

	err := db.WithContext(ctx).Create(&obj).Error
	if err != nil {
		return nil, err
	}
	return &obj, nil
}

func (*CustomerDetailDB) Update(ctx context.Context, obj *model.CustomerDetailModel, updateFields []string) error {
	return helper.GenericUpdate(ctx, obj, updateFields)
}
