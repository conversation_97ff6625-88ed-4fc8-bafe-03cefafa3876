package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
)

type CustomerFlagsRepo interface {
	GetByCustomerId(context.Context, int64) (*model.CustomerFlagsModel, error)
	UpdateCliOtp(context.Context, int64, int) error
	UpdateCustomerOfferDiscountClicked(context.Context, int64, bool) error
}

type CustomerFlagsDB struct{}

var customerFlagsInstance CustomerFlagsRepo = &CustomerFlagsDB{}

func GetCustomerFlagsRepo() CustomerFlagsRepo {
	return customerFlagsInstance
}

func (*CustomerFlagsDB) GetByCustomerId(ctx context.Context, customerId int64) (*model.CustomerFlagsModel, error) {
	var model model.CustomerFlagsModel
	db := config.GetConnectionCtx(ctx)

	err := db.Where("customer_id = ?", customerId).First(&model).Error
	if err != nil {
		return nil, err
	}

	return &model, nil
}

func (*CustomerFlagsDB) UpdateCliOtp(ctx context.Context, customerId int64, cliOtp int) error {
	db := config.GetConnectionCtx(ctx)

	err := db.Model(&model.CustomerFlagsModel{}).Where("customer_id = ?", customerId).Update(
		"cli_verify_otp_eligible", cliOtp).Error
	if err != nil {
		return err
	}

	return nil
}

func (*CustomerFlagsDB) UpdateCustomerOfferDiscountClicked(ctx context.Context, customerId int64, status bool) error {
	db := config.GetConnectionCtx(ctx)
	err := db.Model(&model.CustomerFlagsModel{}).
		Where("customer_id = ?", customerId).Update(
		"is_offer_discount_clicked", status).Error

	return err
}
