package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
)

type CustomerLeadRepo struct{}

var customerLeadRepo *CustomerLeadRepo = &CustomerLeadRepo{}

func GetCustomerLeadRepo() *CustomerLeadRepo {
	return customerLeadRepo
}

func (r *CustomerLeadRepo) CreateWithMapping(
	ctx context.Context,
	fields map[string]interface{},
) error {
	db := config.GetConnectionCtx(ctx)
	err := db.WithContext(ctx).
		Model(&model.CustomerLead{}).
		Create(fields).Error
	if err != nil {
		return err
	}
	return nil
}

func (r *CustomerLeadRepo) Exists(ctx context.Context, customerID, leadTypeID int64) (bool, error) {
	db := config.GetConnectionCtx(ctx)

	var count int64
	err := db.WithContext(ctx).
		Model(&model.CustomerLead{}).
		Where("customer_id = ? AND lead_type_id = ?", customerID, leadTypeID).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}
