package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
)

type CustomerOTP struct{}

var customerOTPInstance = &CustomerOTP{}

func GetCustomerOTPRepo() *CustomerOTP {
	return customerOTPInstance
}

func (*CustomerOTP) GetByCustomerID(ctx context.Context, customerID int64) (*model.CustomerOTP, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.CustomerOTP
	err := db.WithContext(ctx).
		Where(&model.CustomerOTP{CustomerID: customerID}).
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}
