package repo

import (
	"context"
	"time"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
)

type DeviceLoginLogsRepo interface {
	GetLatestDeviceIdByTimeAndCustomerId(context.Context, time.Time, int64) (string, error)
}

type DeviceLoginLogsDB struct {}

var deviceLoginLogsRepoInstance DeviceLoginLogsRepo = &DeviceLoginLogsDB{}

func GetDeviceLoginLogsRepo() DeviceLoginLogsRepo {
	return deviceLoginLogsRepoInstance
}

func (*DeviceLoginLogsDB) GetLatestDeviceIdByTimeAndCustomerId(ctx context.Context, time time.Time, customerId int64) (
	string, error){
	db := config.GetConnectionCtx(ctx)
	var model model.DeviceLoginLogs

	err := db.Where("customer_id = ? and create_date <= ?", customerId, time).Order("id DESC").First(&model).Error
	if err != nil {
		return "", err
	}

	return model.DeviceId, nil
}