package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
)

type LeadTypeRepo struct{}

var leadTypeRepo *LeadTypeRepo = &LeadTypeRepo{}

func GetLeadTypeRepo() *LeadTypeRepo {
	return leadTypeRepo
}

func (r *LeadTypeRepo) GetLeadTypeByID(ctx context.Context, leadTypeID int64) (*model.LeadType, error) {
	db := config.GetConnectionCtx(ctx)

	var leadType model.LeadType
	if err := db.WithContext(ctx).First(&leadType, "id = ?", leadTypeID).Error; err != nil {
		return nil, err
	}

	return &leadType, nil
}
