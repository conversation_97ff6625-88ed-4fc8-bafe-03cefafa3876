package repo

import (
	"context"
	"time"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
)

type LocCustomersRepo interface {
	GetByCustomerId(context.Context, int64) (*model.LocCustomersModel, error)
	UpdatLocLimit(context.Context, int64, float32, float32) error
}

type LocCustomersDB struct{}

var locCustomersInstance LocCustomersRepo = &LocCustomersDB{}

func GetLocCustomersRepo() LocCustomersRepo {
	return locCustomersInstance
}

func (*LocCustomersDB) GetByCustomerId(ctx context.Context, customerId int64) (*model.LocCustomersModel, error) {
	db := config.GetConnectionCtx(ctx)
	var model model.LocCustomersModel

	err := db.Where("customer_id = ?", customerId).First(&model).Error
	if err != nil {
		return nil, err
	}

	return &model, nil
}

func (*LocCustomersDB) UpdatLocLimit(ctx context.Context, customerId int64, locLimit float32, maxLocLimit float32) error {
	db := config.GetConnectionCtx(ctx)
	updatedModel := model.LocCustomersModel{
		LocLimit:    locLimit,
		MaxLocLimit: maxLocLimit,
		UpdateDate:  time.Now(),
	}

	err := db.Model(&model.LocCustomersModel{}).Where("customer_id = ?", customerId).Updates(updatedModel).Error
	if err != nil {
		return err
	}

	return nil
}
