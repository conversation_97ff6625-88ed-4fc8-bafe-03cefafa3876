package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
)

type OkycRepo struct{}

func NewOkycRepo() *OkycRepo {
	return &OkycRepo{}
}

func (*OkycRepo) GetOkycCompletedRow(ctx context.Context, customerId int64) (*model.AadhaarKycModel, error) {
	db := config.GetConnectionCtx(ctx)
	var okyc model.AadhaarKycModel

	err := db.WithContext(ctx).Where("customer_id = ? and status = ?", customerId, dtype.OkycDoneStatus).Last(&okyc).Error
	if err != nil {
		return nil, err
	}
	return &okyc, nil
}
