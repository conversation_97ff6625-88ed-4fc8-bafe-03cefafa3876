package repo

import (
	"context"
	"time"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
)

type SentinalCliBaseRepo interface {
	GetByCustomerId(context.Context, int64) (*model.SentinalCliBaseModel, error)
	UpdateOtpDetail(context.Context, int64) error
}

type SentinalCliBaseDB struct{}

var sentinalCliBaseInstance SentinalCliBaseRepo = &SentinalCliBaseDB{}

func GetSentinaCliBaseRepo() SentinalCliBaseRepo {
	return sentinalCliBaseInstance
}

func (*SentinalCliBaseDB) GetByCustomerId(ctx context.Context, customerId int64) (*model.SentinalCliBaseModel, error) {
	db := config.GetConnectionCtx(ctx)
	var model model.SentinalCliBaseModel

	err := db.Where("customer_id = ?", customerId).First(&model).Error
	if err != nil {
		return nil, err
	}

	return &model, nil
}

func (*SentinalCliBaseDB) UpdateOtpDetail(ctx context.Context, customerId int64) error {
	db := config.GetConnectionCtx(ctx)
	updatedModel := model.SentinalCliBaseModel{
		OtpDone: 1,
		OtpVerifyDate: time.Now(),
	}

	err := db.Model(&model.SentinalCliBaseModel{}).Where("customer_id = ?", customerId).Updates(updatedModel).Error
	if err != nil {
		return err
	}

	return nil
}
