package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
)

type StCommentRepo interface{
	Save(context.Context, *model.StCommentModel) error
}

type StCommentDB struct {}

var stCommentInstance StCommentRepo = &StCommentDB{}

func GetStCommentRepo() StCommentRepo {
	return stCommentInstance
}

func (*StCommentDB) Save(ctx context.Context, model *model.StCommentModel) error {
	db := config.GetConnectionCtx(ctx)

	err := db.Create(model).Error
	if err != nil {
		return err
	}

	return nil
}