package service

import (
	"context"
	"errors"
	"math"
	"net/http"
	"strconv"
	"time"

	"gorm.io/gorm"

	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
	"stashfin.com/stashfin/magneto/pkg/customer/repo"
	"stashfin.com/stashfin/magneto/pkg/loan"
	"stashfin.com/stashfin/magneto/util"
)

type CustomerInterface struct{}

func (*CustomerInterface) GetCustomerPhone(ctx context.Context, customerId int64) (error, string) {
	err, customerDetails := repo.GetCustomerDetailRepo().Get(ctx, customerId)
	if err != nil {
		return err, ""
	}

	return nil, customerDetails.Phone
}

func (*CustomerInterface) GetIDFromPhone(ctx context.Context, phone int) (int64, error) {
	obj, err := repo.GetCustomerDetailRepo().GetByPhone(ctx, phone)
	if err != nil {
		return 0, err
	}

	return obj.CustomerId, nil
}

func (*CustomerInterface) GetCustomerDetail(ctx context.Context, customerId int64) (*dtype.CustomerDetailData, error) {
	err, customerDetails := repo.GetCustomerDetailRepo().Get(ctx, customerId)
	if err != nil {
		return nil, err
	}

	customerAddressDetail, err := repo.GetCustomerAddressRepo().GetCurrentAddress(ctx, customerId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil {
		customerAddressDetail = &model.CustomerAddress{}
	}

	dob := ""
	if !customerDetails.DOB.IsZero() {
		dob = customerDetails.DOB.Format("02-01-2006")
	}

	pincode := ""
	if customerAddressDetail.Pin != 0 {
		pincode = strconv.Itoa(customerAddressDetail.Pin)
	}

	return &dtype.CustomerDetailData{
		Gender:      customerDetails.GetGender(),
		Name:        customerDetails.GetFullName(),
		DateOfBirth: dob,
		Address:     customerAddressDetail.Address,
		Pincode:     pincode,
		Email:       customerDetails.Email,
	}, nil
}
func (*CustomerInterface) GetCustomerData(ctx context.Context, customerId int64) (*model.CustomerDetailModel, error) {
	err, customerData := repo.GetCustomerDetailRepo().Get(ctx, customerId)
	if err != nil {
		return nil, err
	}
	return customerData, nil
}

// This is only for insurance non-register customer only. Please don't use this.
func (*CustomerInterface) CreateCustomerWithPhoneAndName(ctx context.Context, firstName string, phoneNumber, source string) (int64, error) {
	customerDetail, err := repo.GetCustomerDetailRepo().CreateWithPhoneAndName(ctx, firstName, phoneNumber, source)
	if err != nil {
		return 0, err
	}

	return customerDetail.CustomerId, nil
}

func (*CustomerInterface) GetLatestDeviceIdByTime(ctx context.Context, customerId int64, time time.Time) (string, error) {
	deviceId, err := repo.GetDeviceLoginLogsRepo().GetLatestDeviceIdByTimeAndCustomerId(ctx, time, customerId)
	if err != nil {
		return "", err
	}

	return deviceId, nil
}

func (*CustomerInterface) UpdateCustomerCliFLag(ctx context.Context, customerId int64) (bool, bool, int, bool, error) {
	var cliVerifyOtpEligible int
	isCliUpdated := false

	customerFlagsModel, err := repo.GetCustomerFlagsRepo().GetByCustomerId(ctx, customerId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return false, false, 0, false, err
	}

	cliVerifyOtpEligible = customerFlagsModel.CliVerifyOtpEligible

	isArmyCustomer, err := loan.GetService().CheckArmyCustomer(ctx, customerId)
	if err != nil {
		return false, false, 0, false, err
	}

	isSentinalBase, sentinalIncreasedLimit, err := checkSentinalCliBaseCustomer(ctx, customerId)
	if err != nil {
		return false, false, 0, false, err
	}

	if (isSentinalBase && isArmyCustomer) || !isArmyCustomer {
		cliFlag := cliVerifyOtpEligible + 1
		err := repo.GetCustomerFlagsRepo().UpdateCliOtp(ctx, customerId, cliFlag)
		if err != nil {
			return false, isArmyCustomer, sentinalIncreasedLimit, isSentinalBase, err
		}
		isCliUpdated = true

		if isArmyCustomer {
			err := repo.GetSentinaCliBaseRepo().UpdateOtpDetail(ctx, customerId)
			if err != nil {
				return false, isArmyCustomer, sentinalIncreasedLimit, isSentinalBase, err
			}
		}
	}

	return isCliUpdated, isArmyCustomer, sentinalIncreasedLimit, isSentinalBase, nil
}

func (*CustomerInterface) UpdateLocLimit(ctx context.Context, customerId int64, isArmyCustomer bool,
	sentinalIncreasedLimit int, isSentinalBase bool,
) (bool, int, error) {
	isLocLimitUpdated := false

	customerFlagsModel, err := repo.GetCustomerFlagsRepo().GetByCustomerId(ctx, customerId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return isLocLimitUpdated, 0, err
	} else if err != nil {
		return isLocLimitUpdated, 0, nil
	}

	if customerFlagsModel.CliVerifyOtpEligible == 1 {
		locCustomersModel, err := repo.GetLocCustomersRepo().GetByCustomerId(ctx, customerId)
		if err != nil {
			return isLocLimitUpdated, 0, err
		}

		increasedLimit := locCustomersModel.LocLimit * LocIncrementPercentage
		newLocLimit := increasedLimit + locCustomersModel.LocLimit

		if increasedLimit > CliIncrementLimit {
			newLocLimit = locCustomersModel.LocLimit + CliIncrementLimit
		}

		if isArmyCustomer {
			if isSentinalBase {
				newLocLimit = float32(sentinalIncreasedLimit)
			} else {
				return false, 0, nil
			}
		}

		updatedLocLimit := int(math.Round(float64(newLocLimit)))
		maxLocLimit := locCustomersModel.MaxLocLimit
		if maxLocLimit < newLocLimit {
			maxLocLimit = newLocLimit
		}

		err = repo.GetLocCustomersRepo().UpdatLocLimit(ctx, customerId, newLocLimit, maxLocLimit)
		if err != nil {
			return isLocLimitUpdated, 0, err
		}
		isLocLimitUpdated = true

		maxLoanId, err := loan.GetService().GetMaxLoanId(ctx, customerId)
		if err != nil {
			return isLocLimitUpdated, 0, err
		}

		stCommentModel := &model.StCommentModel{
			LoanId:       maxLoanId,
			CustomerId:   customerId,
			StatusTypeId: CliIncrementCommentStatusTypeID,
			StatusId:     CliIncrementCommentStatusID,
			Comment:      CliIncrementComment,
			AddUserId:    SystemID,
			CreatedDate:  time.Now(),
		}

		err = repo.GetStCommentRepo().Save(ctx, stCommentModel)
		if err != nil {
			return isLocLimitUpdated, 0, err
		}
		templateData := map[string]any{
			"limit": updatedLocLimit,
		}

		err = client.RedisStreamPush(ctx, customerId, "inform_cli_increase", templateData)
		if err != nil {
			return isLocLimitUpdated, updatedLocLimit, err
		}

		return isLocLimitUpdated, updatedLocLimit, nil
	}

	return isLocLimitUpdated, 0, nil
}

func (s *CustomerInterface) ValidateUserOTP(ctx context.Context, userID int64, otp int) error {
	otpObj, err := repo.GetCustomerOTPRepo().GetByCustomerID(ctx, userID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	if err != nil {
		return errors.New("entry not found for otp")
	}

	if otpObj.LoginExpire != nil && otpObj.LoginExpire.Before(time.Now()) {
		return helper.NotFoundError("OTP has expired!")
	}

	if otpObj.Login != otp {
		return helper.NotFoundError("Invalid OTP!")
	}

	return nil
}

func (s *CustomerInterface) CanCreateLead(ctx context.Context, customerId, leadTypeId int64) (bool, error) {
	leadObj, err := repo.GetLeadTypeRepo().GetLeadTypeByID(ctx, leadTypeId)
	if err != nil {
		return false, err
	}

	if leadObj.MultiSubmit {
		return true, nil
	}

	exist, err := repo.GetCustomerLeadRepo().Exists(ctx, customerId, leadTypeId)
	if err != nil {
		return false, err
	}

	return !exist, nil
}

func (s *CustomerInterface) GetLeadDetail(ctx context.Context, customerId, leadTypeId int64) (*dtype.LeadDetailResponse, error) {
	logger := util.CLogCtx(ctx)

	canCreateLead, err := s.CanCreateLead(ctx, customerId, leadTypeId)
	if err != nil {
		logger.Info("error in can create lead", err)
	}

	if !canCreateLead {
		return &dtype.LeadDetailResponse{
			IsRequired:    false,
			PreFilledData: nil,
		}, nil
	}

	err, customerDetails := repo.GetCustomerDetailRepo().Get(ctx, customerId)
	if err != nil {
		return nil, err
	}

	return &dtype.LeadDetailResponse{
		IsRequired: true,
		PreFilledData: &dtype.PrefilledData{
			Name:  customerDetails.GetFullName(),
			Email: customerDetails.Email,
		},
	}, nil
}

func (s *CustomerInterface) CreateCustomerLead(ctx context.Context, customerID int64, fields map[string]interface{}) error {
	logger := util.CLogCtx(ctx)
	canCreateLead, err := s.CanCreateLead(ctx, customerID, fields["lead_type_id"].(int64))
	if err != nil {
		logger.Info("error in can create lead", err)
	}

	if !canCreateLead {
		return helper.NewCustomError("response already submitted", http.StatusBadRequest)
	}

	err, customerDetails := repo.GetCustomerDetailRepo().Get(ctx, customerID)
	if err != nil {
		return err
	}

	fields["phone_number"] = customerDetails.Phone
	err = repo.GetCustomerLeadRepo().CreateWithMapping(ctx, fields)
	return err
}

func (s *CustomerInterface) IsOfferCustomer(ctx context.Context, customerId int64) (bool, error) {
	err, customerDetail := repo.GetCustomerDetailRepo().Get(ctx, customerId)
	if err != nil {
		return false, err
	}

	return customerDetail.Source == "offer", nil
}

func (s *CustomerInterface) IsCustomerLOCOpen(ctx context.Context, customerId int64) (bool, error) {
	locCustomer, err := repo.GetLocCustomersRepo().GetByCustomerId(ctx, customerId)
	if err != nil {
		return false, err
	}

	return locCustomer.CanReload, nil
}

func (s *CustomerInterface) UpdateUTM(ctx context.Context, customerID int64, utm string) error {
	err, obj := repo.GetCustomerDetailRepo().Get(ctx, customerID)
	if err != nil {
		return err
	}

	obj.Source = utm
	return repo.GetCustomerDetailRepo().Update(ctx, obj, []string{"source"})
}

func (s *CustomerInterface) IsCustomerOfferDiscountEligible(ctx context.Context, customerID int64) (bool, error) {

	customerFlags, err := repo.GetCustomerFlagsRepo().GetByCustomerId(ctx, customerID)
	if err != nil {
		return false, err
	}

	return customerFlags.IsOfferDiscountClicked && customerFlags.IsElev8Libr8, nil
}

func (s *CustomerInterface) UpdateCustomerOfferDiscountClicked(ctx context.Context, customerId int64, status bool) error {
	return repo.GetCustomerFlagsRepo().UpdateCustomerOfferDiscountClicked(ctx, customerId, status)
}

func (s *CustomerInterface) GetOkycCompletedStatus(ctx context.Context, customerId int64) (bool, error) {
	_, err := repo.NewOkycRepo().GetOkycCompletedRow(ctx, customerId)
	if util.IsNotFoundError(err) {
		return false, nil
	}
	if err != nil {
		return false, err
	}

	return true, nil
}
