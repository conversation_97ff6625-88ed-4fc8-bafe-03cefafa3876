package service

import (
	"context"
	"errors"

	"gorm.io/gorm"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/customer/model"
	"stashfin.com/stashfin/magneto/pkg/customer/repo"
)

func convertIntoUpiStatusModel(upiStatusDetail *dtype.UpiStatusDetail,
	customerId int64) *model.CustomerDetailModel {
	var customerDetailModel model.CustomerDetailModel

	customerDetailModel.CustomerId = customerId
	// customerDetailModel.UpiProfileId = upiStatusDetail.ProfileId
	// customerDetailModel.UpiId = upiStatusDetail.UpiId
	// customerDetailModel.Status = upiStatusDetail.UpiStatus

	return &customerDetailModel
}

func checkSentinalCliBaseCustomer(ctx context.Context, customerId int64) (bool, int, error) {
	sentinalCliBaseModel, err := repo.GetSentinaCliBaseRepo().GetByCustomerId(ctx, customerId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound){
		return false, 0, err
	} else if err != nil {
		return false, 0, nil
	}

	return true, sentinalCliBaseModel.IncreasedLimit, nil
}

