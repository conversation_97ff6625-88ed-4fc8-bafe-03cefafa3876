package service

import (
	"context"

	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/customer/repo"
)

type UpiInterface struct {
}

func (*UpiInterface) Save(ctx context.Context, upiStatusDetail *dtype.UpiStatusDetail,
	customerId int64) error {
	upiStatusModel := convertIntoUpiStatusModel(upiStatusDetail, customerId)

	err := repo.GetCustomerDetailRepo().Save(ctx, upiStatusModel)
	if err != nil {
		return err
	}

	return nil
}
