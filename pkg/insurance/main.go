package insurance

import (
	"stashfin.com/stashfin/magneto/pkg/insurance/repo"
	"stashfin.com/stashfin/magneto/pkg/insurance/service"
)

var (
	serviceInstance *service.Service
)

func InitializeServices() {
	insuranceProductRepo := repo.NewIsuranceProductRepo()
	cmsDataRepo := repo.NewCMSDataRepo()
	insuranceProductDetailRepo := repo.NewIsuranceProductDetailsRepo()
	insuranceApplicationRepo := repo.NewIsuranceApplicationRepo()
	insuranceApplicationExtraDetailsRepo := repo.NewInsuranceApplicationExtraDetailsRepo()
	insuranceLeadRepo := repo.NewIsuranceLeadRepo()
	insurancePaymentsRepo := repo.NewInsurancePaymentsRepo()
	insurrancePartnerResponseRepo := repo.NewInsurancePartnerAPIResponse()

	feedbackQuestionRepo := repo.NewInsuranceFeedbackQuestion()
	feedbackQuestionOptionRepo := repo.NewInsuranceFeedbackQuestionOptionn()
	feedbackResponseRepo := repo.NewInsuranceFeedbackResponse()
	formFieldRepo := repo.NewFormFieldRepo()

	insuranceTopologyRepo := repo.NewInsuranceTopologyRepo()
	insuranceExternalApplicationRepo := repo.NewExternalApplicationRepo()

	serviceInstance = service.NewService(
		service.NewRepo(
			insuranceProductRepo,
			cmsDataRepo,
			insuranceProductDetailRepo,
			insuranceApplicationRepo,
			insuranceApplicationExtraDetailsRepo,
			insuranceLeadRepo,
			insurancePaymentsRepo,
			insurrancePartnerResponseRepo,
			feedbackQuestionRepo,
			feedbackQuestionOptionRepo,
			feedbackResponseRepo,
			formFieldRepo,
			insuranceTopologyRepo,
			insuranceExternalApplicationRepo,
		),
	)

	service.SetOneAssistHandler(serviceInstance)
}

func GetService() *service.Service {
	return serviceInstance
}
