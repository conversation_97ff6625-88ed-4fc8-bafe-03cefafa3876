package model

type IdentifierType string

const (
	InsuranceIdentifierType IdentifierType = "insurance"
	OfferIdentifierType     IdentifierType = "offer"
)

type CMSData struct {
	ID             int64          `gorm:"primaryKey;column:id"`
	IdentifierUID  string         `gorm:"column:identifier_uid"`
	IdentifierType IdentifierType `gorm:"column:identifier_type"`
	Key            string         `gorm:"column:key"`
	Value          string         `gorm:"column:value"`
	IsActive       bool           `gorm:"column:is_active"`
}

func (*CMSData) TableName() string {
	return "cms_data"
}
