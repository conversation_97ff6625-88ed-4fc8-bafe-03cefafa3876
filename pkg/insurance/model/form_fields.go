package model

import "gorm.io/datatypes"

type FormField struct {
	ID             int64          `gorm:"primaryKey;column:id"`
	IdentifierType string         `gorm:"column:identifier_type"`
	IdentifierID   string         `gorm:"column:identifier_id"`
	Type           string         `gorm:"column:type"`
	Key            string         `gorm:"column:key"`
	Validation     string         `gorm:"column:validation"`
	Label          string         `gorm:"column:label"`
	Placeholder    string         `gorm:"column:placeholder"`
	Options        datatypes.JSON `gorm:"column:options"`
	AdditionalInfo datatypes.JSON `gorm:"column:additional_info"`
	InitialValue   string         `gorm:"column:initial_value"`
	MinValue       string         `gorm:"column:min_value"`
	MaxValue       string         `gorm:"column:max_value"`
	MaxLength      int            `gorm:"column:max_length"`
	Editable       bool           `gorm:"column:editable"`
	Priority       int            `gorm:"column:priority"`
	IsActive       bool           `gorm:"column:is_active"`
}

func (*FormField) TableName() string {
	return "form_fields"
}
