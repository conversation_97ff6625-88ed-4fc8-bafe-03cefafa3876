package model

import (
	"time"
)

type PaymentStatus int
type ApplicationStatus int
type PartnerAPIStatus int

const (
	PendingPaymentStatus   PaymentStatus = 0
	InitiatedPaymentStatus PaymentStatus = 1
	SuccessPaymentStatus   PaymentStatus = 2
	FailedPaymentStatus    PaymentStatus = 3
	CanceledPaymentStatus  PaymentStatus = 4

	CreatedApplicationStatus    ApplicationStatus = 0
	InprogressApplicationStatus ApplicationStatus = 1
	SuccessApplicationStatus    ApplicationStatus = 2
	FailedApplicationStatus     ApplicationStatus = 3

	CreatedPartnerAPIStatus    PartnerAPIStatus = 0
	InprogressPartnerAPIStatus PartnerAPIStatus = 1
	SuccessPartnerAPIStatus    PartnerAPIStatus = 2
	FailedPartnerAPIStatus     PartnerAPIStatus = 3
)

type InsuranceApplication struct {
	ID               int64             `gorm:"primaryKey;column:id"`
	UID              string            `gorm:"column:uid"`
	Product          *InsuranceProduct `gorm:"foreignKey:product_id"`
	ProductID        int64             `gorm:"column:product_id"`
	CustomerID       int64             `gorm:"column:customer_id"`
	Lead             *InsuranceLead    `gorm:"foreignKey:lead_id"`
	LeadID           int64             `gorm:"column:lead_id"`
	Status           ApplicationStatus `gorm:"column:status"`
	PaymentStatus    PaymentStatus     `gorm:"column:payment_status"`
	Remarks          string            `gorm:"column:remarks"`
	CreatedAt        time.Time         `gorm:"column:created_at"`
	UpdatedAt        time.Time         `gorm:"column:updated_at"`
	CreatedBy        int64             `gorm:"column:created_by"`
	UpdatedBy        int64             `gorm:"column:updated_by"`
	PartnerAPIStatus PartnerAPIStatus  `gorm:"column:partner_api_status"`
}

func (*InsuranceApplication) TableName() string {
	return "insurance_application"
}
