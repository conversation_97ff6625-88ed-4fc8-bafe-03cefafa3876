package model

import (
	"time"
)

type FalimyConstructStatus int

type NomineeRelation int

type ConstructStatus int64

const (
	UndefinedFalimyConstruct FalimyConstructStatus = 0
	Adult1                   FalimyConstructStatus = 1
	Adult2                   FalimyConstructStatus = 2
	Adult2Child1             FalimyConstructStatus = 3
	Adult2Child2             FalimyConstructStatus = 4

	UndefinedRelation NomineeRelation = 0
	Son               NomineeRelation = 1
	Daughter          NomineeRelation = 2
	Mother            NomineeRelation = 3
	Father            NomineeRelation = 4
	Spouse            NomineeRelation = 5
	Other             NomineeRelation = 6

	UnlimitedService        ConstructStatus = 1
	PMS1AC1UnlimitedService ConstructStatus = 2
	PMS2AC1UnlimitedService ConstructStatus = 3
	PMS2AC2UnlimitedService ConstructStatus = 4
)

type InsuranceApplicationExtraDetails struct {
	ID                  int64                 `gorm:"primaryKey;column:id"`
	Application         *InsuranceApplication `gorm:"foreignKey:application_id"`
	ApplicationID       int64                 `gorm:"column:application_id"`
	Gender              string                `gorm:"column:gender"`
	Name                string                `gorm:"column:name"`
	DateOfBirth         *time.Time            `gorm:"column:dob"`
	Address             string                `gorm:"column:address"`
	Pincode             string                `gorm:"column:pincode"`
	Email               string                `gorm:"column:email"`
	NomineeName         string                `gorm:"column:nominee_name"`
	NomineeRelation     NomineeRelation       `gorm:"column:nominee_relation"`
	FamilyConstruct     FalimyConstructStatus `gorm:"column:family_construct"`
	NomineeDOB          *time.Time            `gorm:"column:nominee_dob"`
	HasGoodHealth       bool                  `gorm:"column:has_good_health"`
	CreatedAt           time.Time             `gorm:"column:created_at"`
	UpdatedAt           time.Time             `gorm:"column:updated_at"`
	CreatedBy           int64                 `gorm:"column:created_by"`
	UpdatedBy           int64                 `gorm:"column:updated_by"`
	MembershipID        string                `gorm:"column:membership_id"`
	PolicyStartDate     *time.Time            `gorm:"column:policy_start_date"`
	PolicyEndDate       *time.Time            `gorm:"column:policy_end_date"`
	DeductibleAmount    int64                 `gorm:"column:deductible_amount"`
	FinalAmount         int                   `gorm:"column:final_amount"`
	CoverageAmount      int                   `gorm:"column:coverage_amount"`
	Construct           ConstructStatus       `gorm:"column:construct"`
	ProductCovered      int64                 `gorm:"column:product_covered"`
	MembershipStartDate *time.Time            `gorm:"column:membership_start_date"`
}

func (*InsuranceApplicationExtraDetails) TableName() string {
	return "insurance_application_extra_details"
}

var relationMap = map[NomineeRelation]string{
	UndefinedRelation: "Undefined Relation",
	Son:               "Son",
	Daughter:          "Daughter",
	Mother:            "Mother",
	Father:            "Father",
	Spouse:            "Spouse",
	Other:             "Other",
}

func GetRelationName(relation NomineeRelation) string {
	if relationName, exists := relationMap[relation]; exists {
		return relationName
	}
	return "Unknown Relation"
}
