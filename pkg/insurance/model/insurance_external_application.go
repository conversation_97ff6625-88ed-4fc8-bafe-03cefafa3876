package model

type InsuranceExternalApplication struct {
	ID           int64  `gorm:"primaryKey;column:id;autoIncrement"`
	Name         string `gorm:"column:name"`
	PolicyNumber string `gorm:"column:policy_number"`
	Phone        string `gorm:"column:phone"`
	Insurer      string `gorm:"column:insurer"`
}

func (*InsuranceExternalApplication) TableName() string {
	return "insurance_external_application"
}
