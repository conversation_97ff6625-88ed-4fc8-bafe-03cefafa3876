package model

type InsuranceFeedbackQuestion struct {
	ID                 int64             `gorm:"primaryKey;column:id"`
	QuestionText       string            `gorm:"column:question_text"`
	InsuranceProduct   *InsuranceProduct `gorm:"foreignKey:insurance_product_id"`
	InsuranceProductID int64             `gorm:"column:insurance_product_id"`
	IsActive           bool              `gorm:"column:is_active"`
}

func (*InsuranceFeedbackQuestion) TableName() string {
	return "insurance_feedback_question"
}
