package model

type InsuranceFeedbackQuestionOption struct {
	ID         int64                      `gorm:"primaryKey;column:id"`
	QuestionID int64                      `gorm:"column:question_id"`
	Question   *InsuranceFeedbackQuestion `gorm:"foreignKey:question_id"`
	OptionText string                     `gorm:"column:option_text"`
	Priority   int                        `gorm:"column:priority"`
	IsActive   bool                       `gorm:"column:is_active"`
}

func (*InsuranceFeedbackQuestionOption) TableName() string {
	return "insurance_feedback_question_option"
}
