package model

type InsuranceFeedbackResponse struct {
	ID            int                              `gorm:"primaryKey;column:id"`
	CustomerID    int64                            `gorm:"column:customer_id"`
	ApplicationID int64                            `gorm:"column:application_id"`
	Application   *InsuranceApplication            `gorm:"foreignKey:application_id"`
	OptionID      int64                            `gorm:"column:option_id"`
	Option        *InsuranceFeedbackQuestionOption `gorm:"foreignKey:option_id"`
	Remark        string                           `gorm:"column:remark"`
}

func (*InsuranceFeedbackResponse) TableName() string {
	return "insurance_feedback_response"
}
