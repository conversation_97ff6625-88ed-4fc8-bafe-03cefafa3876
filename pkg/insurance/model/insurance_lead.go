package model

import (
	"time"
)

type InsuranceLead struct {
	ID           int64             `gorm:"primaryKey;column:id"`
	UID          string            `gorm:"column:uid"`
	Product      *InsuranceProduct `gorm:"foreignKey:product_id"`
	ProductID    int64             `gorm:"column:product_id"`
	CustomerID   int64             `gorm:"column:customer_id"`
	UTMSource    string            `gorm:"column:utm_source"`
	UTMMedium    string            `gorm:"column:utm_medium"`
	UTMCampaign  string            `gorm:"column:utm_campaign"`
	IsEligible   bool              `gorm:"column:is_eligible"`
	IsRegistered bool              `gorm:"column:is_registered"`
	CreatedAt    time.Time         `gorm:"column:created_at"`
	UpdatedAt    time.Time         `gorm:"column:updated_at"`
}

func (*InsuranceLead) TableName() string {
	return "insurance_lead"
}
