package model

import (
	"time"
)

type InsurancePayments struct {
	ID              int64         `gorm:"primaryKey;column:id"`
	OrderID         string        `gorm:"column:order_id"`
	PaymentProvider string        `gorm:"column:payment_provider"`
	CustomerID      int64         `gorm:"column:customer_id"`
	ReferenceID     int64         `gorm:"column:reference_id"`
	Mode            string        `gorm:"column:mode"`
	Amount          float64       `gorm:"column:amount"`
	Status          PaymentStatus `gorm:"column:status"`
	CreatedAt       time.Time     `gorm:"column:created_at"`
	UpdatedAt       time.Time     `gorm:"column:updated_at"`
}

func (*InsurancePayments) TableName() string {
	return "insurance_payments"
}

type OrderManagmentTable struct {
	CreatedDate time.Time  `gorm:"created_date"`
	UpdatedDate *time.Time `gorm:"updated_date"`
	TxnID       string     `gorm:"txn_id"`
	OrderID     string     `gorm:"order_id"`
	Processor   string     `gorm:"processor"`
	Notes       string     `gorm:"notes"`
	CustomerID  uint64     `gorm:"customer_id"`
	Amount      float64    `gorm:"amount"`
	ID          uint       `gorm:"primary_key" json:"id"`
	Mode        int        `gorm:"mode"`
	Status      int        `gorm:"status"`
}

func (*OrderManagmentTable) TableName() string {
	return "overall_order_management"
}
