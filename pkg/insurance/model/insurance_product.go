package model

import (
	"time"
)

type ProductEligibility int

const (
	AllEligible          ProductEligibility = 1
	ExistingOnlyEligible ProductEligibility = 2
)

type InsuranceProduct struct {
	ID                        int64              `gorm:"primaryKey;column:id"`
	UID                       string             `gorm:"column:uid"`
	Insurer                   *Insurer           `gorm:"foreignKey:insurer_id"`
	InsurerID                 int64              `gorm:"column:insurer_id"`
	Type                      string             `gorm:"column:type"`
	SubType                   string             `gorm:"column:sub_type"`
	Category                  string             `gorm:"column:category"`
	Coverage                  string             `gorm:"column:coverage"`
	Name                      string             `gorm:"column:name"`
	TermsAndConditionDocument string             `gorm:"column:terms_and_condition_document"`
	Eligibility               ProductEligibility `gorm:"column:eligibility"`
	Tenure                    int                `gorm:"column:tenure"`
	SumInsured                int                `gorm:"column:sum_insured"`
	FinalPricing              int                `gorm:"column:final_pricing"`
	IsActive                  bool               `gorm:"column:is_active"`
	CreatedAt                 time.Time          `gorm:"column:created_at"`
	UpdatedAt                 time.Time          `gorm:"column:updated_at"`
	CreatedBy                 int64              `gorm:"column:created_by"`
	UpdatedBy                 int64              `gorm:"column:updated_by"`
	PlanID                    string             `gorm:"column:plan_id"`
	IsPartnerAPICallActive    bool               `gorm:"column:is_partner_api_call_active"`
	RulesConfig               string             `gorm:"rules_config"`
	ProductCovered            int64              `gorm:"product_covered"`
	TenureText                string             `gorm:"tenure_text"`
	ApiVersion                int                `gorm:"column:api_version"`
}

func (*InsuranceProduct) TableName() string {
	return "insurance_product"
}
