package model

import (
	"time"
)

type InsuranceProductDetails struct {
	ID                  int64             `gorm:"primaryKey;column:id"`
	UID                 string            `gorm:"column:uid"`
	Product             *InsuranceProduct `gorm:"foreignKey:product_id"`
	ProductID           int64             `gorm:"column:product_id"`
	Brouchure           string            `gorm:"column:brouchure"`
	TermAndConditionDoc string            `gorm:"column:term_and_condition_doc"`
	SupportGUIDE        string            `gorm:"column:support_guide"`
	CreatedAt           time.Time         `gorm:"column:created_at"`
	UpdatedAt           time.Time         `gorm:"column:updated_at"`
}

func (*InsuranceProductDetails) TableName() string {
	return "insurance_product_details"
}
