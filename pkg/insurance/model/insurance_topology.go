package model

import (
	"time"

	"gorm.io/datatypes"
)

type InsuranceTopologyLevel int

const (
	Catagory    InsuranceTopologyLevel = 1
	SubCatagory InsuranceTopologyLevel = 2
	Listing     InsuranceTopologyLevel = 3
)

type InsuranceTopology struct {
	ID          int64                  `gorm:"primaryKey;column:id"`
	UID         string                 `gorm:"column:uid"`
	Logo        string                 `gorm:"column:logo"`
	Name        string                 `gorm:"column:name`
	Title       string                 `gorm:"column:title"`
	SubTitle    string                 `gorm:"column:sub_title"`
	ParentUID   string                 `gorm:"column:parent_uid"`
	ActionType  string                 `gorm:"column:action_type"`
	Action      string                 `gorm:"column:action"`
	Level       InsuranceTopologyLevel `gorm:"column:level"`
	UpdatedDate time.Time              `gorm:"column:updated_date"`
	CreateDate  time.Time              `gorm:"column:create_date"`
	MetaData    datatypes.JSON         `gorm:"column:meta_data"`
	IsActive    bool                   `gorm:"column:is_active`
}

func (*InsuranceTopology) TableName() string {
	return "insurance_topology"
}
