package model

import (
	"time"
)

type Insurer struct {
	ID            int64     `gorm:"primaryKey;column:id"`
	UID           string    `gorm:"column:uid"`
	Name          string    `gorm:"column:name"`
	LogoLink      string    `gorm:"column:logo_link"`
	CreatedAt     time.Time `gorm:"column:created_at"`
	UpdatedAt     time.Time `gorm:"column:updated_at"`
	CreatedBy     int64     `gorm:"column:created_by"`
	UpdatedBy     int64     `gorm:"column:updated_by"`
	Identifier    string    `gorm:"column:identifier"`
	PartnerCode   string    `gorm:"column:partner_code"`
	PartnerBuCode string    `gorm:"column:partner_bu_code"`
}

func (*Insurer) TableName() string {
	return "insurer"
}
