package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type CMSDataRepo struct{}

func NewCMSDataRepo() *CMSDataRepo {
	return &CMSDataRepo{}
}

func (*CMSDataRepo) Get(ctx context.Context, identifierType model.IdentifierType, identifierUID string, key string) (*model.CMSData, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.CMSData
	err := db.WithContext(ctx).
		Where(&model.CMSData{IdentifierUID: identifierUID, IdentifierType: identifierType, Key: key}).
		Last(&obj).Error

	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*CMSDataRepo) BulkGet(ctx context.Context, identifierType model.IdentifierType, identifierUID string) ([]model.CMSData, error) {
	db := config.GetConnectionCtx(ctx)

	var cmsData []model.CMSData
	err := db.WithContext(ctx).
		Where(&model.CMSData{IdentifierUID: identifierUID, IdentifierType: identifierType}).
		Find(&cmsData).Error

	if err != nil {
		return nil, err
	}

	return cmsData, nil
}

func (*CMSDataRepo) GetActive(ctx context.Context, identifierType model.IdentifierType, identifierUID string, key string) (*model.CMSData, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.CMSData
	err := db.WithContext(ctx).
		Where(&model.CMSData{IdentifierUID: identifierUID, IdentifierType: identifierType, Key: key, IsActive: true}).
		Last(&obj).Error

	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*CMSDataRepo) ConvertIdentifierType(indentifierType string) model.IdentifierType {
	switch indentifierType {
	case "insurance":
		return model.InsuranceIdentifierType
	case "offer":
		return model.OfferIdentifierType
	default:
		return ""
	}
}
