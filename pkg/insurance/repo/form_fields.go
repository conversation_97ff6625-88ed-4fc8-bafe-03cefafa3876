package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type FormFieldRepo struct{}

func NewFormFieldRepo() *FormFieldRepo {
	return &FormFieldRepo{}
}

func (r *FormFieldRepo) GetActiveInsuranceFormFields(ctx context.Context, applicationUID string) ([]model.FormField, error) {
	db := config.GetConnectionCtx(ctx)

	var objs []model.FormField
	err := db.WithContext(ctx).
		Model(&model.FormField{}).
		Where(&model.FormField{IdentifierType: dtype.Insurance, IdentifierID: applicationUID, IsActive: true}).
		Order("priority").
		Find(&objs).Error

	if err != nil {
		return nil, err
	}

	return objs, nil
}
