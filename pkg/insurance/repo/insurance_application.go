package repo

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsuranceApplicationRepo struct{}

func NewIsuranceApplicationRepo() *InsuranceApplicationRepo {
	return &InsuranceApplicationRepo{}
}

func (*InsuranceApplicationRepo) IsUniqueUID(ctx context.Context, uid string) (bool, error) {
	db := config.GetConnectionCtx(ctx)
	var count int64
	err := db.WithContext(ctx).
		Model(&model.InsuranceApplication{}).
		Where(&model.InsuranceApplication{UID: uid}).
		Count(&count).Error
	if err != nil {
		return false, err
	}

	return count == 0, nil
}

func (r *InsuranceApplicationRepo) Create(
	ctx context.Context,
	productID int64,
	customerID int64,
	leadID int64,
) (*model.InsuranceApplication, error) {
	db := config.GetConnectionCtx(ctx)

	uid, err := helper.GenerateUniqueUID(ctx, r, helper.UIDSize8)
	if err != nil {
		return nil, err
	}

	obj := model.InsuranceApplication{
		UID:           uid,
		ProductID:     productID,
		CustomerID:    customerID,
		LeadID:        leadID,
		Status:        model.CreatedApplicationStatus,
		PaymentStatus: model.PendingPaymentStatus,
	}

	err = db.WithContext(ctx).Create(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (r *InsuranceApplicationRepo) CreateSuccessApplication(
	ctx context.Context,
	productID int64,
	customerID int64,
	leadID int64,
) (*model.InsuranceApplication, error) {
	db := config.GetConnectionCtx(ctx)

	uid, err := helper.GenerateUniqueUID(ctx, r, helper.UIDSize8)
	if err != nil {
		return nil, err
	}

	obj := model.InsuranceApplication{
		UID:           uid,
		ProductID:     productID,
		CustomerID:    customerID,
		LeadID:        leadID,
		Status:        model.SuccessApplicationStatus,
		PaymentStatus: model.SuccessPaymentStatus,
	}

	err = db.WithContext(ctx).Create(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (r *InsuranceApplicationRepo) GetOrCreate(
	ctx context.Context,
	productID int64,
	customerID int64,
	leadID int64,
) (*model.InsuranceApplication, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsuranceApplication
	err := db.WithContext(ctx).
		Where(&model.InsuranceApplication{CustomerID: customerID, ProductID: productID, LeadID: leadID}).
		Last(&obj).Error

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if err != nil {
		return r.Create(ctx, productID, customerID, leadID)
	}

	return &obj, nil
}

func (*InsuranceApplicationRepo) GetByUID(ctx context.Context, applicationUID string) (*model.InsuranceApplication, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsuranceApplication
	err := db.WithContext(ctx).
		Where(&model.InsuranceApplication{UID: applicationUID}).
		Joins("Product").
		Joins("Product.Insurer").
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*InsuranceApplicationRepo) GetOrderTxnIdFromOverallOrderManagementTable(ctx context.Context, applicationUID string) (*model.OrderManagmentTable, error) {
	db := config.GetConnectionCtx(ctx)
	var obj model.OrderManagmentTable
	err := db.WithContext(ctx).
		Where(&model.OrderManagmentTable{OrderID: applicationUID}).
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil

}

func (*InsuranceApplicationRepo) Update(ctx context.Context, obj *model.InsuranceApplication, updateFields []string) error {
	if len(updateFields) == 0 {
		updateFields = []string{"*"}
	}

	db := config.GetConnectionCtx(ctx)
	err := db.WithContext(ctx).Select(updateFields).Updates(obj).Error
	if err != nil {
		return err
	}

	return nil
}

func (*InsuranceApplicationRepo) GetLastNonSuccessfulApplication(ctx context.Context, customerID, productID int64) (*model.InsuranceApplication, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsuranceApplication
	err := db.WithContext(ctx).
		Where("customer_id = ? AND product_id = ? AND payment_status != ?", customerID, productID, model.SuccessPaymentStatus).
		Last(&obj).Error

	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*InsuranceApplicationRepo) GetSuccessfulLeadIDs(ctx context.Context, customerID, productID int64) ([]int64, error) {
	db := config.GetConnectionCtx(ctx)

	var leadIDs []int64
	err := db.WithContext(ctx).
		Model(&model.InsuranceApplication{}).
		Select("lead_id").
		Where("customer_id = ? AND product_id = ? AND payment_status = ?", customerID, productID, model.SuccessPaymentStatus).
		Find(&leadIDs).Error

	if err != nil {
		return nil, err
	}

	return leadIDs, nil
}
