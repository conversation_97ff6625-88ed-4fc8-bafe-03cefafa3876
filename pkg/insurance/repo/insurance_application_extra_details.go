package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsuranceApplicationExtraDetailsRepo struct{}

func NewInsuranceApplicationExtraDetailsRepo() *InsuranceApplicationExtraDetailsRepo {
	return &InsuranceApplicationExtraDetailsRepo{}
}

func (r *InsuranceApplicationExtraDetailsRepo) CreateWithMapping(
	ctx context.Context,
	fields map[string]interface{},
) error {
	db := config.GetConnectionCtx(ctx)
	err := db.WithContext(ctx).
		Model(&model.InsuranceApplicationExtraDetails{}).
		Create(fields).Error
	if err != nil {
		return err
	}
	return nil
}

func (r *InsuranceApplicationExtraDetailsRepo) UpdateByIDWithMapping(
	ctx context.Context,
	id int64,
	fields map[string]interface{},
) error {
	db := config.GetConnectionCtx(ctx)

	err := db.WithContext(ctx).
		Model(&model.InsuranceApplicationExtraDetails{}).
		Where("id = ?", id).
		Updates(fields).Error
	if err != nil {
		return err
	}
	return nil
}

func (*InsuranceApplicationExtraDetailsRepo) Get(ctx context.Context, applicationID int64) (*model.InsuranceApplicationExtraDetails, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsuranceApplicationExtraDetails
	err := db.WithContext(ctx).
		Where(&model.InsuranceApplicationExtraDetails{ApplicationID: applicationID}).
		Last(&obj).Error

	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*InsuranceApplicationExtraDetailsRepo) Update(ctx context.Context, obj *model.InsuranceApplicationExtraDetails, updateFields []string) error {
	if len(updateFields) == 0 {
		updateFields = []string{"*"}
	}

	db := config.GetConnectionCtx(ctx)
	err := db.WithContext(ctx).Select(updateFields).Updates(obj).Error
	if err != nil {
		return err
	}

	return nil
}
