package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsuranceExternalApplicationRepo struct{}

func NewExternalApplicationRepo() *InsuranceExternalApplicationRepo {
	return &InsuranceExternalApplicationRepo{}
}

func (r *InsuranceExternalApplicationRepo) Create(
	ctx context.Context,
	name string,
	policyNumber string,
	phone string,
	insurer string,
) (*model.InsuranceExternalApplication, error) {
	db := config.GetConnectionCtx(ctx)

	obj := model.InsuranceExternalApplication{
		Name:         name,
		PolicyNumber: policyNumber,
		Phone:        phone,
		Insurer:      insurer,
	}

	err := db.WithContext(ctx).Create(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}
