package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsuranceFeedbackQuestion struct{}

func NewInsuranceFeedbackQuestion() *InsuranceFeedbackQuestion {
	return &InsuranceFeedbackQuestion{}
}

func (*InsuranceFeedbackQuestion) GetByProduct(ctx context.Context, productID int64) (*model.InsuranceFeedbackQuestion, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsuranceFeedbackQuestion
	err := db.WithContext(ctx).
		Where(&model.InsuranceFeedbackQuestion{InsuranceProductID: productID}).
		Last(&obj).Error

	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*InsuranceFeedbackQuestion) GetByID(ctx context.Context, id int64) (*model.InsuranceFeedbackQuestion, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsuranceFeedbackQuestion
	err := db.WithContext(ctx).
		Where(&model.InsuranceFeedbackQuestion{ID: id}).
		Last(&obj).Error

	return &obj, err
}
