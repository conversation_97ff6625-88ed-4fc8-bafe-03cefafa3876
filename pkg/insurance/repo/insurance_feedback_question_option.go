package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsuranceFeedbackQuestionOption struct{}

func NewInsuranceFeedbackQuestionOptionn() *InsuranceFeedbackQuestionOption {
	return &InsuranceFeedbackQuestionOption{}
}

func (*InsuranceFeedbackQuestionOption) GetByID(ctx context.Context, id int64) (*model.InsuranceFeedbackQuestionOption, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsuranceFeedbackQuestionOption
	err := db.WithContext(ctx).
		Where(&model.InsuranceFeedbackQuestionOption{ID: id}).
		Last(&obj).Error

	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*InsuranceFeedbackQuestionOption) GetByQuestionID(ctx context.Context, questionID int64) ([]model.InsuranceFeedbackQuestionOption, error) {
	db := config.GetConnectionCtx(ctx)

	var options []model.InsuranceFeedbackQuestionOption
	err := db.WithContext(ctx).
		Where(&model.InsuranceFeedbackQuestionOption{QuestionID: questionID}).
		Order("priority").
		Find(&options).Error

	if err != nil {
		return nil, err
	}

	return options, nil
}
