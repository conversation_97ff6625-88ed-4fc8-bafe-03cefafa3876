package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsuranceFeedbackResponse struct{}

func NewInsuranceFeedbackResponse() *InsuranceFeedbackResponse {
	return &InsuranceFeedbackResponse{}
}

func (*InsuranceFeedbackResponse) GetByCustomerApplication(
	ctx context.Context,
	customerID int64,
	applicationID int64,
) (*model.InsuranceFeedbackResponse, error) {
	db := config.GetConnectionCtx(ctx)

	var response model.InsuranceFeedbackResponse
	err := db.WithContext(ctx).Where("customer_id = ? AND application_id = ?", customerID, applicationID).Last(&response).Error
	return &response, err
}

func (*InsuranceFeedbackResponse) Create(
	ctx context.Context,
	customerID int64,
	applicationID int64,
	optionID int64,
	remark string,
) (*model.InsuranceFeedbackResponse, error) {
	db := config.GetConnectionCtx(ctx)

	obj := model.InsuranceFeedbackResponse{
		CustomerID:    customerID,
		ApplicationID: applicationID,
		OptionID:      optionID,
		Remark:        remark,
	}

	err := db.WithContext(ctx).Create(&obj).Error
	return &obj, err
}
