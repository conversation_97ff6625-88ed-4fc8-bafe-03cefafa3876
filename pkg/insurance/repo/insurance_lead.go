package repo

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsuranceLeadRepo struct{}

func NewIsuranceLeadRepo() *InsuranceLeadRepo {
	return &InsuranceLeadRepo{}
}

func (*InsuranceLeadRepo) IsUniqueUID(ctx context.Context, uid string) (bool, error) {
	db := config.GetConnectionCtx(ctx)

	var count int64
	err := db.WithContext(ctx).
		Model(&model.InsuranceLead{}).
		Where(&model.InsuranceLead{UID: uid}).
		Count(&count).Error
	if err != nil {
		return false, err
	}

	return count == 0, nil
}

func (*InsuranceLeadRepo) GetLead(ctx context.Context, customerID int64, productID int64) (*model.InsuranceLead, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsuranceLead
	err := db.WithContext(ctx).
		Where(&model.InsuranceLead{ProductID: productID, CustomerID: customerID}).
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (r *InsuranceLeadRepo) Create(
	ctx context.Context,
	customerID int64,
	productID int64,
	utmSource string,
	utmMedium string,
	utmCampaign string,
	isEligible bool,
	isRegistered bool,
) (*model.InsuranceLead, error) {
	db := config.GetConnectionCtx(ctx)

	uid, err := helper.GenerateUniqueUID(ctx, r, helper.UIDSize8)
	if err != nil {
		return nil, err
	}

	obj := model.InsuranceLead{
		UID:          uid,
		ProductID:    productID,
		CustomerID:   customerID,
		UTMSource:    utmSource,
		UTMMedium:    utmMedium,
		UTMCampaign:  utmCampaign,
		IsEligible:   isEligible,
		IsRegistered: isRegistered,
	}

	err = db.WithContext(ctx).Create(&obj).Error
	if err != nil {
		return nil, err
	}
	return &obj, nil
}

func (*InsuranceLeadRepo) Update(ctx context.Context, obj *model.InsuranceLead, updateFields []string) error {
	if len(updateFields) == 0 {
		updateFields = []string{"*"}
	}

	db := config.GetConnectionCtx(ctx)
	err := db.WithContext(ctx).Select(updateFields).Updates(obj).Error
	if err != nil {
		return err
	}

	return nil
}

func (r *InsuranceLeadRepo) GetOrCreate(
	ctx context.Context,
	customerID int64,
	productID int64,
	utmSource string,
	utmMedium string,
	utmCampaign string,
	isEligible bool,
	isRegistered bool,
) (*model.InsuranceLead, error) {

	obj, err := r.GetLead(ctx, customerID, productID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil {
		return r.Create(ctx, customerID, productID, utmSource, utmMedium, utmCampaign, isEligible, isRegistered)
	}

	return obj, nil
}

func (*InsuranceLeadRepo) GetLeadWithExclusions(ctx context.Context, customerID, productID int64, excludedIDs []int64) (*model.InsuranceLead, error) {
	db := config.GetConnectionCtx(ctx)

	query := db.WithContext(ctx).
		Model(&model.InsuranceLead{}).
		Where("product_id = ? AND customer_id = ?", productID, customerID)

	if len(excludedIDs) > 0 {
		query = query.Where("id NOT IN ?", excludedIDs)
	}

	var obj model.InsuranceLead
	err := query.Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*InsuranceLeadRepo) UpdateLeadUtms(ctx context.Context, leadID int64, utmSource, utmMedium, utmCampaign string) error {
	db := config.GetConnectionCtx(ctx)

	columns := map[string]interface{}{
		"utm_source":   utmSource,
		"utm_medium":   utmMedium,
		"utm_campaign": utmCampaign,
	}

	err := db.Model(&model.InsuranceLead{}).Where("id = ?", leadID).Updates(columns).Error
	if err != nil {
		return err
	}

	return nil
}
