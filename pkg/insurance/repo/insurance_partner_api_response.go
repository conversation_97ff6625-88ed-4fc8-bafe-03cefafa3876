package repo

import (
	"context"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsurancePartnerAPIResponse struct{}

func NewInsurancePartnerAPIResponse() *InsurancePartnerAPIResponse {
	return &InsurancePartnerAPIResponse{}
}

func (r *InsurancePartnerAPIResponse) Create(
	ctx context.Context,
	applicationID int64,
	response string,
) (*model.InsurancePartnerAPIResponse, error) {
	db := config.GetConnectionCtx(ctx)

	obj := model.InsurancePartnerAPIResponse{
		InsuranceApplicationID: applicationID,
		Response:               response,
	}

	err := db.WithContext(ctx).Create(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}
