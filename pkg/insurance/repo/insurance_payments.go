package repo

import (
	"context"
	"time"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsurancePayments struct{}

func NewInsurancePaymentsRepo() *InsurancePayments {
	return &InsurancePayments{}
}

func (*InsurancePayments) Get(ctx context.Context, customerID int64, referenceID int64) (*model.InsurancePayments, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsurancePayments
	err := db.WithContext(ctx).
		Where(&model.InsurancePayments{CustomerID: customerID, ReferenceID: referenceID}).
		Last(&obj).Error

	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*InsurancePayments) CreateSuccessEntry(ctx context.Context, customerID int64, referenceID int64, finalPricing int) error {
	db := config.GetConnectionCtx(ctx)

	obj := model.InsurancePayments{
		CustomerID:  customerID,
		ReferenceID: referenceID,
		Amount:      float64(finalPricing),
		Status:      model.SuccessPaymentStatus,
	}
	err := db.WithContext(ctx).Create(&obj).Error
	if err != nil {
		return err
	}

	return nil

}

func CreateOverallOrderManagementTable(ctx context.Context, CustomerID uint64, txnId string, body []byte, mode int, amount float64, processor string) error {
	//db := config.GetConnectionCtx(ctx)
	obj := model.OrderManagmentTable{
		CreatedDate: time.Now(),
		Processor:   processor,
		TxnID:       txnId,
		Status:      0,
		Amount:      amount,
		Mode:        mode,
		CustomerID:  CustomerID,
		OrderID:     txnId,
		Notes:       string(body),
	}
	db := config.GetDBConnection().Begin()
	err := db.WithContext(ctx).Create(&obj).Error
	if err != nil {
		return err
	}
	db.Commit()
	return nil
}
