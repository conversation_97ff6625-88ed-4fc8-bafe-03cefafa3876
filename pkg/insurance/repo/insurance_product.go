package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsuranceProductRepo struct{}

func NewIsuranceProductRepo() *InsuranceProductRepo {
	return &InsuranceProductRepo{}
}

func (*InsuranceProductRepo) Get(ctx context.Context, insuranceUID string) (*model.InsuranceProduct, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsuranceProduct
	err := db.WithContext(ctx).
		Joins("Insurer").
		Where(&model.InsuranceProduct{UID: insuranceUID}).
		Last(&obj).Error

	if err != nil {
		return nil, err
	}

	return &obj, nil
}
