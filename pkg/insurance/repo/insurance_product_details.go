package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsuranceProductDetailsRepo struct{}

func NewIsuranceProductDetailsRepo() *InsuranceProductDetailsRepo {
	return &InsuranceProductDetailsRepo{}
}

func (*InsuranceProductDetailsRepo) Get(ctx context.Context, insuranceID int64) (*model.InsuranceProductDetails, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsuranceProductDetails
	err := db.WithContext(ctx).
		Where(&model.InsuranceProductDetails{ProductID: insuranceID}).
		Last(&obj).Error

	if err != nil {
		return nil, err
	}

	return &obj, nil
}
