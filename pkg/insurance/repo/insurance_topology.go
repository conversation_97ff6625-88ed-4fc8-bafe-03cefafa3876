package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

type InsuranceTopologyRepo struct{}

func NewInsuranceTopologyRepo() *InsuranceTopologyRepo {
	return &InsuranceTopologyRepo{}
}

func (*InsuranceTopologyRepo) GetByUIDAndLevel(ctx context.Context, UID string, level model.InsuranceTopologyLevel) (*model.InsuranceTopology, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.InsuranceTopology
	err := db.WithContext(ctx).
		Where(&model.InsuranceTopology{UID: UID, IsActive: true, Level: level}).
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*InsuranceTopologyRepo) FilterByParentUIDAndLevel(ctx context.Context, ParentUID string, level model.InsuranceTopologyLevel) ([]model.InsuranceTopology, error) {
	db := config.GetConnectionCtx(ctx)

	var results []model.InsuranceTopology
	err := db.WithContext(ctx).
		Where(&model.InsuranceTopology{ParentUID: ParentUID, IsActive: true, Level: level}).
		Order("priority").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (*InsuranceTopologyRepo) FilterByLevel(ctx context.Context, level model.InsuranceTopologyLevel) ([]model.InsuranceTopology, error) {
	db := config.GetConnectionCtx(ctx)

	var results []model.InsuranceTopology
	err := db.WithContext(ctx).
		Where(&model.InsuranceTopology{Level: level, IsActive: true}).
		Order("priority").
		Find(&results).Error
	if err != nil {
		return nil, err
	}

	return results, nil
}

func (r *InsuranceTopologyRepo) GetInsuranceHomeData(ctx context.Context) ([]model.InsuranceTopology, error) {
	return r.FilterByLevel(ctx, model.Catagory)
}
