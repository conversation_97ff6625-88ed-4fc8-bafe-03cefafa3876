package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/datatypes"
	"reflect"
	"stashfin.com/stashfin/magneto/util"
	"strconv"
	"time"

	"gorm.io/gorm"

	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
	"stashfin.com/stashfin/magneto/pkg/loan"
)

func (s *Service) getCMSData(
	ctx context.Context,
	identifierType model.IdentifierType,
	identifierUID string,
	key string,
	result interface{},
) error {
	obj, err := s.repo.cmsData.Get(ctx, identifierType, identifierUID, key)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}

	err = json.Unmarshal([]byte(obj.Value), &result)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) getBulkCMSData(
	ctx context.Context,
	identifierType model.IdentifierType,
	identifierUID string,
	whatsCoveredResult interface{},
	whatsNotCoveredResult interface{},
	keyCoverageResult interface{},
	documentsResult interface{},
) error {
	objs, err := s.repo.cmsData.BulkGet(ctx, identifierType, identifierUID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}

	for _, obj := range objs {
		switch obj.Key {
		case WhatsCovered:
			err = json.Unmarshal([]byte(obj.Value), &whatsCoveredResult)
		case WhatsNotCovered:
			err = json.Unmarshal([]byte(obj.Value), &whatsNotCoveredResult)
		case KeyCoverage:
			err = json.Unmarshal([]byte(obj.Value), &keyCoverageResult)
		case Documents:
			err = json.Unmarshal([]byte(obj.Value), &documentsResult)
		default:
			err = nil
		}
	}

	return err
}

func (s *Service) isCustomerEligible(ctx context.Context, customerID int64) (bool, error) {
	return loan.GetService().HasCustomerDisbursedLoan(ctx, customerID)
}

func (s *Service) createNewCustomer(ctx context.Context, firstName string, phone int,
	utmSource, utmMedium, utmCampaign, productCode string,
) (int64, error) {
	customerID, err := customer.GetCustomerService().CreateCustomerWithPhoneAndName(ctx, firstName, strconv.Itoa(phone), utmSource)
	if err != nil {
		return 0, err
	}

	err = loan.GetService().CreateLoan(ctx, customerID, utmSource, utmMedium, utmCampaign, productCode)
	if err != nil {
		return 0, err
	}

	return customerID, nil
}

// DeduceResultFromRules analyzes input values and rules to deduce a result based on provided conditions.
// If the 'age' key is absent but 'dob' exists, it calculates the age from the 'dob' value and adds it to the input values.
// It then iterates through a set of rules to check conditions and return the result specified by the matching rule.
// If no rules match, it returns the default result from the rules configuration.
func DeduceResultFromRules(inputValues map[string]interface{}, rulesConfig dtype.RulesConfig) (datatypes.JSON, error) {
	_, hasAge := inputValues["age"]
	dob, dobExists := inputValues["dob"]

	if !hasAge && dobExists {
		switch dobVal := dob.(type) {
		case string:
			if dobVal == "" {
				break
			}
			dobTime, err := time.Parse(helper.DDMMYYYYParserLayout, dobVal)
			if err != nil {
				return nil, err
			}
			age := helper.CalculateAge(dobTime)
			inputValues["age"] = float64(age)
		case time.Time:
			age := helper.CalculateAge(dobVal)
			inputValues["age"] = float64(age)
		case *time.Time:
			age := helper.CalculateAge(*dobVal)
			inputValues["age"] = float64(age)
		default:
			return nil, fmt.Errorf("unexpeted dob type while deducing values %T", dob)
		}
	}

	for _, rule := range rulesConfig.Rules {
		if conditionsMetFromMap(rule.Conditions, inputValues) {
			return rule.ResultValue, nil
		}
	}

	return rulesConfig.Default, nil
}

func conditionsMetFromMap(conditions []dtype.Condition, inputValues map[string]interface{}) bool {
	for _, condition := range conditions {
		value, exists := inputValues[condition.Field]
		if !exists {
			return false
		}

		compareRes, err := compare(condition.Operator, value, condition.Value)
		if err != nil {
			util.Log.Errorf("error while comparing values %v %s %v, error: %s", value, condition.Operator, condition.Value, err)
			return false
		}

		if !compareRes {
			return false
		}
	}
	return true
}

func compare(operator string, a, b interface{}) (bool, error) {
	if reflect.TypeOf(a) != reflect.TypeOf(b) {
		return false, fmt.Errorf("values are not of the same type")
	}

	switch a.(type) {
	case int, int8, int16, int32, int64,
		uint, uint8, uint16, uint32, uint64,
		float32, float64:
		return numericComparison(a, b, operator)
	case string:
		return stringComparison(a, b, operator)
	default:
		return false, fmt.Errorf("unsupported type")
	}
}

func numericComparison(a, b interface{}, operator string) (bool, error) {
	af, err := getFloatValue(a)
	if err != nil {
		return false, err
	}

	bf, err := getFloatValue(b)
	if err != nil {
		return false, err
	}

	switch operator {
	case "==":
		return af == bf, nil
	case "!=":
		return af != bf, nil
	case ">":
		return af > bf, nil
	case "<":
		return af < bf, nil
	case ">=":
		return af >= bf, nil
	case "<=":
		return af <= bf, nil
	default:
		return false, fmt.Errorf("unsupported operator")
	}
}

func stringComparison(a, b interface{}, operator string) (bool, error) {
	as, ok := a.(string)
	if !ok {
		return false, fmt.Errorf("value is not a string")
	}

	bs, ok := b.(string)
	if !ok {
		return false, fmt.Errorf("value is not a string")
	}

	switch operator {
	case "==":
		return as == bs, nil
	case "!=":
		return as != bs, nil
	case ">":
		return as > bs, nil
	case "<":
		return as < bs, nil
	case ">=":
		return as >= bs, nil
	case "<=":
		return as <= bs, nil
	default:
		return false, fmt.Errorf("unsupported operator")
	}
}

// getFloatValue converts various types to float64.
// It takes an interface{} as input and tries to convert it to float64.
func getFloatValue(v interface{}) (float64, error) {
	switch val := v.(type) {
	case int, int8, int16, int32, int64:
		return float64(val.(int)), nil
	case uint, uint8, uint16, uint32, uint64:
		return float64(val.(uint)), nil
	case float32:
		return float64(val), nil
	case float64:
		return val, nil
	case string:
		return strconv.ParseFloat(val, 64)
	default:
		return 0, fmt.Errorf("value %v is not numeric", v)
	}
}
