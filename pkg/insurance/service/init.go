package service

import "stashfin.com/stashfin/magneto/pkg/insurance/repo"

type Repo struct {
	insuranceProduct                     *repo.InsuranceProductRepo
	cmsData                              *repo.CMSDataRepo
	insuranceProductDetails              *repo.InsuranceProductDetailsRepo
	insuranceApplicationRepo             *repo.InsuranceApplicationRepo
	insuranceApplicationExtraDetailsRepo *repo.InsuranceApplicationExtraDetailsRepo
	insuranceLeadRepo                    *repo.InsuranceLeadRepo
	insurancePaymentsRepo                *repo.InsurancePayments
	insurnacePartnerAPIResponse          *repo.InsurancePartnerAPIResponse
	feedbackQuestion                     *repo.InsuranceFeedbackQuestion
	feedbackQuestionOption               *repo.InsuranceFeedbackQuestionOption
	feedbackResponse                     *repo.InsuranceFeedbackResponse
	formField                            *repo.FormFieldRepo
	insuranceTopologyRepo                *repo.InsuranceTopologyRepo
	insuranceExternalApplicationRepo     *repo.InsuranceExternalApplicationRepo
}

func NewRepo(
	insuranceProduct *repo.InsuranceProductRepo,
	cmsData *repo.CMSDataRepo,
	insuranceProductDetails *repo.InsuranceProductDetailsRepo,
	insuranceApplicationRepo *repo.InsuranceApplicationRepo,
	insuranceApplicationExtraDetails *repo.InsuranceApplicationExtraDetailsRepo,
	insuranceLeadRepo *repo.InsuranceLeadRepo,
	insurancePaymentsRepo *repo.InsurancePayments,
	insurancePartnerAPIResponse *repo.InsurancePartnerAPIResponse,
	feedbackQuestion *repo.InsuranceFeedbackQuestion,
	feedbackQuestionOption *repo.InsuranceFeedbackQuestionOption,
	feedbackResponse *repo.InsuranceFeedbackResponse,
	formFieldRepo *repo.FormFieldRepo,
	insuranceTopologyRepo *repo.InsuranceTopologyRepo,
	insuranceExternalApplicationRepo *repo.InsuranceExternalApplicationRepo,
) *Repo {
	return &Repo{
		insuranceProduct:                     insuranceProduct,
		cmsData:                              cmsData,
		insuranceProductDetails:              insuranceProductDetails,
		insuranceApplicationRepo:             insuranceApplicationRepo,
		insuranceApplicationExtraDetailsRepo: insuranceApplicationExtraDetails,
		insuranceLeadRepo:                    insuranceLeadRepo,
		insurancePaymentsRepo:                insurancePaymentsRepo,
		insurnacePartnerAPIResponse:          insurancePartnerAPIResponse,
		feedbackQuestion:                     feedbackQuestion,
		feedbackQuestionOption:               feedbackQuestionOption,
		feedbackResponse:                     feedbackResponse,
		formField:                            formFieldRepo,
		insuranceTopologyRepo:                insuranceTopologyRepo,
		insuranceExternalApplicationRepo:     insuranceExternalApplicationRepo,
	}
}

type Service struct {
	repo *Repo
}

func NewService(repo *Repo) *Service {
	return &Service{
		repo: repo,
	}
}
