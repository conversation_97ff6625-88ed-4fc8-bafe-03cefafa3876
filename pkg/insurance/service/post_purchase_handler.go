package service

import (
	"context"
	"encoding/json"
	"strconv"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"

	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
)

var (
	oneAssistHandler *OneAssist
)

type InsurancePostPurchase interface {
	InitiatePostPurchase(context.Context, *model.InsuranceApplication) error
}

type OneAssist struct {
	service *Service
}

func SetOneAssistHandler(service *Service) {
	oneAssistHandler = &OneAssist{service}
}

func GetOneAssistHandler() *OneAssist {
	return oneAssistHandler
}

func (h *OneAssist) InitiatePostPurchase(ctx context.Context, applicationData *model.InsuranceApplication) error {
	applicationExtraDetails, err := h.service.repo.insuranceApplicationExtraDetailsRepo.Get(ctx, applicationData.ID)
	if err != nil {
		return err
	}

	err, phone := customer.GetCustomerService().GetCustomerPhone(ctx, applicationData.CustomerID)
	if err != nil {
		return err
	}

	result, err := client.GetOneAssistClient().OnboardCustomer(phone, applicationData, applicationExtraDetails)
	if err != nil {
		return err
	}

	applicationData.PartnerAPIStatus = model.FailedPartnerAPIStatus

	if result.Status == "success" {
		applicationData.PartnerAPIStatus = model.SuccessPartnerAPIStatus
	}

	err = h.service.repo.insuranceApplicationRepo.Update(ctx, applicationData, []string{"partner_api_status"})
	if err != nil {
		return err
	}

	marshalResult, err := json.Marshal(&result)
	if err != nil {
		return err
	}

	_, err = h.service.repo.insurnacePartnerAPIResponse.Create(ctx, applicationData.ID, string(marshalResult))
	if err != nil {
		return err
	}

	policyStartDate, err := helper.DateStringToTime(result.MembershipInfo.StartDate)
	if err != nil {
		return err
	}

	policyEndDate, err := helper.DateStringToTime(result.MembershipInfo.EndDate)
	if err != nil {
		return err
	}

	applicationExtraDetails.MembershipID = strconv.FormatInt(result.MembershipInfo.MembershipNumber, 10)
	applicationExtraDetails.PolicyStartDate = policyStartDate
	applicationExtraDetails.PolicyEndDate = policyEndDate

	err = h.service.repo.insuranceApplicationExtraDetailsRepo.Update(ctx, applicationExtraDetails, []string{"MembershipID", "PolicyStartDate", "PolicyEndDate"})
	if err != nil {
		return err
	}

	data := map[string]any{
		"applicationID": applicationData.ID,
		"customerID":    applicationData.CustomerID,
		"policyStartDate" : policyStartDate,
		"policyEndDate" : policyEndDate,
		"productId" : applicationData.ProductID,
	}
	config.GetSQSClient().SendMessageStruct(ctx, data, "BUY_INSURANCE")

	return nil
}
