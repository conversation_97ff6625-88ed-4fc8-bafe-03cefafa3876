package service

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/knadh/koanf/maps"
	"gorm.io/datatypes"

	"gorm.io/gorm"

	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/client/paymentservice"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/pkg/insurance/model"
	"stashfin.com/stashfin/magneto/util"
)

func (s *Service) GetProductDetails(ctx context.Context, productUID string) (*dtype.ProductDetailResponseData, error) {
	productDetail, err := s.repo.insuranceProduct.Get(ctx, productUID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil {
		return nil, helper.NewCustomError("Invalid insurance UID", http.StatusBadRequest)
	}

	if !productDetail.IsActive {
		return nil, helper.NewCustomError("Product is no longer available", http.StatusBadRequest)
	}

	var whatsCovered, whatsNotCovered, keyCoverage, documentsData *dtype.ExtraDetails
	err = s.getBulkCMSData(ctx, model.InsuranceIdentifierType, productUID, &whatsCovered, &whatsNotCovered, &keyCoverage, &documentsData)
	if err != nil {
		return nil, err
	}

	return &dtype.ProductDetailResponseData{
		ProductDetails: dtype.ProductDetailsData{
			Metadata: dtype.Metadata{
				Title: productDetail.Category,
			},
			Data: dtype.ProductDataWithTerms{
				ProductData: dtype.ProductData{
					Logo:           productDetail.Insurer.LogoLink,
					Title:          productDetail.Name,
					SubTitle:       productDetail.Insurer.Name,
					Type:           productDetail.Category,
					CoverageAmount: productDetail.SumInsured,
					Tenure:         strconv.Itoa(productDetail.Tenure) + " " + productDetail.TenureText,
					Premium:        productDetail.FinalPricing,
					ProductCovered: productDetail.ProductCovered,
				},
				TermsAndConditions: productDetail.TermsAndConditionDocument,
			},
		},
		WhatsCovered:    whatsCovered,
		WhatsNotCovered: whatsNotCovered,
		KeyCoverage:     keyCoverage,
		Documents:       documentsData,
	}, nil
}

func (s *Service) CreateOrGetInsuranceApplication(
	ctx context.Context,
	productUID string,
	customerId int64,
	utmSource string,
	utmMedium string,
	utmCampaign string,
) (*dtype.CreateInsuranceApplicationData, error) {
	insuranceProduct, err := s.repo.insuranceProduct.Get(ctx, productUID)
	if err != nil {
		return nil, err
	}
	productID := insuranceProduct.ID

	if !insuranceProduct.IsActive {
		return nil, helper.NewCustomError("Product is no longer available", http.StatusBadRequest)
	}

	isRegistered := true // Non-register user will not be able to come at this flow without registering

	isEligible, err := s.isCustomerEligiblityForInsurance(ctx, customerId, insuranceProduct)
	if err != nil {
		return nil, err
	}

	insuranceApplicationData, err := s.ProcessApplicationForLead(ctx, customerId, productID, utmSource, utmMedium, utmCampaign, isEligible, isRegistered)
	if err != nil {
		return nil, err
	}

	if !isEligible {
		return nil, helper.NotFoundError("You are not eligible for this product.")
	}

	insuranceApplicationExtraDetials, err := s.repo.insuranceApplicationExtraDetailsRepo.Get(ctx, insuranceApplicationData.ID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil {
		insuranceApplicationExtraDetials = &model.InsuranceApplicationExtraDetails{}
	}

	customerDetailsData, err := customer.GetCustomerService().GetCustomerDetail(ctx, customerId)
	if err != nil {
		return nil, err
	}

	nomineeDOB := ""
	if insuranceApplicationExtraDetials.NomineeDOB != nil && !insuranceApplicationExtraDetials.NomineeDOB.IsZero() {
		nomineeDOB = insuranceApplicationExtraDetials.NomineeDOB.Format("02-01-2006")
	}

	return &dtype.CreateInsuranceApplicationData{
		Gender:          customerDetailsData.Gender,
		Name:            customerDetailsData.Name,
		DateOfBirth:     customerDetailsData.DateOfBirth,
		Address:         customerDetailsData.Address,
		Pincode:         customerDetailsData.Pincode,
		Email:           customerDetailsData.Email,
		NomineeName:     insuranceApplicationExtraDetials.NomineeName,
		NomineeDOB:      nomineeDOB,
		NomineeRelation: int(insuranceApplicationExtraDetials.NomineeRelation),
		FamilyConstruct: int(insuranceApplicationExtraDetials.FamilyConstruct),
		ApplicationUID:  insuranceApplicationData.UID,
	}, nil
}

func (s *Service) RequestOTP(ctx context.Context, phone int) error {
	_, err := customer.GetCustomerService().GetIDFromPhone(ctx, phone)
	fmt.Println("query executed for otp")
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	fmt.Println("not found error")
	if err != nil {
		return client.GetApiV2Client().RequestOTPBeforeRegistration(ctx, phone)
	}
	fmt.Println("afetr register otp")
	return client.GetApiV2Client().RequestOTP(ctx, phone)
}

func (s *Service) VerifyOTP(ctx context.Context, req *dtype.VerifyOTPRequest, utmSource, utmMedium, utmCampaign string) (*dtype.VerifyOTPResponse, error) {
	insuranceObj, err := s.repo.insuranceProduct.Get(ctx, req.ProductUID)
	if err != nil {
		return nil, err
	}

	customerID, err := customer.GetCustomerService().GetIDFromPhone(ctx, req.Mobile)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil {
		err := client.GetApiV2Client().VerifyOTPBeforeRegistration(ctx, req.Mobile, req.OTP)
		if err != nil {
			return nil, err
		}

		customerID, err := s.createNewCustomer(ctx, req.Name, req.Mobile, InsuranceUTMSource, "", "", InsuranceProductCode)
		if err != nil {
			return nil, err
		}

		_, err = s.GetUnusedOrCreateInsuranceLead(ctx, customerID, insuranceObj.ID, utmSource, utmMedium, utmCampaign, false, true)
		if err != nil {
			return nil, err
		}

		return &dtype.VerifyOTPResponse{
			IsRegistered: false,
		}, nil
	}

	authToken, err := client.GetApiV2Client().VerifyOTP(ctx, req.Mobile, req.OTP)
	if err != nil {
		return nil, err
	}

	isEligible, err := s.isCustomerEligiblityForInsurance(ctx, customerID, insuranceObj)
	if err != nil {
		return nil, err
	}

	_, err = s.GetUnusedOrCreateInsuranceLead(ctx, customerID, insuranceObj.ID, utmSource, utmMedium, utmCampaign, isEligible, true)
	if err != nil {
		return nil, err
	}

	if !isEligible {
		return &dtype.VerifyOTPResponse{
			IsRegistered: true,
			IsEligible:   false,
		}, nil
	}

	return &dtype.VerifyOTPResponse{
		IsRegistered: true,
		IsEligible:   true,
		AccessToken:  authToken,
	}, nil
}

func (s *Service) SaveApplicationAndGeneratePaymentLink(ctx context.Context, applicationUID string, customerID int64, requestBody dtype.SaveApplicationDetailsRequestBody) (*dtype.PaymentLinkData, error) {
	applicationData, err := s.repo.insuranceApplicationRepo.GetByUID(ctx, applicationUID)
	if err != nil {
		return nil, err
	}

	dob := util.ParseDateString(requestBody.UserDetails.DateOfBirth, "02-01-2006")

	applicationID := applicationData.ID
	fields := map[string]interface{}{
		"application_id":        applicationID,
		"gender":                requestBody.UserDetails.Gender,
		"name":                  requestBody.UserDetails.Name,
		"dob":                   dob,
		"address":               requestBody.UserDetails.Address,
		"pincode":               requestBody.UserDetails.Pincode,
		"email":                 requestBody.UserDetails.Email,
		"nominee_name":          requestBody.UserDetails.NomineeName,
		"nominee_relation":      requestBody.UserDetails.NomineeRelation,
		"family_construct":      requestBody.UserDetails.FamilyConstruct,
		"nominee_dob":           util.ParseDateString(requestBody.UserDetails.NomineeDOB, "02-01-2006"),
		"deductible_amount":     requestBody.UserDetails.DeductibleAmount,
		"coverage_amount":       requestBody.UserDetails.CoverageAmount,
		"product_covered":       requestBody.UserDetails.ProductCovered,
		"construct":             requestBody.UserDetails.Construct,
		"membership_start_date": util.ParseDateString(requestBody.UserDetails.MembershipStartDate, "02-01-2006"),
	}

	finalPrice := applicationData.Product.FinalPricing

	if applicationData.Product.RulesConfig != "" {
		updatedFields := maps.Copy(fields)
		var rulesConfig dtype.RulesConfig
		if err := json.Unmarshal([]byte(applicationData.Product.RulesConfig), &rulesConfig); err != nil {
			return nil, err
		}

		updatedValues, err := DeduceResultFromRules(updatedFields, rulesConfig)
		if err != nil {
			return nil, err
		}

		if updatedValues != nil {
			var updatedValuesMap map[string]interface{}
			err = json.Unmarshal(updatedValues, &updatedValuesMap)
			if err != nil {
				return nil, err
			}

			amountInterface, exist := updatedValuesMap["amount"]
			if exist {
				updatedAmount, ok := amountInterface.(float64)
				if !ok {
					return nil, fmt.Errorf("error while parsing interface to int")
				}
				finalPrice = int(updatedAmount)
			}
		}
	}

	if requestBody.UserDetails.CoverageAmount == 0 {
		fields["coverage_amount"] = applicationData.Product.SumInsured
	}

	fields["final_amount"] = finalPrice

	err = s.CreateOrUpdateApplicationExtraDetials(ctx, fields, applicationID)
	if err != nil {
		return nil, err
	}

	if !util.IsValidAge(dob, util.InsuranceMinAge, util.InsuranceMaxAge) {
		return nil, helper.NewCustomError("You're ineligible for this product. Age needs to be between 18 to 60 years.", http.StatusBadRequest)
	}

	paymentGateway := paymentservice.CashFreePaymentGatway
	//cashFreePaymentGateway intregate all insurance payments. same issuse in RazorpaymentGateway payment callback

	//	paymentGateway = paymentservice.RazorpayPaymentGateway
	// if slices.Contains(config.GetConfigValues().InsuranceCashFreeGatewayProductUIDs, applicationData.Product.UID) {
	// 	paymentGateway = paymentservice.CashFreePaymentGatway
	// }

	paymentLink, err := client.GetPaymentService().GetPaymentLink(
		customerID,
		finalPrice,
		applicationID,
		paymentservice.InsurancePaymentMode,
		applicationData.Product.Name,
		applicationData.Product.UID,
		paymentGateway,
		requestBody.UserDetails.OfferDiscount,
		ctx,
	)

	if err != nil {
		return nil, err
	}

	if applicationData.Status == model.SuccessApplicationStatus || applicationData.PaymentStatus == model.SuccessPaymentStatus {
		return nil, helper.NewCustomError("Already Purchased", http.StatusBadRequest)
	}

	applicationData.Status = model.InprogressApplicationStatus
	applicationData.PaymentStatus = model.InitiatedPaymentStatus
	err = s.repo.insuranceApplicationRepo.Update(ctx, applicationData, []string{"status", "payment_status"})
	if err != nil {
		return nil, err
	}

	return &dtype.PaymentLinkData{
		PaymentLink: paymentLink,
	}, nil
}

func (s *Service) GetPaymentStatus(ctx context.Context, applicationUID string, customerID int64) (*dtype.PaymentStatusResponse, error) {
	applicationData, err := s.repo.insuranceApplicationRepo.GetByUID(ctx, applicationUID)
	if err != nil {
		return nil, err
	}

	applicationExtraDetails, err := s.repo.insuranceApplicationExtraDetailsRepo.Get(ctx, applicationData.ID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil {
		applicationExtraDetails = &model.InsuranceApplicationExtraDetails{}
	}

	insurancePayment, err := s.repo.insurancePaymentsRepo.Get(ctx, customerID, applicationData.ID)
	if err != nil {
		return nil, err
	}

	productDetail := applicationData.Product
	customerDetailsData, err := customer.GetCustomerService().GetCustomerDetail(ctx, customerID)
	if err != nil {
		return nil, err
	}

	finalStatus := applicationData.PaymentStatus

	applicationUpdatetimeDiff := time.Now().Sub(applicationData.UpdatedAt)
	if applicationData.PaymentStatus == model.SuccessPaymentStatus && applicationData.PartnerAPIStatus == model.InprogressPartnerAPIStatus && applicationUpdatetimeDiff <= (30*time.Second) {
		finalStatus = model.InitiatedPaymentStatus
	}

	policyStartDate := ""
	policyEndDate := ""

	if applicationExtraDetails.PolicyStartDate != nil && !applicationExtraDetails.PolicyStartDate.IsZero() {
		policyStartDate = applicationExtraDetails.PolicyStartDate.Format(helper.DDMMYYYYParserLayout)
	}

	if applicationExtraDetails.PolicyEndDate != nil && !applicationExtraDetails.PolicyEndDate.IsZero() {
		policyEndDate = applicationExtraDetails.PolicyEndDate.Format(helper.DDMMYYYYParserLayout)
	}

	return &dtype.PaymentStatusResponse{
		Status: int(finalStatus),
		Data: dtype.PaymentStatusData{
			ProductDetails: dtype.ProductData{
				Logo:           productDetail.Insurer.LogoLink,
				Title:          productDetail.Name,
				SubTitle:       productDetail.Insurer.Name,
				Type:           productDetail.Category,
				CoverageAmount: applicationExtraDetails.CoverageAmount,
				Tenure:         strconv.Itoa(productDetail.Tenure) + " " + productDetail.TenureText,
				Premium:        applicationExtraDetails.FinalAmount,
				ProductCovered: applicationExtraDetails.ProductCovered,
			},
			TransactionDetails: dtype.TransactionDetails{
				ApplicationID:    applicationUID,
				Name:             customerDetailsData.Name,
				TransactionID:    insurancePayment.OrderID,
				MembershipNumber: applicationExtraDetails.MembershipID,
				StartDate:        policyStartDate,
				EndDate:          policyEndDate,
			},
		},
	}, nil
}

func (s *Service) GetUnusedOrCreateInsuranceLead(
	ctx context.Context, customerID, productID int64,
	utmSource, utmMedium, utmCampaign string,
	isEliglible, isRegistered bool,
) (*model.InsuranceLead, error) {

	// Find successful lead IDs
	successfulLeadIDs, err := s.repo.insuranceApplicationRepo.GetSuccessfulLeadIDs(ctx, customerID, productID)
	if err != nil {
		return nil, err
	}

	// Find leads with exclusions of successful lead IDs
	lead, err := s.repo.insuranceLeadRepo.GetLeadWithExclusions(ctx, customerID, productID, successfulLeadIDs)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if lead != nil {
		lead.UTMSource = utmSource
		lead.UTMMedium = utmMedium
		lead.UTMCampaign = utmCampaign
		err = s.repo.insuranceLeadRepo.Update(ctx, lead, []string{"utm_source", "utm_medium", "utm_campaign"})
		if err != nil {
			return nil, err
		}
		return lead, nil
	}

	return s.repo.insuranceLeadRepo.Create(ctx, customerID, productID, utmSource, utmMedium, utmCampaign, isEliglible, isRegistered)
}

func (s *Service) CreateInternalSuccessApplication(
	ctx context.Context, productUID string, customerID int64,
	utmSource, utmMedium, utmCampaign string,
	isEliglible, isRegistered bool,
) (*model.InsuranceApplication, error) {

	product, err := s.repo.insuranceProduct.Get(ctx, productUID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil {
		return nil, helper.NewCustomError("Invalid insurance UID", http.StatusBadRequest)
	}

	newLead, err := s.repo.insuranceLeadRepo.Create(ctx, customerID, product.ID, utmSource, utmMedium, utmCampaign, isEliglible, isRegistered)
	if err != nil {
		return nil, err
	}

	insuranceApplicationData, err := s.repo.insuranceApplicationRepo.CreateSuccessApplication(ctx, product.ID, customerID, newLead.ID)
	if err != nil {
		return nil, err
	}

	err = s.repo.insurancePaymentsRepo.CreateSuccessEntry(ctx, customerID, insuranceApplicationData.ID, product.FinalPricing)
	if err != nil {
		return nil, err
	}

	customerDetailsData, err := customer.GetCustomerService().GetCustomerDetail(ctx, customerID)
	if err != nil {
		return nil, err
	}

	fields := map[string]interface{}{
		"application_id": insuranceApplicationData.ID,
		"name":           customerDetailsData.Name,
		"dob":            customerDetailsData.DateOfBirth,
		"pincode":        customerDetailsData.Pincode,
		"email":          customerDetailsData.Email,
		"address":        customerDetailsData.Address,
		"gender":         customerDetailsData.Gender,
	}

	err = s.repo.insuranceApplicationExtraDetailsRepo.CreateWithMapping(ctx, fields)
	if err != nil {
		return nil, err
	}

	err = s.PostPaymentSuccess(ctx, insuranceApplicationData.UID)
	if err != nil {
		util.Log.Errorf("Error in doing post purchase while creatin internal insurance success application, Error: %s", err)
	}
	return insuranceApplicationData, nil
}

func (s *Service) ProcessApplicationForLead(
	ctx context.Context, customerID, productID int64,
	utmSource, utmMedium, utmCampaign string,
	isEliglible, isRegistered bool,
) (*model.InsuranceApplication, error) {

	// Check if there is a non-successful application
	nonSuccessfulApplication, err := s.repo.insuranceApplicationRepo.GetLastNonSuccessfulApplication(ctx, customerID, productID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if nonSuccessfulApplication != nil {
		if utmSource != "" || utmMedium != "" || utmCampaign != "" {
			err := s.repo.insuranceLeadRepo.UpdateLeadUtms(ctx, nonSuccessfulApplication.LeadID, utmSource, utmMedium, utmCampaign)
			if err != nil {
				return nil, err
			}
		}

		return nonSuccessfulApplication, nil
	}

	lead, err := s.GetUnusedOrCreateInsuranceLead(ctx, customerID, productID, utmSource, utmMedium, utmCampaign, isEliglible, isRegistered)
	if err != nil {
		return nil, err
	}

	// Create an application for lead
	return s.repo.insuranceApplicationRepo.Create(ctx, productID, customerID, lead.ID)
}

func (s *Service) isCustomerEligiblityForInsurance(ctx context.Context, customerId int64, insuranceObj *model.InsuranceProduct) (bool, error) {
	if insuranceObj.Eligibility == model.AllEligible {
		return true, nil
	}

	return s.isCustomerEligible(ctx, customerId)
}

func (s *Service) GetRetryPaymentLink(ctx context.Context, applicationUID string, customerID int64) (*dtype.PaymentLinkData, error) {
	applicationData, err := s.repo.insuranceApplicationRepo.GetByUID(ctx, applicationUID)
	if err != nil {
		return nil, err
	}

	applicationID := applicationData.ID

	if applicationData.Status == model.SuccessApplicationStatus || applicationData.PaymentStatus == model.SuccessPaymentStatus {
		return nil, helper.NewCustomError("Already Purchased", http.StatusBadRequest)
	}

	insuranceApplicationExtraDetials, err := s.repo.insuranceApplicationExtraDetailsRepo.Get(ctx, applicationID)
	if err != nil {
		return nil, err
	}

	insuranceTxnIdFromOverallOrderManagement, err := s.repo.insuranceApplicationRepo.GetOrderTxnIdFromOverallOrderManagementTable(ctx, strconv.FormatInt(applicationID, 10))
	if err != nil {
		return nil, err
	}
	if insuranceApplicationExtraDetials.FinalAmount <= 0 {
		return nil, fmt.Errorf("amount should not be zero or null")
	}

	applicationData.Status = model.InprogressApplicationStatus
	applicationData.PaymentStatus = model.InitiatedPaymentStatus
	err = s.repo.insuranceApplicationRepo.Update(ctx, applicationData, []string{"status", "payment_status"})
	if err != nil {
		return nil, err
	}

	paymentGateway := paymentservice.CashFreePaymentGatway
	//cashFreePaymentGateway intregate all insurance payments. same issuse in RazorpaymentGateway payment callback

	// paymentGateway := paymentservice.RazorpayPaymentGateway
	// if slices.Contains(config.GetConfigValues().InsuranceCashFreeGatewayProductUIDs, applicationData.Product.UID) {
	// 	paymentGateway = paymentservice.CashFreePaymentGatway
	// }

	paymentLink, err := client.GetPaymentService().GetPaymentLink(
		customerID,
		insuranceApplicationExtraDetials.FinalAmount,
		applicationID,
		paymentservice.InsurancePaymentMode,
		applicationData.Product.Name,
		applicationData.Product.UID,
		paymentGateway,
		insuranceTxnIdFromOverallOrderManagement.Notes,
		ctx,
	)

	if err != nil {
		return nil, err
	}

	return &dtype.PaymentLinkData{
		PaymentLink: paymentLink,
	}, nil
}

func (s *Service) CreateOrUpdateApplicationExtraDetials(ctx context.Context, fields map[string]interface{}, applicationID int64) error {
	applicationExtraDetails, err := s.repo.insuranceApplicationExtraDetailsRepo.Get(ctx, applicationID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	if err == nil {
		return s.repo.insuranceApplicationExtraDetailsRepo.UpdateByIDWithMapping(ctx, applicationExtraDetails.ID, fields)
	}

	err = s.repo.insuranceApplicationExtraDetailsRepo.CreateWithMapping(ctx, fields)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) PostPaymentSuccess(ctx context.Context, applicationUID string) error {
	applicationData, err := s.repo.insuranceApplicationRepo.GetByUID(ctx, applicationUID)
	if err != nil {
		return err
	}

	if !applicationData.Product.IsPartnerAPICallActive {
		return helper.NewCustomError("Partner API call is not active for this product", http.StatusBadRequest)
	}

	applicationData.PartnerAPIStatus = model.InprogressPartnerAPIStatus
	err = s.repo.insuranceApplicationRepo.Update(ctx, applicationData, []string{"partner_api_status"})
	if err != nil {
		return err
	}

	handler := getHandlerUsingIdentifier(applicationData.Product.Insurer.Identifier)
	if handler == nil {
		return errors.New("handler not found in insurance post payment success")
	}

	err = handler.InitiatePostPurchase(ctx, applicationData)
	if err != nil {
		return err
	}
	return nil
}

func getHandlerUsingIdentifier(identifier string) InsurancePostPurchase {
	switch identifier {
	case "one-assist":
		return GetOneAssistHandler()
	default:
		return nil
	}
}

func (s *Service) GetFeedbackQuestion(
	ctx context.Context,
	applicationUID string,
	customerID int64,
) (*dtype.InsuranceFeedbackQuestionResponse, error) {
	application, err := s.repo.insuranceApplicationRepo.GetByUID(ctx, applicationUID)
	if err != nil {
		return nil, err
	}

	_, err = s.repo.feedbackResponse.GetByCustomerApplication(ctx, customerID, application.ID)
	if err == nil {
		return &dtype.InsuranceFeedbackQuestionResponse{
			IsFeedbackRequired: false,
		}, nil
	}

	questions, err := s.repo.feedbackQuestion.GetByProduct(ctx, application.ProductID)
	if err != nil {
		return nil, err
	}

	optionObj, err := s.repo.feedbackQuestionOption.GetByQuestionID(ctx, questions.ID)
	if err != nil {
		return nil, err
	}

	var options []dtype.InsuranceFeedbackOption
	for _, option := range optionObj {
		options = append(options, dtype.InsuranceFeedbackOption{
			ID:   option.ID,
			Text: option.OptionText,
		})
	}

	return &dtype.InsuranceFeedbackQuestionResponse{
		IsFeedbackRequired: true,
		Question: &dtype.InsuranceFeedbackQuestion{
			ID:   questions.ID,
			Text: questions.QuestionText,
		},
		Options: options,
	}, nil
}

func (s *Service) SaveFeedbackResponse(
	ctx context.Context,
	applicationUID string,
	customerID int64,
	questionID int64,
	optionID int64,
	remarks string,
) error {
	application, err := s.repo.insuranceApplicationRepo.GetByUID(ctx, applicationUID)
	if err != nil {
		if util.IsNotFoundError(err) {
			return helper.BadRequestError("Invalid application")
		}
		return err
	}

	_, err = s.repo.feedbackResponse.GetByCustomerApplication(ctx, customerID, application.ID)
	if err == nil {
		return helper.BadRequestError("Response already recorded")
	}

	_, err = s.repo.feedbackQuestion.GetByID(ctx, questionID)
	if err != nil {
		if util.IsNotFoundError(err) {
			return helper.BadRequestError("Invalid question ID")
		}
		return err
	}

	_, err = s.repo.feedbackQuestionOption.GetByID(ctx, optionID)
	if err != nil {
		if util.IsNotFoundError(err) {
			return helper.BadRequestError("Invalid response")
		}
		return err
	}

	_, err = s.repo.feedbackResponse.Create(ctx, customerID, application.ID, optionID, remarks)
	return err
}

func (s *Service) CreateOrGetGenericApplication(
	ctx context.Context,
	productUID string,
	customerId int64,
	utmSource string,
	utmMedium string,
	utmCampaign string,
) (*dtype.CreateGenericApplicationResponse, error) {
	insuranceApplicationData, err := s.CreateOrGetInsuranceApplication(ctx, productUID, customerId, utmSource, utmMedium, utmCampaign)
	if err != nil {
		return nil, err
	}

	resultMap, err := helper.StructToMap(insuranceApplicationData)
	if err != nil {
		return nil, err
	}

	formFields, err := s.repo.formField.GetActiveInsuranceFormFields(ctx, productUID)
	if err != nil {
		return nil, err
	}

	response := &dtype.CreateGenericApplicationResponse{}
	for _, field := range formFields {
		if _, ok := resultMap[field.Key]; !ok {
			resultMap[field.Key] = nil
		}

		minValue := field.MinValue
		maxValue := field.MaxValue

		if field.Type == Date && minValue != "" && maxValue != "" {
			minAge, err := strconv.Atoi(minValue)
			if err != nil {
				return nil, err
			}

			maxAge, err := strconv.Atoi(maxValue)
			if err != nil {
				return nil, err
			}
			maxValue, minValue = helper.CalculateBirthDates(minAge, maxAge)
		}
		response.Fields = append(response.Fields, dtype.Field{
			Type:           field.Type,
			Key:            field.Key,
			Label:          field.Label,
			Validation:     field.Validation,
			Placeholder:    field.Placeholder,
			InitialValue:   resultMap[field.Key],
			Editable:       field.Editable,
			Options:        field.Options,
			AdditionalInfo: field.AdditionalInfo,
			MaxLength:      field.MaxLength,
			MinValue:       minValue,
			MaxValue:       maxValue,
		})
	}
	response.ApplicationUID = insuranceApplicationData.ApplicationUID
	return response, nil
}

func (s *Service) GetProductUpdatedInfo(
	ctx context.Context,
	productUID string,
	inputValues map[string]interface{},
) (datatypes.JSON, error) {
	productDetail, err := s.repo.insuranceProduct.Get(ctx, productUID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil {
		return nil, helper.NewCustomError("Invalid insurance UID", http.StatusBadRequest)
	}

	if !productDetail.IsActive {
		return nil, helper.NewCustomError("Product is no longer available", http.StatusBadRequest)
	}

	if productDetail.RulesConfig == "" {
		return nil, helper.NewCustomError("no updated values", http.StatusBadRequest)
	}

	var rulesConfig dtype.RulesConfig
	if err := json.Unmarshal([]byte(productDetail.RulesConfig), &rulesConfig); err != nil {
		return nil, err
	}

	updatedValues, err := DeduceResultFromRules(inputValues, rulesConfig)
	if err != nil {
		return nil, err
	}

	return updatedValues, nil
}

func (s *Service) GetInsuranceHomePage(ctx context.Context) (*dtype.InsuranceHomeResponse, error) {
	insuranceHomeObjs, err := s.repo.insuranceTopologyRepo.GetInsuranceHomeData(ctx)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	insuranceCatagories := make([]dtype.InsuranceCategory, 0)
	for _, insuranceHomeObj := range insuranceHomeObjs {
		insuranceCatagories = append(insuranceCatagories, dtype.InsuranceCategory{
			Logo:       insuranceHomeObj.Logo,
			Name:       insuranceHomeObj.Name,
			UID:        insuranceHomeObj.UID,
			ActionType: insuranceHomeObj.ActionType,
			Action:     insuranceHomeObj.Action,
		})
	}

	return &dtype.InsuranceHomeResponse{
		Title:        InsuranceHomeTitle,
		SubTitle:     InsuranceHomeSubTitle,
		Categories:   insuranceCatagories,
		TopBanner:    s.GetInsuranceHomeTopBanner(ctx),
		BottomBanner: s.GetInsuranceHomeBottomBanner(ctx),
	}, nil
}

func (s *Service) GetInsuranceHomeTopBanner(ctx context.Context) []dtype.BannerItem {
	topBanner := make([]dtype.BannerItem, 0)
	topBannerCMSObj, err := s.repo.cmsData.GetActive(ctx, model.InsuranceIdentifierType, BannerIdentifierUID, InsuranceHomeTopBannerKey)
	if err != nil {
		util.Log.Infof("Error while fetching top banner for insurance home page, error : %s", err)
		return topBanner
	}

	err = json.Unmarshal([]byte(topBannerCMSObj.Value), &topBanner)
	if err != nil {
		util.Log.Infof("Error while Unmarshal Insurance Home top banner value, error : %s", err)
	}

	return topBanner
}

func (s *Service) GetInsuranceHomeBottomBanner(ctx context.Context) *dtype.BannerItem {
	var bottomBanner dtype.BannerItem
	bottomBannerCMSObj, err := s.repo.cmsData.GetActive(ctx, model.InsuranceIdentifierType, BannerIdentifierUID, InsuranceHomeBottomBannerKey)
	if err != nil {
		util.Log.Infof("Error while fetching bottom banner for insurance home page, error : %s", err)
		return nil
	}

	err = json.Unmarshal([]byte(bottomBannerCMSObj.Value), &bottomBanner)
	if err != nil {
		util.Log.Infof("Error while Unmarshal Insurance Home bottom banner value, error : %s", err)
		return nil
	}

	return &bottomBanner
}

func (s *Service) GetInsuranceSubCatagory(ctx context.Context, parentUID string) (*dtype.InsuranceSubCatagoryResponse, error) {

	catagoryObj, err := s.repo.insuranceTopologyRepo.GetByUIDAndLevel(ctx, parentUID, model.Catagory)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil {
		return nil, helper.NewCustomError("Invalid UID", http.StatusBadRequest)
	}

	subCatagoryObjs, err := s.repo.insuranceTopologyRepo.FilterByParentUIDAndLevel(ctx, parentUID, model.SubCatagory)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil {
		return nil, helper.NewCustomError("no sub-catagory found", http.StatusBadRequest)
	}

	insuranceSubCatagories := make([]dtype.InsuranceCategory, 0)
	for _, insuranceHomeObj := range subCatagoryObjs {
		insuranceSubCatagories = append(insuranceSubCatagories, dtype.InsuranceCategory{
			Logo:       insuranceHomeObj.Logo,
			Name:       insuranceHomeObj.Name,
			UID:        insuranceHomeObj.UID,
			ActionType: insuranceHomeObj.ActionType,
			Action:     insuranceHomeObj.Action,
		})
	}

	return &dtype.InsuranceSubCatagoryResponse{
		Title:         catagoryObj.Title,
		SubTitle:      catagoryObj.SubTitle,
		SubCategories: insuranceSubCatagories,
	}, nil
}

func (s *Service) GetInsuranceLisitingData(ctx context.Context, parentUID string) (*dtype.InsuranceListingResponse, error) {

	subCatObj, err := s.repo.insuranceTopologyRepo.GetByUIDAndLevel(ctx, parentUID, model.SubCatagory)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if err != nil {
		return nil, helper.NewCustomError("Invalid UID", http.StatusBadRequest)
	}

	insuranceObjs, err := s.repo.insuranceTopologyRepo.FilterByParentUIDAndLevel(ctx, parentUID, model.Listing)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if err != nil {
		return nil, helper.NewCustomError("no insurance found", http.StatusBadRequest)
	}

	insuranceRes := make([]dtype.InsuranceListingProduct, 0)
	for _, insuranceHomeObj := range insuranceObjs {
		var objMetaData dtype.InsuraneListingMetaData
		err := json.Unmarshal(insuranceHomeObj.MetaData, &objMetaData)
		if err != nil {
			util.Log.Errorf("error while unmarshal InsuranceListingProduct for UID, %s", insuranceHomeObj.UID)
		}

		insuranceRes = append(insuranceRes, dtype.InsuranceListingProduct{
			Logo:       insuranceHomeObj.Logo,
			Name:       insuranceHomeObj.Name,
			UID:        insuranceHomeObj.UID,
			ActionType: insuranceHomeObj.ActionType,
			Action:     insuranceHomeObj.Action,
			Comment:    objMetaData.Comment,
			KeyPoints:  objMetaData.KeyPoints,
		})
	}

	return &dtype.InsuranceListingResponse{
		Title:     subCatObj.Title,
		SubTitle:  subCatObj.SubTitle,
		Insurance: insuranceRes,
	}, nil
}

func (s *Service) GetInsuranceBanner(ctx context.Context) (*dtype.InsuranceBannerResponse, error) {
	insuranceBanner := make([]dtype.BannerItem, 0)
	insuranceBannerCMSObj, err := s.repo.cmsData.GetActive(ctx, model.InsuranceIdentifierType, BannerIdentifierUID, InsuranceBannerKey)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err

	}
	if err != nil {
		return &dtype.InsuranceBannerResponse{
			Banner: insuranceBanner,
		}, nil
	}

	err = json.Unmarshal([]byte(insuranceBannerCMSObj.Value), &insuranceBanner)
	if err != nil {
		return nil, err
	}

	return &dtype.InsuranceBannerResponse{
		Banner: insuranceBanner,
	}, nil
}

func (s *Service) SaveExternalApplication(ctx context.Context, request dtype.ExternalApplicationRequest) error {
	_, err := s.repo.insuranceExternalApplicationRepo.Create(ctx, request.Name, request.PolicyNumber, request.PhoneNumber, request.InsurerName)
	return err
}

func (s *Service) GetCMSData(ctx context.Context, identifierType, identifierUID, key string, result interface{}) error {
	identifierTypeValue := s.repo.cmsData.ConvertIdentifierType(identifierType)

	cmsObj, err := s.repo.cmsData.GetActive(ctx, identifierTypeValue, identifierUID, key)
	if err != nil {
		util.Log.Infof("Error while fetching cms data, error : %s", err)
		return err
	}

	err = json.Unmarshal([]byte(cmsObj.Value), result)
	if err != nil {
		util.Log.Infof("Error while Unmarshal cms data, error : %s", err)
		return err
	}

	return nil
}
