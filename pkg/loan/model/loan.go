package model

import "time"

type LoanStatusType int

type LeadStatusType int

const (
	IncompleteLoanStatus LoanStatusType = 1
	ApprovedLoanStatus   LoanStatusType = 3
	RejectedLoanStatus   LoanStatusType = 4
	DisbursedLoanStatus  LoanStatusType = 5

	LocApprovedLeadStatus  LeadStatusType = 10
	LOCRejectedLeadStatus  LeadStatusType = 11
	LOCRequestedLeadStatus LeadStatusType = 80
)

type Loan struct {
	ID                uint64         `gorm:"primaryKey;column:id"`
	CustomerID        int64          `gorm:"column:customer_id"`
	LoanStatus        LoanStatusType `gorm:"column:loan_status"`
	LeadStatus        LeadStatusType `gorm:"column:lead_status"`
	RandomLoanID      string         `gorm:"column:random_loan_id"`
	LoanCreationDate  time.Time      `gorm:"column:loan_creation_date"`
	UTMSource         string         `gorm:"column:utm_source"`
	OriginalUTMSource string         `gorm:"column:original_utm_source"`
	Origin            string         `gorm:"column:origin"`
	UTMMedium         string         `gorm:"column:utm_medium"`
	UTMCampaign       string         `gorm:"column:utm_campaign"`
	RandomAllotment   int            `gorm:"column:random_allotment"` // random.randint(1, 99)
	ProductCode       string         `gorm:"column:product_code"`
	UpdateDate        time.Time      `gorm:"column:update_date"`
}

func (*Loan) TableName() string {
	return "st_loan"
}
