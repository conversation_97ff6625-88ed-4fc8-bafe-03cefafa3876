package repo

import (
	"context"
	"fmt"
	"math/rand"
	"time"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/loan/model"
	"stashfin.com/stashfin/magneto/util"
)

const letterBytes = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

type Loan struct{}

func NewLoanRepo() *Loan {
	return &Loan{}
}

func (*Loan) Get(ctx context.Context, customerID int64, loanStatus model.LoanStatusType, leadStatus model.LeadStatusType) (*model.Loan, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.Loan
	err := db.WithContext(ctx).
		Where(&model.Loan{CustomerID: customerID, LoanStatus: loanStatus, LeadStatus: leadStatus}).
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*Loan) GetWithLoanStatus(ctx context.Context, customerID int64, loanStatus model.LoanStatusType) (*model.Loan, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.Loan
	err := db.WithContext(ctx).
		Where(&model.Loan{CustomerID: customerID, LoanStatus: loanStatus}).
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*Loan) GetWithArmyUtmSource(ctx context.Context, customerId int64) (*[]model.Loan, error) {
	var loanModels []model.Loan
	db := config.GetConnectionCtx(ctx)

	err := db.Where("customer_id = ? and (utm_source like 'ARMY%' or utm_source like  'idd%' or utm_source like  'irp%')", customerId).Find(&loanModels).Error
	if err != nil {
		return nil, err
	}

	return &loanModels, nil
}

func (*Loan) GetMaxLoanId(ctx context.Context, customerId int64) (int64, error) {
	var maxLoanId int64
	db := config.GetConnectionCtx(ctx)

	err := db.Table("st_loan").Select("MAX(id)").Where("customer_id = ?", customerId).Scan(&maxLoanId).Error
	if err != nil {
		return 0, err
	}

	return maxLoanId, nil
}

func (*Loan) IsLocRequested(ctx context.Context, customerID int64) (bool, error) {
	db := config.GetConnectionCtx(ctx)

	var count int64
	err := db.WithContext(ctx).
		Model(&model.Loan{}).
		Where(&model.Loan{CustomerID: customerID, LeadStatus: model.LOCRequestedLeadStatus}).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

func (*Loan) IsLoanApproved(ctx context.Context, customerID int64) (bool, error) {
	db := config.GetConnectionCtx(ctx)

	var count int64
	err := db.WithContext(ctx).
		Model(&model.Loan{}).
		Where(&model.Loan{CustomerID: customerID, LoanStatus: model.ApprovedLoanStatus}).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

func (*Loan) IsLocApprovedLeadStatus(ctx context.Context, customerID int64) (bool, error) {
	db := config.GetConnectionCtx(ctx)

	var count int64
	err := db.WithContext(ctx).
		Model(&model.Loan{}).
		Where(&model.Loan{CustomerID: customerID, LeadStatus: model.LocApprovedLeadStatus}).
		Count(&count).Error

	if err != nil {
		return false, err
	}

	return count > 0, nil
}

func (*Loan) generateLoanID() string {
	b := make([]byte, 5)
	for i := range b {
		b[i] = letterBytes[rand.Intn(len(letterBytes))]
	}

	return fmt.Sprintf("SE%s%s%d", time.Now().Format("0201"), string(b), rand.Intn(10_000)+1_000)
}

func (l *Loan) Create(
	ctx context.Context,
	customerID int64,
	loanStatus model.LoanStatusType,
	leadStatus model.LeadStatusType,
	utmSource, utmMedium, utmCampaign, productCode string,
) (*model.Loan, error) {
	db := config.GetConnectionCtx(ctx)

	obj := model.Loan{
		CustomerID:        customerID,
		LoanStatus:        loanStatus,
		LeadStatus:        leadStatus,
		RandomLoanID:      l.generateLoanID(),
		LoanCreationDate:  time.Now(),
		UTMSource:         utmSource,
		OriginalUTMSource: utmSource,
		Origin:            utmSource,
		UTMMedium:         utmMedium,
		UTMCampaign:       utmCampaign,
		RandomAllotment:   util.RandomInt(1, 99),
		ProductCode:       productCode,
		UpdateDate:        time.Now(),
	}

	err := db.WithContext(ctx).Create(&obj).Error
	if err != nil {
		return nil, err
	}
	return &obj, nil
}

func (*Loan) GetByLoanID(ctx context.Context, loanID int64) (*model.Loan, error) {
	var loanObj model.Loan

	db := config.GetConnectionCtx(ctx)
	err := db.Where("id = ?", loanID).Last(&loanObj).Error
	if err != nil {
		return nil, err
	}

	return &loanObj, nil
}

func (*Loan) GetByCustomerID(ctx context.Context, customerID int64) ([]model.Loan, error) {
	db := config.GetConnectionCtx(ctx)

	var loanModels []model.Loan

	err := db.WithContext(ctx).Where("customer_id = ?", customerID).Order("id").Find(&loanModels).Error
	if err != nil {
		return nil, err
	}

	return loanModels, nil
}
