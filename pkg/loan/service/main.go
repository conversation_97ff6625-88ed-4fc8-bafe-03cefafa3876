package service

import (
	"context"
	"errors"

	"gorm.io/gorm"

	"stashfin.com/stashfin/magneto/pkg/loan/model"
	"stashfin.com/stashfin/magneto/util"
)

func (s *Service) HasCustomerDisbursedLoan(ctx context.Context, customerID int64) (bool, error) {
	_, err := s.repo.loan.GetWithLoanStatus(ctx, customerID, model.DisbursedLoanStatus)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return false, err
	}

	if err != nil {
		return false, nil
	}

	return true, nil
}

func (s *Service) CreateLoan(ctx context.Context, customerID int64, utmSource, utmMedium, utmCampaign, productCode string) error {
	_, err := s.repo.loan.Create(ctx, customerID, 1, 1, utmSource, utmMedium, utmCampaign, productCode)
	return err
}

func (s *Service) CheckArmyCustomer(ctx context.Context, customerId int64) (bool, error) {
	loanModels, err := s.repo.loan.GetWithArmyUtmSource(ctx, customerId)
	if err != nil {
		return false, err
	}

	if len(*loanModels) > 0 {
		return true, err
	}

	return false, nil
}

func (s *Service) GetMaxLoanId(ctx context.Context, customerId int64) (int64, error) {
	maxLoanId, err := s.repo.loan.GetMaxLoanId(ctx, customerId)
	if err != nil {
		return 0, err
	}

	return maxLoanId, nil
}

func (s *Service) CheckApprovedNewLoan(ctx context.Context, customerID int64) (bool, error) {

	_, err := s.repo.loan.Get(ctx, customerID, model.ApprovedLoanStatus, model.LocApprovedLeadStatus)
	if util.IsServiceError(err) {
		return false, err
	}

	if err != nil {
		return false, nil
	}

	return true, nil
}

func (s *Service) IsLocOpen(ctx context.Context, customerID int64) (bool, error) {
	locRequested, err := s.repo.loan.IsLocRequested(ctx, customerID)
	if err != nil {
		return false, err
	}

	locLoanApproved, err := s.repo.loan.IsLoanApproved(ctx, customerID)
	if err != nil {
		return false, err
	}

	locLeadApproved, err := s.repo.loan.IsLocApprovedLeadStatus(ctx, customerID)
	if err != nil {
		return false, err
	}

	return locRequested || locLoanApproved || locLeadApproved, nil
}

func (s *Service) IsRejectedCustomer(ctx context.Context, customerID int64) (bool, error) {
	maxLoanId, err := s.GetMaxLoanId(ctx, customerID)
	if err != nil {
		return true, err
	}

	loanObj, err := s.repo.loan.GetByLoanID(ctx, maxLoanId)
	if err != nil {
		return true, err
	}

	isLOCRejected := (loanObj.LoanStatus == model.RejectedLoanStatus) && (loanObj.LeadStatus == model.LOCRejectedLeadStatus)
	return isLOCRejected, nil
}

func (s *Service) IsCustomerJourneyIncomplete(ctx context.Context, customerID int64) (bool, error) {
	customerLoanObjs, err := s.repo.loan.GetByCustomerID(ctx, customerID)
	if err != nil {
		return false, err
	}

	if len(customerLoanObjs) > 1 {
		return false, nil
	}

	return customerLoanObjs[0].LoanStatus == model.IncompleteLoanStatus, nil
}
