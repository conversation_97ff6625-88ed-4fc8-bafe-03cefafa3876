package pkg

import (
	"stashfin.com/stashfin/magneto/pkg/cli"
	"stashfin.com/stashfin/magneto/pkg/insurance"
	"stashfin.com/stashfin/magneto/pkg/loan"
	"stashfin.com/stashfin/magneto/pkg/offer"
	"stashfin.com/stashfin/magneto/pkg/ordersystem"
	"stashfin.com/stashfin/magneto/pkg/restructure"
)

func InitializeApps() {
	insurance.InitializeServices()
	loan.InitializeService()
	cli.InitializeServices()
	restructure.InitializeServices()
	offerService := offer.InitializeServices()

	ordersystem.InitializeService(offerService)
}
