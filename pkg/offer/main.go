package offer

import (
	"stashfin.com/stashfin/magneto/pkg/offer/repo"
	"stashfin.com/stashfin/magneto/pkg/offer/service"
	"stashfin.com/stashfin/magneto/pkg/offer/service/vendor"
)

var serviceInstance *service.Service

func InitializeServices() *service.Service {
	offerProductRepo := repo.NewOfferProduct()
	offerDetailRepo := repo.NewOfferDetailRepo()
	offerExtraDetailRepo := repo.NewOfferExtraDetailRepo()
	userOfferPaymentRepo := repo.NewUserOfferPayment()
	offerInternalCouponRepo := repo.NewOfferInternalCouponRepo()

	vendor := &service.Vendors{
		Internal: &vendor.InternalCouponFetcher{
			OfferInternalRepo: offerInternalCouponRepo,
		},
	}

	serviceInstance = service.NewService(
		service.NewRepo(
			offerProductRepo,
			offerDetailRepo,
			offerExtraDetailRepo,
			userOfferPaymentRepo,
		),
		vendor,
	)

	return serviceInstance
}

func GetService() *service.Service {
	return serviceInstance
}
