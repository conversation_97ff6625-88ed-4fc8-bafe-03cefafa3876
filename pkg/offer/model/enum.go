package model

type PaymentStatus int

const (
	PendingPaymentStatus   PaymentStatus = 0
	InitiatedPaymentStatus PaymentStatus = 1
	SuccessPaymentStatus   PaymentStatus = 2
	FailedPaymentStatus    PaymentStatus = 3
	CanceledPaymentStatus  PaymentStatus = 4
)

func (p PaymentStatus) String() string {
	switch p {
	case PendingPaymentStatus:
		return "Pending"
	case InitiatedPaymentStatus:
		return "Initiated"
	case SuccessPaymentStatus:
		return "Success"
	case FailedPaymentStatus:
		return "Failed"
	case CanceledPaymentStatus:
		return "Canceled"
	default:
		return "Unknown"
	}
}

func PaymentStatusFromInt(status int) PaymentStatus {
	switch status {
	case 0:
		return PendingPaymentStatus
	case 1:
		return InitiatedPaymentStatus
	case 2:
		return SuccessPaymentStatus
	case 3:
		return FailedPaymentStatus
	case 4:
		return CanceledPaymentStatus
	default:
		return PendingPaymentStatus
	}
}

type OfferCouponStatus int

const (
	InitiatedOfferStatus OfferCouponStatus = 0
	PendingOfferStatus   OfferCouponStatus = 1
	SuccessOfferStatus   OfferCouponStatus = 2
	FailedOfferStatus    OfferCouponStatus = 3
	CanceledOfferStatus  OfferCouponStatus = 4
)

func (p OfferCouponStatus) String() string {
	switch p {
	case PendingOfferStatus:
		return "Pending"
	case InitiatedOfferStatus:
		return "Initiated"
	case SuccessOfferStatus:
		return "Success"
	case FailedOfferStatus:
		return "Failed"
	case CanceledOfferStatus:
		return "Canceled"
	default:
		return "Unknown"
	}
}

func OfferStatusFromInt(status int) OfferCouponStatus {
	switch status {
	case 0:
		return InitiatedOfferStatus
	case 1:
		return PendingOfferStatus
	case 2:
		return SuccessOfferStatus
	case 3:
		return FailedOfferStatus
	case 4:
		return CanceledOfferStatus
	default:
		return PendingOfferStatus
	}
}
