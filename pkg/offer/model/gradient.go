package model

import (
	"gorm.io/datatypes"
)

type Gradient struct {
	ID             int            `gorm:"column:id"`
	Name           string         `gorm:"column:name"`
	StartColor     string         `gorm:"column:start_color"`
	EndColor       string         `gorm:"column:end_color"`
	StartPosition  float64        `gorm:"column:start_position"`
	EndPosition    float64        `gorm:"column:end_position"`
	Angle          float64        `gorm:"column:angle"`
	AdditionalData datatypes.JSON `gorm:"column:additional_data"`
}

func (*Gradient) TableName() string {
	return "gradients"
}
