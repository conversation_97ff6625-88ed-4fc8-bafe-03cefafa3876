package model

import (
	"time"
)

type OfferDetail struct {
	ID                  int               `gorm:"column:id"`
	CreatedAt           time.Time         `gorm:"column:created_at"`
	UpdatedAt           time.Time         `gorm:"column:updated_at"`
	IsActive            bool              `gorm:"column:is_active"`
	UID                 string            `gorm:"column:uid"`
	Type                string            `gorm:"column:type"`
	BrandProductCode    string            `gorm:"column:brand_product_code"`
	BrandName           string            `gorm:"column:brand_name"`
	BrandType           string            `gorm:"column:brand_type"`
	BrandImage          string            `gorm:"column:brand_image"`
	Thumbnail           string            `gorm:"column:thumbnail"`
	IsStockAvailable    bool              `gorm:"column:is_stock_available"`
	Category            string            `gorm:"column:category"`
	Icon                string            `gorm:"column:icon"`
	DiscountPercentage  float32           `gorm:"column:discount_percentage"`
	GradientID          int               `gorm:"column:gradient_id"`
	OfferExtraDetailsID int               `gorm:"column:offer_extra_details_id"`
	Gradient            *Gradient         `gorm:"foreignKey:gradient_id"`
	OfferExtraDetails   *OfferExtraDetail `gorm:"foreignKey:offer_extra_details_id"`
}

func (*OfferDetail) TableName() string {
	return "offer_detail"
}
