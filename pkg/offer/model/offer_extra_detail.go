package model

import (
	"encoding/json"

	"gorm.io/datatypes"
	"stashfin.com/stashfin/magneto/util"
)

type OfferExtraDetail struct {
	ID                   int            `gorm:"column:id;primaryKey"`
	TNC                  string         `gorm:"column:tnc"`
	ImportantInstruction string         `gorm:"column:important_instruction"`
	RedeemSteps          datatypes.JSON `gorm:"column:redeem_steps"`
	KeyPoints            datatypes.JSON `gorm:"column:key_points"`
	OfferText            string         `gorm:"column:offer_text"`
	PromotionText        string         `gorm:"column:promotion_text"`
	DenominationList     string         `gorm:"column:denomination_list"`
}

func (*OfferExtraDetail) TableName() string {
	return "offer_extra_details"
}

func (o *OfferExtraDetail) GetRedeemSteps() []string {
	var result []string
	err := json.Unmarshal([]byte(o.RedeemSteps), &result)
	if err != nil {
		util.Log.Infof("Error while Unmarshal cms data, error : %s", err)
		return result
	}
	return result
}

func (o *OfferExtraDetail) GetKeyPoints() []string {
	var result []string
	err := json.Unmarshal([]byte(o.KeyPoints), &result)
	if err != nil {
		util.Log.Infof("Error while Unmarshal cms data, error : %s", err)
		return result
	}
	return result
}
