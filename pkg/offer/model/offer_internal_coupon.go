package model

import (
	"time"
)

type OfferInternalCoupon struct {
	ID             int        `gorm:"primaryKey;column:id"`
	VendorType     int        `gorm:"column:vendor_type"`
	CouponCode     string     `gorm:"column:coupon_code"`
	CreatedAt      time.Time  `gorm:"column:created_at"`
	UpdatedAt      time.Time  `gorm:"column:updated_at"`
	IsAssigned     bool       `gorm:"column:is_assigned"`
	CouponPin      string     `gorm:"column:coupon_pin"`
	CouponValidity *time.Time `gorm:"column:coupon_validity"`
}

func (*OfferInternalCoupon) TableName() string {
	return "offer_internal_coupon"
}
