package model

import (
	"fmt"
	"time"
)

type OfferProduct struct {
	ID            int          `gorm:"column:id"`
	UID           string       `gorm:"column:uid"`
	CreatedAt     time.Time    `gorm:"column:created_at"`
	UpdatedAt     time.Time    `gorm:"column:updated_at"`
	IsActive      bool         `gorm:"column:is_active"`
	OfferDetailID int          `gorm:"column:offer_detail_id"`
	OfferDetail   *OfferDetail `gorm:"foreignKey:offer_detail_id"`
	Price         float32      `gorm:"column:price"`
	Validity      int          `gorm:"column:validity"`
	StockQuantity int          `gorm:"column:stock_quantity"`
	OfferVendorID int          `gorm:"column:offer_vendor_id"`
	OfferVendor   *OfferVendor `gorm:"foreignKey:offer_vendor_id"`
}

func (*OfferProduct) TableName() string {
	return "offer_product"
}

func (o *OfferProduct) GetValidityString() string {
	if o.Validity == 1 {
		return "1 month"
	} else if o.Validity > 1 {
		return fmt.Sprintf("%d months", o.Validity)
	} else {
		return ""
	}
}
