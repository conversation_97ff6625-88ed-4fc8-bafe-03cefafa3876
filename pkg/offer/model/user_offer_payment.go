package model

import (
	"time"

	"stashfin.com/stashfin/magneto/util"
)

type UserOfferPayment struct {
	ID             int               `gorm:"column:id"`
	CreatedAt      time.Time         `gorm:"column:created_at"`
	UpdatedAt      time.Time         `gorm:"column:updated_at"`
	IsActive       bool              `gorm:"column:is_active"`
	UserID         int64             `gorm:"column:user_id"`
	OfferProductID int               `gorm:"column:offer_product_id"`
	OfferProduct   *OfferProduct     `gorm:"foreignKey:offer_product_id"`
	PaymentStatus  PaymentStatus     `gorm:"column:payment_status"`
	OfferStatus    OfferCouponStatus `gorm:"column:offer_status"`
	OrderID        string            `gorm:"column:order_id"`
	OrderItemID    string            `gorm:"column:order_item_id"`
	CouponCode     string            `gorm:"column:coupon_code"`
	CouponPin      string            `gorm:"column:coupon_pin"`
	CouponValidity *time.Time        `gorm:"column:coupon_validity"`
}

func (*UserOfferPayment) TableName() string {
	return "user_offer_payment"
}

func (obj *UserOfferPayment) GetValidityString() string {
	if obj.CouponValidity == nil {
		return ""
	}
	return util.ParseDateTimeIntoDateString(*obj.CouponValidity)
}

func (u *UserOfferPayment) GetCouponCode() (string, error) {
	return util.GetEncryptor().DecryptString(u.CouponCode)
}
