package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
)

type OfferDetailRepo struct{}

func NewOfferDetailRepo() *OfferDetailRepo {
	return &OfferDetailRepo{}
}

func (*OfferDetailRepo) IsUniqueUID(ctx context.Context, uid string) (bool, error) {
	db := config.GetConnectionCtx(ctx)
	var count int64
	err := db.WithContext(ctx).
		Model(&model.OfferDetail{}).
		Where(&model.OfferDetail{UID: uid}).
		Count(&count).Error
	if err != nil {
		return false, err
	}

	return count == 0, nil
}

func (*OfferDetailRepo) GetAllActive(ctx context.Context) ([]model.OfferDetail, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []model.OfferDetail
	err := db.WithContext(ctx).
		Joins("Gradient").
		Joins("OfferExtraDetails").
		Where(&model.OfferDetail{IsActive: true}).
		Find(&obj).Error
	if err != nil {
		return nil, err
	}

	return obj, nil
}

func (*OfferDetailRepo) GetByUID(ctx context.Context, OfferUID string) (*model.OfferDetail, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.OfferDetail
	err := db.WithContext(ctx).
		Joins("OfferExtraDetails").
		Where(&model.OfferDetail{UID: OfferUID}).
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*OfferDetailRepo) GetByProductCode(ctx context.Context, brandProductCode string) (*model.OfferDetail, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.OfferDetail
	err := db.WithContext(ctx).
		Joins("OfferExtraDetails").
		Where(&model.OfferDetail{BrandProductCode: brandProductCode}).
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (r *OfferDetailRepo) Create(ctx context.Context, offerDetail *model.OfferDetail) (*model.OfferDetail, error) {
	uid, err := helper.GenerateUniqueUID(ctx, r, helper.UIDSize8)
	if err != nil {
		return nil, err
	}

	offerDetail.UID = uid

	obj, err := helper.GenericCreate[model.OfferDetail](ctx, offerDetail)
	if err != nil {
		return nil, err
	}
	return obj, nil
}

func (*OfferDetailRepo) Update(ctx context.Context, offerDetail *model.OfferDetail, updateFields []string) error {
	return helper.GenericUpdate[model.OfferDetail](ctx, offerDetail, updateFields)
}
