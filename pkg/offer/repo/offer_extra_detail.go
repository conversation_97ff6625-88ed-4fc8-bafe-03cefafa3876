package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
)

type OfferExtraDetailRepo struct{}

func NewOfferExtraDetailRepo() *OfferExtraDetailRepo {
	return &OfferExtraDetailRepo{}
}

func (*OfferExtraDetailRepo) Create(ctx context.Context, offerExtraDetail *model.OfferExtraDetail) (*model.OfferExtraDetail, error) {
	obj, err := helper.GenericCreate[model.OfferExtraDetail](ctx, offerExtraDetail)
	if err != nil {
		return nil, err
	}
	return obj, nil
}

func (*OfferExtraDetailRepo) Update(ctx context.Context, offerExtraDetail *model.OfferExtraDetail, updateFields []string) error {
	return helper.GenericUpdate[model.OfferExtraDetail](ctx, offerExtraDetail, updateFields)
}
