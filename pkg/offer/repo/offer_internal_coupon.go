package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
)

type OfferInternalCouponRepo struct{}

func NewOfferInternalCouponRepo() *OfferInternalCouponRepo {
	return &OfferInternalCouponRepo{}
}

func (repo *OfferInternalCouponRepo) GetByVendorType(ctx context.Context, vendorType int) (*model.OfferInternalCoupon, error) {

	db := config.GetConnectionCtx(ctx)

	var obj model.OfferInternalCoupon
	err := db.WithContext(ctx).
		Where(&model.OfferInternalCoupon{VendorType: vendorType, IsAssigned: false}).
		First(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*OfferInternalCouponRepo) Update(ctx context.Context, offerInternalCouponRepo *model.OfferInternalCoupon, updateFields []string) error {
	return helper.GenericUpdate[model.OfferInternalCoupon](ctx, offerInternalCouponRepo, updateFields)
}
