package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
)

type OfferProductRepo struct{}

func NewOfferProduct() *OfferProductRepo {
	return &OfferProductRepo{}
}

func (*OfferProductRepo) IsUniqueUID(ctx context.Context, uid string) (bool, error) {
	db := config.GetConnectionCtx(ctx)
	var count int64
	err := db.WithContext(ctx).
		Model(&model.OfferProduct{}).
		Where(&model.OfferProduct{UID: uid}).
		Count(&count).Error
	if err != nil {
		return false, err
	}

	return count == 0, nil
}

func (*OfferProductRepo) FilterActiveByOfferDetail(ctx context.Context, offerDetialID int) ([]model.OfferProduct, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []model.OfferProduct
	err := db.WithContext(ctx).
		Where(&model.OfferProduct{OfferDetailID: offerDetialID, IsActive: true}).
		Find(&obj).Error
	if err != nil {
		return nil, err
	}

	return obj, nil
}

func (*OfferProductRepo) GetByID(ctx context.Context, offerID int) (*model.OfferProduct, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.OfferProduct
	err := db.WithContext(ctx).
		Joins("OfferDetail").
		Joins("OfferDetail.Gradient").
		Joins("OfferDetail.OfferExtraDetails").
		Where(&model.OfferProduct{ID: offerID}).
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*OfferProductRepo) GetBulkByUIDs(ctx context.Context, offerUIDs []string) ([]model.OfferProduct, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []model.OfferProduct
	err := db.WithContext(ctx).
		Model(&model.OfferProduct{}).
		Joins("OfferDetail").
		Where("offer_product.uid IN ?", offerUIDs).
		Scan(&obj).Error
	if err != nil {
		return nil, err
	}

	return obj, nil
}

func (*OfferProductRepo) GetBulkByIDs(ctx context.Context, offerIDs []int) ([]model.OfferProduct, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []model.OfferProduct
	err := db.WithContext(ctx).
		Joins("OfferDetail").
		Joins("OfferDetail.Gradient").
		Joins("OfferDetail.OfferExtraDetails").
		Where("id IN ?", offerIDs).
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return obj, nil
}

func (r *OfferProductRepo) Create(ctx context.Context, offerProduct *model.OfferProduct) (*model.OfferProduct, error) {
	uid, err := helper.GenerateUniqueUID(ctx, r, helper.UIDSize8)
	if err != nil {
		return nil, err
	}

	offerProduct.UID = uid

	obj, err := helper.GenericCreate[model.OfferProduct](ctx, offerProduct)
	if err != nil {
		return nil, err
	}
	return obj, nil
}

func (*OfferProductRepo) GetBulkIDFromUID(ctx context.Context, uid []string) (map[string]int, error) {
	db := config.GetConnectionCtx(ctx)

	var objs []model.OfferProduct
	err := db.WithContext(ctx).
		Select("id", "uid").
		Model(&model.OfferProduct{}).
		Where("offer_product.uid IN ?", uid).
		Scan(&objs).Error
	if err != nil {
		return nil, err
	}

	uidMap := make(map[string]int)
	for _, obj := range objs {
		uidMap[obj.UID] = obj.ID
	}

	return uidMap, nil
}

func (*OfferProductRepo) FilterAllByOfferDetailID(ctx context.Context, offerDetailID int) ([]float32, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []float32
	err := db.WithContext(ctx).
		Model(&model.OfferProduct{}).
		Select("price").
		Where(&model.OfferProduct{OfferDetailID: offerDetailID}).
		Find(&obj).Error

	if err != nil {
		return nil, err
	}

	return obj, nil
}

func (*OfferProductRepo) GetBulkFromUID(ctx context.Context, uid []string) (map[string]model.OfferProduct, error) {
	db := config.GetConnectionCtx(ctx)

	var objs []model.OfferProduct
	err := db.WithContext(ctx).
		Model(&model.OfferProduct{}).
		Joins("OfferDetail").
		Where("offer_product.uid IN ?", uid).
		Scan(&objs).Error
	if err != nil {
		return nil, err
	}

	uidMap := make(map[string]model.OfferProduct)
	for _, obj := range objs {
		uidMap[obj.UID] = obj
	}

	return uidMap, nil
}




func (*OfferProductRepo) CheckAmazonOffers(ctx context.Context, orderID string) (bool, error) {
    db := config.GetConnectionCtx(ctx)

    var count int64

    err := db.WithContext(ctx).
        Table("user_offer_payment uop").
        Joins("inner join offer_product op on op.id = uop.offer_product_id").
        Joins("inner join offer_detail od on od.id = op.offer_detail_id").
        Where("uop.order_id = ?", orderID).
        Where("od.brand_name = ?", "Amazon").
        Count(&count).Error

    if err != nil {
        return false, err
    }

	if count > 0 {
		return true , nil
	}

    return  false, nil
}
