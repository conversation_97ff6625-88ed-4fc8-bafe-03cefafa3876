package repo

import (
	"context"
	"fmt"
	"time"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
	"stashfin.com/stashfin/magneto/util"
)

type UserOfferPayment struct{}

func NewUserOfferPayment() *UserOfferPayment {
	return &UserOfferPayment{}
}

func (*UserOfferPayment) Create(
	ctx context.Context,
	userID int64,
	offerProductID int,
	orderID, orderItemID string,
) (*model.UserOfferPayment, error) {
	obj := model.UserOfferPayment{
		IsActive:       true,
		UserID:         userID,
		OfferProductID: offerProductID,
		PaymentStatus:  model.InitiatedPaymentStatus,
		OfferStatus:    model.InitiatedOfferStatus,
		OrderID:        orderID,
		OrderItemID:    orderItemID,
		CouponCode:     "",
	}
	return helper.GenericCreate(ctx, &obj)
}

func (*UserOfferPayment) GetActiveByUserID(ctx context.Context, userID int64) ([]model.UserOfferPayment, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []model.UserOfferPayment
	err := db.WithContext(ctx).
		Joins("OfferProduct").
		Joins("OfferProduct.OfferDetail").
		Where(&model.UserOfferPayment{UserID: userID, IsActive: true}).
		Order("id desc").
		Find(&obj).Error
	if err != nil {
		return nil, err
	}

	return obj, nil
}

func (*UserOfferPayment) GetByOrderIDUserID(ctx context.Context, orderID string, userID int64) ([]model.UserOfferPayment, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []model.UserOfferPayment
	err := db.WithContext(ctx).
		Joins("OfferProduct").
		Joins("OfferProduct.OfferDetail").
		Joins("OfferProduct.OfferVendor").
		Where(&model.UserOfferPayment{OrderID: orderID, UserID: userID}).
		Find(&obj).Error
	if err != nil {
		return nil, err
	}

	return obj, nil
}

func (*UserOfferPayment) GetByOrderID(ctx context.Context, orderID string) ([]model.UserOfferPayment, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []model.UserOfferPayment
	err := db.WithContext(ctx).
		Joins("OfferProduct").
		Joins("OfferProduct.OfferDetail").
		Joins("OfferProduct.OfferVendor").
		Where(&model.UserOfferPayment{OrderID: orderID}).
		Find(&obj).Error
	if err != nil {
		return nil, err
	}

	return obj, nil
}

func (*UserOfferPayment) UpdatePaymentStatus(ctx context.Context, orderID string, status model.PaymentStatus) error {
	db := config.GetConnectionCtx(ctx)

	err := db.WithContext(ctx).
		Where(&model.UserOfferPayment{OrderID: orderID}).
		Updates(&model.UserOfferPayment{PaymentStatus: status}).Error

	return err
}

func (*UserOfferPayment) UpdateOfferStatus(ctx context.Context, orderID string, status model.OfferCouponStatus) error {
	db := config.GetConnectionCtx(ctx)

	err := db.WithContext(ctx).
		Where(&model.UserOfferPayment{OrderID: orderID}).
		Updates(&model.UserOfferPayment{OfferStatus: status}).Error

	return err
}
func (*UserOfferPayment) UpdateOfferStatusById(ctx context.Context, offerPaymentId int, status model.OfferCouponStatus) error {
	db := config.GetConnectionCtx(ctx)

	err := db.WithContext(ctx).
		Where(&model.UserOfferPayment{ID: offerPaymentId}).
		Updates(&model.UserOfferPayment{OfferStatus: status}).Error

	return err
}

func (*UserOfferPayment) UpdateCouponDetails(ctx context.Context, id int, couponCode string, couponPin string, couponValidity *time.Time) error {
	db := config.GetConnectionCtx(ctx)

	couponCode, err := util.GetEncryptor().EncryptString(couponCode)
	if err != nil {
		return fmt.Errorf("couldn't encrypt coupon code: %w", err)
	}

	err = db.WithContext(ctx).
		Where(&model.UserOfferPayment{ID: id}).
		Updates(&model.UserOfferPayment{
			CouponCode:     couponCode,
			CouponPin:      couponPin,
			CouponValidity: couponValidity,
		}).Error

	return err
}

func (*UserOfferPayment) UpdateItemCouponStatus(ctx context.Context, id int, status model.OfferCouponStatus) error {
	db := config.GetConnectionCtx(ctx)

	err := db.WithContext(ctx).
		Where(&model.UserOfferPayment{ID: id}).
		Updates(&model.UserOfferPayment{OfferStatus: status}).Error

	return err
}

func (*UserOfferPayment) GetBulkByOrderItem(ctx context.Context, orderItemUIDs []string) (map[string]model.UserOfferPayment, error) {
	db := config.GetConnectionCtx(ctx)

	var objs []model.UserOfferPayment
	err := db.WithContext(ctx).
		Model(model.UserOfferPayment{}).
		Joins("OfferProduct").
		Joins("OfferProduct.OfferDetail").
		Joins("OfferProduct.OfferDetail.OfferExtraDetails").
		Where("order_item_id IN ?", orderItemUIDs).
		Scan(&objs).Error
	if err != nil {
		return nil, err
	}

	uidMap := make(map[string]model.UserOfferPayment)
	for _, obj := range objs {
		uidMap[obj.OrderItemID] = obj
	}

	return uidMap, nil
}

func (*UserOfferPayment) MarkUnfetchedCouponFailed(ctx context.Context, userID int64) error {
	db := config.GetConnectionCtx(ctx)

	// get current time - one hour
	timeBuffer := time.Now().Add(-1 * time.Hour)

	err := db.WithContext(ctx).
		Model(model.UserOfferPayment{}).
		Where("user_id = ? AND updated_at < ? AND offer_status = ? AND payment_status = ?", userID, timeBuffer, model.InitiatedOfferStatus, model.SuccessPaymentStatus).
		Updates(&model.UserOfferPayment{
			OfferStatus: model.FailedOfferStatus,
		}).Error

	return err
}

func (*UserOfferPayment) GetRetryCouponObj(ctx context.Context, userID int64) ([]model.UserOfferPayment, error) {
	db := config.GetConnectionCtx(ctx)

	// get current time - one hour
	timeBuffer := time.Now().Add(-1 * time.Hour)

	var objs []model.UserOfferPayment
	err := db.WithContext(ctx).
		Model(model.UserOfferPayment{}).
		Where("user_id = ? AND updated_at >= ? AND offer_status = ? AND payment_status = ?", userID, timeBuffer, model.InitiatedOfferStatus, model.SuccessPaymentStatus).
		Scan(&objs).Error

	if err != nil {
		return nil, err
	}

	return objs, err
}
