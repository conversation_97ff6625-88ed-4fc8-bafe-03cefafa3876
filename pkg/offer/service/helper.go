package service

import (
	"context"
	"errors"
	"fmt"
	"math"

	"stashfin.com/stashfin/magneto/api/offer"
	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/pkg/loan"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
	"stashfin.com/stashfin/magneto/pkg/ordersystem"
	orderService "stashfin.com/stashfin/magneto/pkg/ordersystem/service"
	"stashfin.com/stashfin/magneto/util"
)

func (s *Service) UpdateCartHelper(ctx context.Context, offerProductUID string, quantity int, customerID int64) error {
	err := ordersystem.GetService().UpdateOfferCart(ctx, customerID, offerProductUID, quantity)
	if err != nil && errors.Is(err, orderService.ErrSKUNotFound) {
		return helper.BadRequestError("invalid product")
	}

	return err
}

func (s *Service) GetCartHelper(ctx context.Context, customerID int64) (*offer.GetCartResponse, error) {
	logger := util.CLogCtx(ctx)
	cartDetail, err := ordersystem.GetService().GetOfferCart(ctx, customerID)
	if err != nil && errors.Is(err, orderService.ErrCartNotFound) {
		return &offer.GetCartResponse{}, nil
	}

	if err != nil {
		return nil, fmt.Errorf("couldn't get the cart items, err: %w", err)
	}

	identifierUIDs := make([]string, 0, len(cartDetail.Items))
	for _, item := range cartDetail.Items {
		identifierUIDs = append(identifierUIDs, item.IdentifierUID)
	}

	offerProductObjs, err := s.repo.OfferProduct.GetBulkByUIDs(ctx, identifierUIDs)
	if err != nil {
		return nil, fmt.Errorf("error fetching offer product, err: %w", err)
	}

	offerProductObjMapping := make(map[string]model.OfferProduct)
	for _, obj := range offerProductObjs {
		offerProductObjMapping[obj.UID] = obj
	}

	cartItems := make([]offer.CartItem, 0)
	for _, cartItem := range cartDetail.Items {
		offerProductObj, ok := offerProductObjMapping[cartItem.IdentifierUID]
		if !ok {
			continue
		}

		cartItems = append(cartItems, offer.CartItem{
			Title:              offerProductObj.OfferDetail.BrandName,
			Icon:               offerProductObj.OfferDetail.Icon,
			UID:                cartItem.IdentifierUID,
			DiscountPercentage: cartItem.DiscountPercentage,
			OriginalPrice:      cartItem.OriginalPrice,
			DiscountedPrice:    cartItem.DiscountedPrice,
			Quantity:           cartItem.Quantity,
		})
	}

	responseData := &offer.GetCartResponse{
		Items: cartItems,
		Discount: offer.CartDiscount{
			IsDiscountCTAAvailable: false,
			LOCDiscoutPercent:      0,
			Total: offer.CartTotal{
				Items:            len(cartItems),
				OriginalAmount:   cartDetail.TotalPrice,
				DiscountedAmount: cartDetail.DiscountedPrice,
				FinalAmount:      cartDetail.DiscountedPrice,
			},
		},
	}

	locDiscountPercentage, locDiscountCap, err := ordersystem.GetService().GetDiscount(ctx, config.GetConfigValues().OfferLocCouponCode)
	if err != nil {
		logger.Errorf("error fetching LOC discount err: %s", err)
	}

	if s.IsEligibleForLOCDiscount(ctx, customerID) {
		responseData.Discount.IsDiscountCTAAvailable = false
		responseData.Discount.LOCDiscoutPercent = locDiscountPercentage
		responseData.Discount.LOCDiscountCap = locDiscountCap
		return responseData, nil
	}

	// if s.IsEligibleForLOCDiscountCTA(ctx, customerID) {
	// 	responseData.Discount.IsDiscountCTAAvailable = true
	// 	responseData.Discount.LOCDiscoutPercent = locDiscountPercentage
	// 	responseData.Discount.LOCDiscountCap = locDiscountCap
	// }

	return responseData, nil
}

func (s *Service) InitatePayment(ctx context.Context, customerID int64, offerDiscount string) (string, error) {
	logger := util.CLogCtx(ctx)

	err := ordersystem.GetService().PlaceOfferUserOrder(ctx, customerID)
	if err != nil {
		return "", err
	}

	orderDetail, err := ordersystem.GetService().GetLatestOfferOrder(ctx, customerID)
	if err != nil {
		return "", fmt.Errorf("failed to fetch order for customer: %d, err: %w", customerID, err)
	}

	identifierUIDs := make([]string, 0, len(orderDetail.OrderItems))
	for _, item := range orderDetail.OrderItems {
		identifierUIDs = append(identifierUIDs, item.IdentifierUID)
	}

	uidIDMap, err := s.repo.OfferProduct.GetBulkIDFromUID(ctx, identifierUIDs)
	if err != nil {
		return "", fmt.Errorf("error fetching offer product, err: %w", err)
	}

	for _, orderItemDetail := range orderDetail.OrderItems {
		orderID, ok := uidIDMap[orderItemDetail.IdentifierUID]
		if !ok {
			logger.Errorf("couldn't find offer product for uid: %s", orderItemDetail.UID)
			continue
		}

		_, err = s.repo.UserOfferPayment.Create(
			ctx,
			customerID,
			orderID,
			orderDetail.UID,
			orderItemDetail.UID,
		)
		if err != nil {
			return "", fmt.Errorf("failed to create order item for customer: %d, err: %w", customerID, err)
		}
	}

	AmazoneOffer, _ := s.repo.OfferProduct.CheckAmazonOffers(ctx, orderDetail.UID)
	Method := false
	if AmazoneOffer {
		Method = true
	}
	totalPrice := math.Ceil(float64(orderDetail.TotalPrice))
	return client.GetPaymentService().GetDealPaymentLink(customerID, float32(totalPrice), orderDetail.UID, Method, offerDiscount, ctx)
}

func (s *Service) GetUserPastOrders(ctx context.Context, customerID int64) (*offer.GetOrdersResponse, error) {
	logger := util.CLogCtx(ctx)

	err := s.repo.UserOfferPayment.MarkUnfetchedCouponFailed(ctx, customerID)
	if err != nil {
		logger.Errorf("error updating coupon failed for customer: %d, err: %s", customerID, err)
	}

	err = s.RetryFetchingCoupon(ctx, customerID)
	if err != nil {
		logger.Errorf("error updating coupon failed for customer: %d, err: %s", customerID, err)
	}

	orderDetails, err := ordersystem.GetService().FetchAllOfferOrders(ctx, customerID)
	if err != nil {
		return nil, err
	}

	orderItemUIDs := make([]string, 0)
	for _, orderDetail := range orderDetails {
		for _, item := range orderDetail.OrderItems {
			orderItemUIDs = append(orderItemUIDs, item.UID)
		}
	}

	userOfferPaymentMap, err := s.repo.UserOfferPayment.GetBulkByOrderItem(ctx, orderItemUIDs)
	if err != nil {
		return nil, fmt.Errorf("error fetching user offer payment, err: %s", err)
	}

	orderItems := make([]offer.OrderItem, 0)
	for _, orderDetail := range orderDetails {
		for _, orderItem := range orderDetail.OrderItems {
			offerPayment, ok := userOfferPaymentMap[orderItem.UID]
			if !ok {
				continue
			}

			validity := offerPayment.GetValidityString()

			var couponCode string

			couponCode, err := offerPayment.GetCouponCode()
			if err != nil {
				logger.Errorf("Error while decrpting coupon code, error : %s", err)
			}

			status := orderItem.OrderStatus.Int()
			if offerPayment.OfferStatus == model.FailedOfferStatus {
				status = 3
			}

			orderItems = append(orderItems, offer.OrderItem{
				Title:         offerPayment.OfferProduct.OfferDetail.BrandName,
				Icon:          offerPayment.OfferProduct.OfferDetail.Icon,
				TransactionID: orderItem.UID,
				Status:        status,
				Validity:      &validity,
				AmountPaid:    fmt.Sprintf("%.2f", orderItem.DiscountedPrice),
				CouponCode:    &couponCode,
				CouponPin:     &offerPayment.CouponPin,
				RedeemSteps:   offerPayment.OfferProduct.OfferDetail.OfferExtraDetails.GetRedeemSteps(),
				TNC:           offerPayment.OfferProduct.OfferDetail.OfferExtraDetails.TNC,
				ItemUID:       offerPayment.OfferProduct.OfferDetail.UID,
			})
		}
	}

	return &offer.GetOrdersResponse{
		Items: orderItems,
	}, nil
}

func (s *Service) IsEligibleForLOCDiscount(ctx context.Context, customerID int64) bool {
	return s.IsOfferDiscountEligible(ctx, customerID) && !s.IsDiscountTaken(ctx, customerID)
}

func (s *Service) IsEligibleForLOCDiscountCTA(ctx context.Context, customerID int64) bool {
	logger := util.CLogCtx(ctx)

	isCustomerJourneyIncomplete, err := loan.GetService().IsCustomerJourneyIncomplete(ctx, customerID)
	if err != nil {
		logger.Errorf("error checking if customer is journey incomplete, err: %s", err)
		return false
	}

	return isCustomerJourneyIncomplete && !s.IsDiscountTaken(ctx, customerID)
}

func (s *Service) IsOfferDiscountEligible(ctx context.Context, customerID int64) bool {
	logger := util.CLogCtx(ctx)
	isOfferDiscountEligible, err := customer.GetCustomerService().IsCustomerOfferDiscountEligible(ctx, customerID)
	if err != nil {
		logger.Errorf("error checking if customer is offer discount clicked, err: %s", err)
		return false
	}

	return isOfferDiscountEligible
}

func (s *Service) IsOfferUTMCustomer(ctx context.Context, customerID int64) bool {
	logger := util.CLogCtx(ctx)
	isOfferCustomer, err := customer.GetCustomerService().IsOfferCustomer(ctx, customerID)
	if err != nil {
		logger.Errorf("error checking if customer is offer customer, err: %s", err)
		return false
	}

	return isOfferCustomer
}

func (s *Service) IsLocOpen(ctx context.Context, customerID int64) bool {
	logger := util.CLogCtx(ctx)
	isLOCOpen, err := loan.GetService().IsLocOpen(ctx, customerID)
	if err != nil {
		logger.Errorf("error checking if LOC is open, err: %s", err)
		return false
	}

	return isLOCOpen
}

func (s *Service) IsDiscountTaken(ctx context.Context, customerId int64) bool {
	logger := util.CLogCtx(ctx)

	isDiscountTaken, err := ordersystem.GetService().IsDiscountAlreadyTaken(ctx, config.GetConfigValues().OfferLocCouponCode, customerId)
	if err != nil {
		logger.Errorf("error checking if discount is already taken, err: %s", err)
		return true
	}
	return isDiscountTaken
}

func (s *Service) IsRejectedLoanCustomer(ctx context.Context, customerID int64) bool {
	logger := util.CLogCtx(ctx)
	isRejectedLoan, err := loan.GetService().IsRejectedCustomer(ctx, customerID)
	if err != nil {
		logger.Errorf("error checking if customer is rejected, err: %s", err)
		return isRejectedLoan
	}

	return isRejectedLoan
}

func (s *Service) RetryFetchingCoupon(ctx context.Context, customerID int64) error {
	logger := util.CLogCtx(ctx)

	userOfferPaymentObjs, err := s.repo.UserOfferPayment.GetRetryCouponObj(ctx, customerID)
	if err != nil {
		logger.Errorf("error fetching coupon obj, err: %s", err)
		return err
	}

	for _, obj := range userOfferPaymentObjs {
		_, err := s.FetchCouponCodes(ctx, obj.OrderID)
		if err != nil {
			logger.Errorf("error while fetching coupon code for user_offer_payment id: %d, err: %s", obj.ID, err)
		}
	}

	return nil
}

func (s *Service) InitatePaymentupi(ctx context.Context, customerID int64, offerDiscount string) (string, error) {
	logger := util.CLogCtx(ctx)

	err := ordersystem.GetService().PlaceOfferUserOrder(ctx, customerID)
	if err != nil {
		return "", err
	}

	orderDetail, err := ordersystem.GetService().GetLatestOfferOrder(ctx, customerID)
	logger.Info(orderDetail, "order details")
	if err != nil {
		return "", fmt.Errorf("failed to fetch order for customer: %d, err: %w", customerID, err)
	}

	identifierUIDs := make([]string, 0, len(orderDetail.OrderItems))
	for _, item := range orderDetail.OrderItems {
		identifierUIDs = append(identifierUIDs, item.IdentifierUID)
	}

	uidIDMap, err := s.repo.OfferProduct.GetBulkIDFromUID(ctx, identifierUIDs)
	if err != nil {
		return "", fmt.Errorf("error fetching offer product, err: %w", err)
	}

	for _, orderItemDetail := range orderDetail.OrderItems {
		orderID, ok := uidIDMap[orderItemDetail.IdentifierUID]
		if !ok {
			logger.Errorf("couldn't find offer product for uid: %s", orderItemDetail.UID)
			continue
		}

		_, err = s.repo.UserOfferPayment.Create(
			ctx,
			customerID,
			orderID,
			orderDetail.UID,
			orderItemDetail.UID,
		)
		if err != nil {
			return "", fmt.Errorf("failed to create order item for customer: %d, err: %w", customerID, err)
		}
	}

	return orderDetail.UID, err
}
