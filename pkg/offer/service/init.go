package service

import (
	"stashfin.com/stashfin/magneto/pkg/offer/repo"
	"stashfin.com/stashfin/magneto/pkg/offer/service/vendor"
)

type Repo struct {
	OfferProduct     *repo.OfferProductRepo
	OfferDetail      *repo.OfferDetailRepo
	OfferExtraDetail *repo.OfferExtraDetailRepo
	UserOfferPayment *repo.UserOfferPayment
}

func NewRepo(
	offerProductRepo *repo.OfferProductRepo,
	offerDetailRepo *repo.OfferDetailRepo,
	offerExtraDetailRepo *repo.OfferExtraDetailRepo,
	userOfferPaymentRepo *repo.UserOfferPayment,
) *Repo {
	return &Repo{
		OfferProduct:     offerProductRepo,
		OfferDetail:      offerDetailRepo,
		OfferExtraDetail: offerExtraDetailRepo,
		UserOfferPayment: userOfferPaymentRepo,
	}
}

type Vendors struct {
	Internal *vendor.InternalCouponFetcher
}

type Service struct {
	repo   *Repo
	vendor *Vendors
}

func NewService(repo *Repo, vendor *Vendors) *Service {
	return &Service{
		repo:   repo,
		vendor: vendor,
	}
}
