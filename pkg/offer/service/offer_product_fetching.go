package service

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"slices"
	"strconv"
	"strings"

	"gorm.io/datatypes"

	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/client/vouchergram"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
	"stashfin.com/stashfin/magneto/pkg/ordersystem"
	"stashfin.com/stashfin/magneto/util"
)

func (s *Service) FetchProducts(ctx context.Context) error {
	err := s.FetchVoucherGramProducts(ctx)
	if err != nil {
		return err
	}

	return nil
}

func (s *Service) FetchVoucherGramProducts(ctx context.Context) error {
	logger := util.CLogCtx(ctx)

	var err error
	result, err := client.GetVoucherGramClient().GetBrands(ctx)
	if err != nil {
		return err
	}

	for _, brand := range result {
		product, err := s.repo.OfferDetail.GetByProductCode(ctx, brand.BrandProductCode)
		if util.IsServiceError(err) {
			logger.Errorf("error fetching offer detail for product: %s, err: %s", brand.BrandProductCode, err)
			continue
		}

		if err != nil {
			err = s.createOffersEntry(ctx, brand)
			if err != nil {
				logger.Errorf("error creating offer entry for product: %s, err: %s", brand.BrandProductCode, err)
			}
			continue
		}

		err = s.updateOffersEntry(ctx, brand, product)
		if err != nil {
			logger.Errorf("error updating offer entry for product: %s, err: %s", brand.BrandProductCode, err)
		}

	}
	return nil
}

// GetRedeemStepsText takes a JSON string containing redeem steps information
// and returns a slice of strings containing only the "text" values from the JSON data.
func GetRedeemStepsText(redeemStepsJSON string) []string {
	if redeemStepsJSON == "" {
		return []string{} // Return empty slice if JSON string is empty
	}

	var redeemStepsMap map[string]map[string]string
	err := json.Unmarshal([]byte(redeemStepsJSON), &redeemStepsMap)
	if err != nil {
		fmt.Println("Error:", err)
		return []string{} // Return empty slice in case of error
	}

	var redeemStepsText []string
	for _, step := range redeemStepsMap {
		redeemStepsText = append(redeemStepsText, step["text"])
	}

	return redeemStepsText
}

func (s *Service) createOffersEntry(ctx context.Context, brand vouchergram.GiftVoucher) error {
	logger := util.CLogCtx(ctx)

	redeemStepsJSON, err := json.Marshal(GetRedeemStepsText(brand.RedeemSteps))
	if err != nil {
		logger.Infof("Error marshaling rewardCodes: %s", err)
		redeemStepsJSON = []byte("[]")
	}

	offerExtraDetail, err := s.repo.OfferExtraDetail.Create(ctx, &model.OfferExtraDetail{
		TNC:                  brand.Tnc,
		ImportantInstruction: brand.ImportantInstruction,
		RedeemSteps:          datatypes.JSON(redeemStepsJSON),
		DenominationList:     brand.DenominationList,
	})
	if err != nil {
		return err
	}

	offerDetail, err := s.repo.OfferDetail.Create(ctx, &model.OfferDetail{
		BrandName:           brand.BrandName,
		BrandImage:          brand.BrandImage,
		Icon:                brand.BrandImage,
		IsActive:            false,
		BrandProductCode:    brand.BrandProductCode,
		Type:                brand.BrandType,
		OfferExtraDetailsID: offerExtraDetail.ID,
		Thumbnail:           brand.BrandImage,
		Category:            brand.Category,
		IsStockAvailable:    brand.StockAvailable.Bool(),
		GradientID:          1,
	})
	if err != nil {
		return err
	}

	// Split the input string based on comma delimiter
	values := strings.Split(brand.DenominationList, ",")

	for _, valStr := range values {
		// Convert the string value to integer
		valInt, err := strconv.Atoi(valStr)
		if err != nil {
			logger.Error("Error converting to integer:", err)
			continue
		}
		valFloat := float32(valInt)
		offerProduct, err := s.repo.OfferProduct.Create(ctx, &model.OfferProduct{
			OfferDetailID: offerDetail.ID,
			Price:         valFloat,
			IsActive:      false,
			OfferVendorID: 1,
		})
		if err != nil {
			logger.Error("Error creating offer product:", err)
			continue
		}

		// creating entry in SKU table
		_, err = ordersystem.GetService().CreateOfferSKU(ctx, offerProduct.UID, valFloat)
		if err != nil {
			logger.Errorf("error creating sku for product UID: %s, err: %s", offerProduct.UID, err)
			continue
		}
	}

	logger.Infof("CRON: Created offer entry for brand: %s", brand.BrandName)
	return nil
}

func (s *Service) updateOffersEntry(ctx context.Context, brand vouchergram.GiftVoucher, offerDetial *model.OfferDetail) error {
	logger := util.CLogCtx(ctx)

	offerDetial.BrandName = brand.BrandName
	offerDetial.Type = brand.BrandType
	offerDetial.Category = brand.Category
	offerDetial.IsStockAvailable = brand.StockAvailable.Bool()
	//offerDetial.BrandImage = brand.BrandImage

	if !brand.StockAvailable.Bool() {
		offerDetial.IsActive = false
	}

	err := s.repo.OfferDetail.Update(ctx, offerDetial, []string{"brand_name", "type", "category", "is_stock_available", "is_active"})
	if err != nil {
		logger.Errorf("error updating offer detail for product UID: %s, err: %s", offerDetial.UID, err)
		return err
	}

	redeemStepsJSON, err := json.Marshal(GetRedeemStepsText(brand.RedeemSteps))
	if err != nil {
		logger.Infof("Error marshaling rewardCodes: %s", err)
		redeemStepsJSON = []byte("[]")
	}

	offerExtraDetail := offerDetial.OfferExtraDetails
	offerExtraDetail.DenominationList = brand.DenominationList
	offerExtraDetail.RedeemSteps = datatypes.JSON(redeemStepsJSON)
	//offerExtraDetail.TNC = brand.Tnc
	offerExtraDetail.ImportantInstruction = brand.ImportantInstruction

	err = s.repo.OfferExtraDetail.Update(ctx, offerExtraDetail, []string{"important_instruction", "redeem_steps", "denomination_list"})
	if err != nil {
		logger.Errorf("error updating offer extra detail for product UID: %s, err: %s", offerDetial.UID, err)
		return err
	}

	priceProductExist, err := s.repo.OfferProduct.FilterAllByOfferDetailID(ctx, offerDetial.ID)
	if err != nil {
		logger.Errorf("error fetching all offer product for offer detail ID: %d, err: %s", offerDetial.ID, err)
		return err
	}

	// Split the input string based on comma delimiter
	values := strings.Split(brand.DenominationList, ",")

	for _, valStr := range values {
		valInt, err := strconv.Atoi(valStr)
		if err != nil {
			logger.Error("Error converting to integer:", err)
			continue
		}

		valFloat := float32(valInt)
		exist := slices.Contains(priceProductExist, valFloat)
		if !exist {
			offerProduct, err := s.repo.OfferProduct.Create(ctx, &model.OfferProduct{
				OfferDetailID: offerDetial.ID,
				Price:         valFloat,
				IsActive:      false,
				OfferVendorID: 1,
			})
			if err != nil {
				logger.Errorf("error creating offer product for product UID: %s", err)
				continue
			}

			// creating entry in SKU table
			_, err = ordersystem.GetService().CreateOfferSKU(ctx, offerProduct.UID, valFloat)
			if err != nil {
				logger.Errorf("error creating sku for product UID: %s, err: %s", offerProduct.UID, err)
				continue
			}
		}
	}

	logger.Infof("CRON: Updated offer entry for brand: %s", brand.BrandName)
	return nil
}

func (s *Service) LocDiscount(ctx context.Context, userID int64) error {
	if !s.IsEligibleForLOCDiscountCTA(ctx, userID) {
		return helper.NewCustomError("LOC discount not eligible", http.StatusBadRequest)
	}
	return customer.GetCustomerService().UpdateCustomerOfferDiscountClicked(ctx, userID, true)
}
