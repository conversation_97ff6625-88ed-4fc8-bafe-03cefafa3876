package service

import (
	"context"
	"errors"
	"fmt"

	offerDtype "stashfin.com/stashfin/magneto/api/offer"
	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
	"stashfin.com/stashfin/magneto/pkg/offer/service/vendor"
	"stashfin.com/stashfin/magneto/util"
)

const (
	GiftcardConfirmationTemplate = "giftcard_confirmation"
	DealsThankYouTemplate        = "dealsthankyou"
)

type IFetchCouponCode interface {
	GetCouponCode(ctx context.Context, userOfferPayment *model.UserOfferPayment) (*offerDtype.GetCouponResponse, error)
}

func (s *Service) postPaymentSuccess(ctx context.Context, orderID string) error {
	logger := util.CLogCtx(ctx)

	err := s.repo.UserOfferPayment.UpdatePaymentStatus(ctx, orderID, model.SuccessPaymentStatus)
	if err != nil {
		logger.Errorf("error updating offer payment status to success for order id: %s, err: %s", orderID, err)
	}
	_, err = s.FetchCouponCodes(ctx, orderID)
	return err
}

func (s *Service) postPaymentFailed(ctx context.Context, orderID string) error {
	logger := util.CLogCtx(ctx)

	err := s.repo.UserOfferPayment.UpdatePaymentStatus(ctx, orderID, model.FailedPaymentStatus)
	if err != nil {
		logger.Errorf("error updating offer payment status to failed for order id: %s, err: %s", orderID, err)
	}

	return err
}

func (s *Service) PostPaymentHandler(ctx context.Context, orderID string, status int) error {
	logger := util.CLogCtx(ctx)

	statunEnum := model.PaymentStatusFromInt(status)

	switch statunEnum {
	case model.SuccessPaymentStatus:
		return s.postPaymentSuccess(ctx, orderID)
	case model.FailedPaymentStatus:
		return s.postPaymentFailed(ctx, orderID)
	default:
		logger.Errorf("invalid payment status: %d", status)
		return fmt.Errorf("invalid payment status: %d", status)
	}
}

func (s *Service) GetVendorStruct(ctx context.Context, vendorType string, isInternal bool) (IFetchCouponCode, error) {
	if isInternal {
		return &vendor.InternalCouponFetcher{}, nil
	}

	switch vendorType {
	case "Vouchergram":
		return &vendor.VouchergramFetcher{}, nil
	case "Qwikgift":
		return &vendor.QwikgiftFetcher{}, nil
	default:
		return nil, errors.New("invalid vendor_type")
	}
}

func (s *Service) FetchCouponCodes(ctx context.Context, orderID string) (string, error) {
	logger := util.CLogCtx(ctx)

	logger.Info("fetching coupon code for order uid: " + orderID)

	// err := s.repo.UserOfferPayment.UpdateOfferStatus(ctx, orderID, model.InitiatedOfferStatus)
	// if err != nil {
	// 	logger.Errorf("error updating offer status to initiated for order uid: %s, err: %s", orderID, err)
	// 	return err
	// }
	msg := ""
	errStr := ""

	userPaymentObj, err := s.repo.UserOfferPayment.GetByOrderID(ctx, orderID)
	if err != nil {
		msg += ", no user payment obj found"
		return msg, err
	}

	if len(userPaymentObj) == 0 {
		errStr = fmt.Sprintf("no user_offer_payment found for order uid: %s", orderID)
		msg += ", " + errStr
		return msg, errors.New(errStr)
	}

	successCount := 0
	var customerID int64
	var SuccessOfferAmount float32 = 0.0

	for _, item := range userPaymentObj {
		customerID = item.UserID
		if item.PaymentStatus != model.SuccessPaymentStatus {
			errStr = fmt.Sprintf("payment not success for order uid: %s, and offer payment id %d", orderID, item.ID)
			msg += ", " + errStr
			return msg, errors.New(errStr)
		}

		if item.OfferStatus == model.SuccessOfferStatus {
			successCount++
			errStr = fmt.Sprintf("offer status alreay success for order uid: %s, and offer payment id %d and offer id: %d", orderID, item.ID, item.ID)
			msg += ", " + errStr
			logger.Errorf(errStr)
			continue
		}
		err := s.repo.UserOfferPayment.UpdateOfferStatusById(ctx, item.ID, model.InitiatedOfferStatus)
		if err != nil {
			errStr = fmt.Sprintf("error updating offer status to initiated for order uid: %s, and offer payment id %d err: %s", orderID, item.ID, err)
			msg += ", " + errStr
			logger.Errorf(errStr)
			continue
		}

		vendorHandler, err := s.GetVendorStruct(ctx, item.OfferProduct.OfferVendor.Name, item.OfferProduct.OfferVendor.IsInternal)
		if err != nil {
			errStr = fmt.Sprintf("error fetching vendor Handler for user_offer_payment id: %d, err: %s", item.ID, err)
			msg += ", " + errStr
			logger.Errorf(errStr)
			continue
		}

		couponDetailObj, err := vendorHandler.GetCouponCode(ctx, &item)
		if err != nil {
			errStr = fmt.Sprintf("error fetching coupon code for user_offer_payment id: %d, err: %s", item.ID, err)
			msg += ", " + errStr
			logger.Errorf(errStr)
			err = s.repo.UserOfferPayment.UpdateItemCouponStatus(ctx, item.ID, model.FailedOfferStatus)
			if err != nil {
				errStr = fmt.Sprintf("error updating coupon status for user_offer_payment id: %d, err: %s", item.ID, err)
				msg += ", " + errStr
				logger.Errorf(errStr)
			}
			continue
		}

		err = s.repo.UserOfferPayment.UpdateCouponDetails(ctx, item.ID, couponDetailObj.CouponCode, couponDetailObj.CouponPin, couponDetailObj.CouponValidity)
		if err != nil {
			errStr = fmt.Sprintf("error updating coupon code for user_offer_payment id: %d, err: %s", item.ID, err)
			msg += ", " + errStr
			logger.Errorf(errStr)
			continue
		}

		err = s.repo.UserOfferPayment.UpdateItemCouponStatus(ctx, item.ID, model.SuccessOfferStatus)
		if err != nil {
			errStr = fmt.Sprintf("error updating coupon status for user_offer_payment id: %d, err: %s", item.ID, err)
			msg += ", " + errStr
			logger.Errorf(errStr)
			continue
		}

		SuccessOfferAmount += item.OfferProduct.Price

		successCount++
	}

	if len(userPaymentObj) > 0 && successCount == len(userPaymentObj) {
		data := map[string]any{
			"orderID":    orderID,
			"customerID": customerID,
			"brandName":  userPaymentObj[0].OfferProduct.OfferDetail.BrandName,
		}
		err = config.GetSQSClient().SendMessageStruct(ctx, data, "BUY_GIFT_CARD")
		if err != nil {
			util.Log.Errorln("failed send sqs event for offer success, err: ", err)
			msg += ", " + "sending sqs event failed. " + err.Error()
		} else {
			msg += ", " + "sqs event sent"
		}

	}

	if SuccessOfferAmount > 0 {
		go func() {
			ctx := context.Background()
			customerDetails, err := customer.GetCustomerService().GetCustomerData(ctx, customerID)
			customerName := ""
			customerEmail := ""
			if err != nil {
				util.Log.Errorln("failed to get customer details for offer success notification, err: ", err)
			} else {
				customerName = customerDetails.GetFullName()
				customerEmail = customerDetails.Email
			}
			templateDataEmail := map[string]any{}
			templateDataEmail["customer_name"] = customerName
			templateDataEmail["amount"] = SuccessOfferAmount
			templateDataEmail["customer_email"] = customerEmail

			templateDataSMS := map[string]any{}
			templateDataSMS["amount"] = SuccessOfferAmount

			var destination []string
			var attachments map[string][]byte
			err = client.SendEmail(ctx, customerID, GiftcardConfirmationTemplate, templateDataEmail, destination, attachments)
			if err != nil {
				util.Log.Errorln("failed to push offer email  to redis, err: ", err)
			}
			err = client.SendSMS(ctx, customerID, DealsThankYouTemplate, templateDataSMS)
			if err != nil {
				util.Log.Errorln("failed to push offer sms to redis, err: ", err)
			}
		}()
	}

	logger.Info("completed fetching coupon code for order uid: " + orderID)
	msg += ", " + "coupon code fetched"
	return msg, nil
}
