package service

import (
	"context"
	"fmt"
	"net/http"

	"stashfin.com/stashfin/magneto/api/offer"
	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/insurance"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
	"stashfin.com/stashfin/magneto/pkg/ordersystem"
	"stashfin.com/stashfin/magneto/util"

	"stashfin.com/stashfin/magneto/pkg/customer"
)

func (s *Service) GetOfferListingPage(ctx context.Context, userID int64) (*offer.OfferLisitingResponse, error) {
	offerProductsDetail, err := s.repo.OfferDetail.GetAllActive(ctx)
	if err != nil {
		return nil, err
	}

	vouchers := make([]offer.Voucher, 0)

	for _, offerProduct := range offerProductsDetail {
		voucher := offer.Voucher{
			UID:                offerProduct.UID,
			Thumbnail:          offerProduct.Thumbnail,
			Icon:               offerProduct.BrandImage,
			Name:               offerProduct.BrandName,
			DiscountPercentage: offerProduct.DiscountPercentage,
			BgGradient: offer.GradientColor{
				Colors:    []string{offerProduct.Gradient.StartColor, offerProduct.Gradient.EndColor},
				Positions: []float64{offerProduct.Gradient.StartPosition, offerProduct.Gradient.EndPosition},
				Angle:     offerProduct.Gradient.Angle,
			},
		}
		vouchers = append(vouchers, voucher)
	}

	return &offer.OfferLisitingResponse{
		TopBanner:    s.GetofferHomeTopBanner(ctx, userID),
		BottomBanner: s.GetofferHomeBottomBanner(ctx, userID),
		FooterImage:  s.GetofferHomeFooterImage(ctx),
		Vouchers:     vouchers,
	}, nil
}

func (s *Service) GetofferHomeTopBanner(ctx context.Context, userID int64) []offer.BannerItem {
	topBanner := make([]offer.BannerItem, 0)
	err := insurance.GetService().GetCMSData(ctx, OfferCMSIdentifierType, BannerIdentifierUID, OfferListingTopBannerKey, &topBanner)
	if err != nil {
		util.Log.Infof("Error while fetching top banner for offer home page, error : %s", err)
		return topBanner
	}

	topBannerResponse := make([]offer.BannerItem, 0)
	for _, item := range topBanner {
		if item.IsDisableForLOC && !s.IsEligibleForLOCDiscountCTA(ctx, userID) {
			continue
		}

		topBannerResponse = append(topBannerResponse, offer.BannerItem{
			Name:            item.Name,
			ImageURL:        item.ImageURL,
			RedirectURL:     item.RedirectURL,
			IsDisableForLOC: item.IsDisableForLOC,
		})
	}

	return topBannerResponse
}

func (s *Service) GetofferHomeBottomBanner(ctx context.Context, userID int64) *offer.BannerItem {
	var bottomBanner offer.BannerItem
	err := insurance.GetService().GetCMSData(ctx, OfferCMSIdentifierType, BannerIdentifierUID, OfferListingBottomBannerKey, &bottomBanner)
	if err != nil {
		util.Log.Infof("Error while fetching bottom banner for offer home page, error : %s", err)
		return nil
	}

	if bottomBanner.IsDisableForLOC && !s.IsEligibleForLOCDiscountCTA(ctx, userID) {
		return nil
	}

	return &bottomBanner
}

func (s *Service) GetofferHomeFooterImage(ctx context.Context) string {
	var footerImage string
	err := insurance.GetService().GetCMSData(ctx, OfferCMSIdentifierType, FooterImageIdentifierUID, OfferListingFooterImageKey, &footerImage)
	if err != nil {
		util.Log.Infof("Error while fetching footer image for offer home page, error : %s", err)
		return ""
	}
	return footerImage
}

func (s *Service) GetOfferDetail(ctx context.Context, offerDetailUID string) (*offer.OfferDetailResponse, error) {
	offerDetail, err := s.repo.OfferDetail.GetByUID(ctx, offerDetailUID)
	if err != nil {
		util.Log.Infof("Error while fetching offer detail, error : %s", err)
		return nil, err
	}

	offerProducts, err := s.repo.OfferProduct.FilterActiveByOfferDetail(ctx, offerDetail.ID)
	if err != nil {
		util.Log.Infof("Error while fetching offer product, error : %s", err)
		return nil, err
	}

	var offerSKUs []offer.SKU
	for _, offerProductSKU := range offerProducts {
		offerSKUs = append(offerSKUs, offer.SKU{
			Amount:   offerProductSKU.Price,
			Validity: offerProductSKU.GetValidityString(),
			UID:      offerProductSKU.UID,
		})
	}

	return &offer.OfferDetailResponse{
		Title:              offerDetail.BrandName,
		Icon:               offerDetail.BrandImage,
		OfferText:          offerDetail.OfferExtraDetails.OfferText,
		SKU:                offerSKUs,
		KeyPoints:          offerDetail.OfferExtraDetails.GetKeyPoints(),
		RedeemSteps:        offerDetail.OfferExtraDetails.GetRedeemSteps(),
		TermsAndConditions: offerDetail.OfferExtraDetails.TNC,
		PromotionText:      offerDetail.OfferExtraDetails.PromotionText,
	}, nil
}

func (s *Service) UpdateCart(ctx context.Context, userID int64, req offer.SKUPayload) error {
	for _, item := range req.SKU {
		err := s.UpdateCartHelper(ctx, item.SKUUID, item.Quantity, userID)
		if err != nil {
			return err
		}
	}
	return nil
}

func (s *Service) GetUserOfferCart(ctx context.Context, userID int64) (*offer.GetCartResponse, error) {
	response, err := s.GetCartHelper(ctx, userID)
	if err != nil {
		return nil, err
	}
	return response, nil
}
func (s *Service) checkOfferLimits(ctx context.Context, customerID int64) error {
	cartDetails, err := ordersystem.GetService().GetOfferCart(ctx, customerID)
	if err != nil {
		return err
	}
	if cartDetails.TotalPrice > dtype.OfferMaxTransactionAmount {
		return helper.NewCustomError("You can only buy maximum "+fmt.Sprint(dtype.OfferMaxTransactionAmount), http.StatusBadRequest)
	}

	ordershortDetails, err := ordersystem.GetService().GetCurrentMonthOffierOrders(ctx, customerID)
	if err != nil {
		return err
	}
	var lastMonthTotalPrice float32 = 0.00
	var lastMonthTotalTxns int = 0
	for _, orderObj := range ordershortDetails {
		lastMonthTotalPrice = lastMonthTotalPrice + orderObj.TotalPrice
		lastMonthTotalTxns = lastMonthTotalTxns + 1
	}
	if lastMonthTotalPrice > dtype.OfferMaxTransactionAmountPerMonth {
		return helper.NewCustomError("maximum orders is "+fmt.Sprint(dtype.OfferMaxTransactionAmountPerMonth)+" rupees per month", http.StatusBadRequest)
	}
	if lastMonthTotalTxns > dtype.OfferMaxTransactionsPerMonth {
		return helper.NewCustomError("Max number of allowed orders per month is "+fmt.Sprint(dtype.OfferMaxTransactionsPerMonth), http.StatusBadRequest)
	}
	return nil
}

func (s *Service) GetPaymentLink(ctx context.Context, customerID int64, offerDiscount string) (*offer.GetPaymentLinkResponse, error) {
	action := ""
	redirectUrl := ""

	err := s.checkOfferLimits(ctx, customerID)
	if err != nil {
		return nil, err
	}

	okycDone, err := customer.GetCustomerService().GetOkycCompletedStatus(ctx, customerID)
	if err != nil {
		return nil, err
	}
	if okycDone {
		redirectUrl, err = s.InitatePayment(ctx, customerID, offerDiscount)
		if err != nil {
			return nil, err
		}
		action = "payment"

		return &offer.GetPaymentLinkResponse{
			RedirectUrl: redirectUrl,
			Action:      action,
		}, nil
	}

	offerPayments, err := s.repo.UserOfferPayment.GetActiveByUserID(ctx, customerID)
	if err != nil && !util.IsNotFoundError(err) {
		return nil, err
	}
	totalPrice := float32(0.0)
	for _, item := range offerPayments {
		if item.OfferStatus != model.SuccessOfferStatus {
			continue
		}
		totalPrice += item.OfferProduct.Price * float32(item.OfferProduct.StockQuantity)
	}

	if totalPrice > dtype.OfferMaxAmountWithoutOkyc {
		action = "okyc"
	} else {
		// orderDetail, err := ordersystem.GetService().GetLatestOfferOrder(ctx, customerID)
		// if err != nil {
		// 	return nil, fmt.Errorf("failed to fetch order for customer: %d, err: %w", customerID, err)
		// }
		// totalPrice += orderDetail.TotalPrice
		cartDetails, err := ordersystem.GetService().GetOfferCart(ctx, customerID)
		if err != nil {
			return nil, err
		}
		totalPrice += cartDetails.TotalPrice

		if totalPrice > dtype.OfferMaxAmountWithoutOkyc {
			action = "okyc"
		}
	}
	if action == "okyc" {
		//get okyc link from v2
		okycData, err := client.GetApiV2Client().GetOKycUrl(ctx, customerID)
		if err != nil {
			return nil, err
		}
		redirectUrl = okycData.RedirectUrl
		action = "okyc"

	} else {
		action = "payment"
		redirectUrl, err = s.InitatePayment(ctx, customerID, offerDiscount)
		if err != nil {
			return nil, err
		}
	}

	return &offer.GetPaymentLinkResponse{
		RedirectUrl: redirectUrl,
		Action:      action,
	}, nil
}

func (s *Service) GetOrdersHistory(ctx context.Context, userID int64) (*offer.GetOrdersResponse, error) {
	return s.GetUserPastOrders(ctx, userID)
}

func (s *Service) GetOrderStatus(ctx context.Context, userID int64, orderUID string) (*offer.OrderStatusResponse, error) {
	logger := util.CLogCtx(ctx)

	orderItems, err := s.repo.UserOfferPayment.GetByOrderIDUserID(ctx, orderUID, userID)
	if util.IsServiceError(err) {
		return nil, err
	}

	if util.IsNotFoundError(err) || len(orderItems) == 0 {
		return nil, helper.BadRequestError("invalid order id")
	}

	orderStatusItems := make([]offer.OrderStatusData, 0)
	for _, item := range orderItems {
		validity := item.GetValidityString()

		couponCode, err := item.GetCouponCode()
		if err != nil {
			logger.Errorf("Error while decrpting coupon code, error : %s", err)
		}

		orderStatusItems = append(orderStatusItems, offer.OrderStatusData{
			Title:         item.OfferProduct.OfferDetail.BrandName,
			Icon:          item.OfferProduct.OfferDetail.Icon,
			Validity:      validity,
			Type:          item.OfferProduct.OfferDetail.BrandType,
			AmountPaid:    fmt.Sprintf("%.2f", item.OfferProduct.Price),
			Name:          item.OfferProduct.OfferDetail.BrandName,
			TransactionID: item.OrderItemID,
			CouponCode:    couponCode,
			CouponPin:     item.CouponPin,
		})
	}

	return &offer.OrderStatusResponse{
		Status: orderItems[0].PaymentStatus,
		Data:   orderStatusItems,
	}, nil
}

func (s *Service) Getcheckokyc(ctx context.Context, customerID int64) (*offer.GetPaymentLinkResponse, error) {
	action := ""
	redirectUrl := ""

	err := s.checkOfferLimits(ctx, customerID)
	if err != nil {
		return nil, err
	}

	okycDone, err := customer.GetCustomerService().GetOkycCompletedStatus(ctx, customerID)
	if err != nil {
		return nil, err
	}
	if okycDone {

		action = "payment"

		return &offer.GetPaymentLinkResponse{
			RedirectUrl: redirectUrl,
			Action:      action,
		}, nil
	}

	offerPayments, err := s.repo.UserOfferPayment.GetActiveByUserID(ctx, customerID)
	if err != nil && !util.IsNotFoundError(err) {
		return nil, err
	}
	totalPrice := float32(0.0)
	for _, item := range offerPayments {
		if item.OfferStatus != model.SuccessOfferStatus {
			continue
		}
		totalPrice += item.OfferProduct.Price * float32(item.OfferProduct.StockQuantity)
	}

	if totalPrice > dtype.OfferMaxAmountWithoutOkyc {
		action = "okyc"
	} else {
		// orderDetail, err := ordersystem.GetService().GetLatestOfferOrder(ctx, customerID)
		// if err != nil {
		// 	return nil, fmt.Errorf("failed to fetch order for customer: %d, err: %w", customerID, err)
		// }
		// totalPrice += orderDetail.TotalPrice
		cartDetails, err := ordersystem.GetService().GetOfferCart(ctx, customerID)
		if err != nil {
			return nil, err
		}
		totalPrice += cartDetails.TotalPrice

		if totalPrice > dtype.OfferMaxAmountWithoutOkyc {
			action = "okyc"
		}
	}
	if action == "okyc" {
		//get okyc link from v2
		okycData, err := client.GetApiV2Client().GetOKycUrl(ctx, customerID)
		if err != nil {
			return nil, err
		}
		redirectUrl = okycData.RedirectUrl
		action = "okyc"

	} else {
		action = "payment"

	}

	return &offer.GetPaymentLinkResponse{
		RedirectUrl: redirectUrl,
		Action:      action,
	}, nil
}

func (s *Service) Getpaymentuid(ctx context.Context, customerID int64, offerDiscount string) (*offer.GetPaymentOrderResponse, error) {
	action := ""
	redirectUrl := ""

	action = "payment"
	redirectUrl, err := s.InitatePaymentupi(ctx, customerID, offerDiscount)
	if err != nil {
		return nil, err
	}

	return &offer.GetPaymentOrderResponse{
		Uid:    redirectUrl,
		Action: action,
	}, nil
}
