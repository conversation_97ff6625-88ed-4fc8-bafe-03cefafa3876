package vendor

import (
	"context"

	"stashfin.com/stashfin/magneto/api/offer"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
	"stashfin.com/stashfin/magneto/pkg/offer/repo"
	"stashfin.com/stashfin/magneto/util"
)

type InternalCouponFetcher struct {
	OfferInternalRepo *repo.OfferInternalCouponRepo
}

func (v *InternalCouponFetcher) GetCouponCode(ctx context.Context, userOfferPayment *model.UserOfferPayment) (*offer.GetCouponResponse, error) {
	logger := util.CLogCtx(ctx)
	obj, err := v.OfferInternalRepo.GetByVendorType(ctx, userOfferPayment.OfferProduct.OfferVendor.ID)
	if util.IsNotFoundError(err) {
		logger.Errorf("error fetching coupon code for user_offer_payment id: %d, err: %s", userOfferPayment.ID, err)
		return nil, err
	}

	obj.IsAssigned = true
	err = v.OfferInternalRepo.Update(ctx, obj, []string{"IsAssigned"})
	if err != nil {
		logger.Errorf("error while making internal coupon code assigned for user_offer_payment id: %d, internal_coupon_id: %d, err: %s", userOfferPayment.ID, obj.ID, err)
		return nil, err
	}

	return &offer.GetCouponResponse{
		CouponCode:     obj.CouponCode,
		CouponPin:      obj.CouponPin,
		CouponValidity: obj.CouponValidity,
	}, nil
}
