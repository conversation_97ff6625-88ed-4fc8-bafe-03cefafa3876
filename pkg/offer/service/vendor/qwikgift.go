package vendor

import (
	"context"

	"stashfin.com/stashfin/magneto/api/offer"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
)

type QwikgiftFetcher struct{}

func (q *QwikgiftFetcher) GetCouponCode(ctx context.Context, userOfferPayment *model.UserOfferPayment) (*offer.GetCouponResponse, error) {
	// Implementation for Qwikgift
	return &offer.GetCouponResponse{
		CouponCode: "QWIKGIFT",
	}, nil
}
