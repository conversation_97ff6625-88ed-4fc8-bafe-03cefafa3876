package vendor

import (
	"context"
	"fmt"

	"stashfin.com/stashfin/magneto/api/offer"
	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/pkg/offer/model"
	"stashfin.com/stashfin/magneto/util"
)

type VouchergramFetcher struct{}

func (v *VouchergramFetcher) GetCouponCode(ctx context.Context, userOfferPayment *model.UserOfferPayment) (*offer.GetCouponResponse, error) {
	logger := util.CLogCtx(ctx)
	res, err := client.GetVoucherGramClient().PullVoucher(ctx, userOfferPayment.OfferProduct.OfferDetail.BrandProductCode, userOfferPayment.OrderItemID, 1, int(userOfferPayment.OfferProduct.Price))
	if err != nil {
		return nil, err
	}

	if res.ResultType != "SUCCESS" {
		return nil, fmt.Errorf("error fetching coupon code for vouchergram, user_offer_payment id: %d:  err: %+v", userOfferPayment.ID, res)
	}

	if len(res.PullVouchers) == 0 && len(res.PullVouchers[0].Vouchers) == 0 {
		return nil, fmt.Errorf("error fetching coupon code for vouchergram, user_offer_payment id: %d:  err: %+v", userOfferPayment.ID, res)
	}

	validity, err := util.ParseDateStringIntoLayout(res.PullVouchers[0].Vouchers[0].EndDate, util.DateMonthYearLayout)
	if err != nil {
		logger.Errorf("error parsing date: %s, err: %s", res.PullVouchers[0].Vouchers[0].EndDate, err)
	}

	return &offer.GetCouponResponse{
		CouponCode:     res.PullVouchers[0].Vouchers[0].VoucherNo,
		CouponPin:      res.PullVouchers[0].Vouchers[0].Voucherpin,
		CouponValidity: validity,
	}, nil
}
