package ordersystem

import (
	"stashfin.com/stashfin/magneto/pkg/ordersystem/repo"
	"stashfin.com/stashfin/magneto/pkg/ordersystem/service"
)

var serviceInstance *service.Service

func InitializeService(offerService service.IPostPaymentHandler) {
	serviceInstance = service.NewService(
		&service.Repo{
			ProductSKU:     repo.NewProductSKU(),
			Cart:           repo.NewCart(),
			CartItem:       repo.NewCartItem(),
			Order:          repo.NewOrder(),
			OrderItem:      repo.NewOrderItem(),
			Discount:       repo.NewDiscount(),
			OrdersDiscount: repo.NewOrdersDiscount(),
		},
		service.PostPaymentHandler{
			Offer: offerService,
		},
	)
}

func GetService() *service.Service {
	return serviceInstance
}
