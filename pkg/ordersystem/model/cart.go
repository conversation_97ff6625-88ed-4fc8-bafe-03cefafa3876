package model

import (
	"time"
)

type Cart struct {
	ID        int       `gorm:"column:id"`
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`
	IsActive  bool      `gorm:"column:is_active"`

	UserID        int64        `gorm:"column:user_id"`
	Category      CategoryType `gorm:"column:category"`
	TotalPrice    float32      `gorm:"column:total_price"`
	DiscountPrice float32      `gorm:"column:discount_price"`
}

func (*Cart) TableName() string {
	return "cart"
}
