package model

import (
	"time"
)

type CartItem struct {
	ID        int       `gorm:"column:id"`
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`

	CartID int   `gorm:"column:cart_id"`
	Cart   *Cart `gorm:"foreignKey:cart_id"`

	SkuID int         `gorm:"column:sku_id"`
	Sku   *ProductSKU `gorm:"foreignKey:sku_id"`

	Quantity int `gorm:"column:quantity"`
}

func (*CartItem) TableName() string {
	return "cart_items"
}
