package model

// Category of the sku/cart/order
type CategoryType string

const (
	OfferCategoryType CategoryType = "offer"
)

// OrderStatus represents the different stages of an order
type OrderStatus int

const (
	// Undefined represents an order that has an invalid status
	OrderStatusUndefined OrderStatus = 0
	// Pending represents an order that has been placed but not yet processed
	OrderStatusPending OrderStatus = 1
	// Success represents an order that has been successfully completed
	OrderStatusSuccess OrderStatus = 2
	// Failed represents an order that could not be completed
	OrderStatusFailed OrderStatus = 3
	// Refunded represents an order that has been refunded
	OrderStatusRefunded OrderStatus = 4
)

func (o OrderStatus) Int() int {
	return int(o)
}

func ConvertOrderStatus(orderStatus int) OrderStatus {
	return OrderStatus(orderStatus)
}
