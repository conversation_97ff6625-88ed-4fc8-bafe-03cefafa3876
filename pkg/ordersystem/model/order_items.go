package model

import (
	"time"
)

type OrderItem struct {
	ID        int       `gorm:"column:id"`
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`
	UID       string    `gorm:"column:uid"`

	OrderID int    `gorm:"column:order_id"`
	Order   *Order `gorm:"foreignKey:order_id"`

	SkuID int         `gorm:"column:sku_id"`
	Sku   *ProductSKU `gorm:"foreignKey:sku_id"`

	OriginalPrice   float32     `gorm:"column:orignal_price"`
	DiscountedPrice float32     `gorm:"column:discounted_price"`
	OrderStatus     OrderStatus `gorm:"column:order_status"`
	Remark          string      `gorm:"column:remark"`
}

func (*OrderItem) TableName() string {
	return "order_items"
}
