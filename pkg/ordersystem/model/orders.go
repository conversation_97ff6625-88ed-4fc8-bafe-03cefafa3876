package model

import (
	"time"
)

type Order struct {
	ID        int       `gorm:"column:id"`
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`
	UID       string    `gorm:"column:uid"`

	UserID      int64        `gorm:"column:user_id"`
	Category    CategoryType `gorm:"column:category"`
	OrderStatus OrderStatus  `gorm:"column:order_status"`
	TotalPrice  float32      `gorm:"column:total_price"`
	PaymentID   string       `gorm:"column:payment_id"`
	Remark      string       `gorm:"column:remark"`
}

func (*Order) TableName() string {
	return "orders"
}
