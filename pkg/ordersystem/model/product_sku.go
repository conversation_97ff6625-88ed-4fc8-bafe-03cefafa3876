package model

import (
	"time"
)

type ProductSKU struct {
	ID        int       `gorm:"column:id;primaryKey"`
	UID       string    `gorm:"column:uid"`
	CreatedAt time.Time `gorm:"column:created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at"`

	IsActive       bool         `gorm:"column:is_active"`
	IdentifierType CategoryType `gorm:"column:identifier_type"`
	IdentifierUID  string       `gorm:"column:identifier_uid"`

	OriginalPrice      float32 `gorm:"column:original_price"`
	DiscountedPrice    float32 `gorm:"column:discounted_price"`
	DiscountPercentage int     `gorm:"column:discount_percentage"`
}

func (*ProductSKU) TableName() string {
	return "product_sku"
}
