package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/ordersystem/model"
)

type Cart struct{}

func NewCart() *Cart {
	return &Cart{}
}

func (*Cart) GetByID(ctx context.Context, id int) (*model.Cart, error) {
	condition := model.Cart{ID: id}
	return helper.GenericGet(ctx, &condition)
}

func (*Cart) Create(
	ctx context.Context,
	userID int64,
	category model.CategoryType,
) (*model.Cart, error) {
	obj := model.Cart{
		UserID:   userID,
		Category: category,
		IsActive: true,
	}
	return helper.GenericCreate(ctx, &obj)
}

func (*Cart) Update(ctx context.Context, obj *model.Cart, updateFields []string) error {
	return helper.GenericUpdate(ctx, obj, updateFields)
}

func (*Cart) GetActiveCartByCategory(ctx context.Context, userID int64, category model.CategoryType) (*model.Cart, error) {
	condition := model.Cart{
		UserID:   userID,
		IsActive: true,
		Category: category,
	}

	return helper.GenericGet(ctx, &condition)
}

func (*Cart) GetCartItems(ctx context.Context, userID int64, category model.CategoryType) ([]model.Cart, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []model.Cart
	err := db.WithContext(ctx).
		Joins("CartItem").
		Joins("CartItem.Sku").
		Where(&model.Cart{UserID: userID, Category: category}).
		Last(&obj).Error

	return obj, err
}
