package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/ordersystem/model"
)

type CartItem struct{}

func NewCartItem() *CartItem {
	return &CartItem{}
}

func (*CartItem) GetByCartAndSKU(ctx context.Context, cartID int, skuID int) (*model.CartItem, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.CartItem
	err := db.WithContext(ctx).
		Where(&model.CartItem{CartID: cartID, SkuID: skuID}).
		First(&obj).Error

	return &obj, err
}

func (*CartItem) Create(ctx context.Context, cartID int, skuID int, quantity int) (*model.CartItem, error) {
	obj := model.CartItem{
		CartID:   cartID,
		SkuID:    skuID,
		Quantity: quantity,
	}
	return helper.GenericCreate(ctx, &obj)
}

func (*CartItem) Update(ctx context.Context, obj *model.CartItem, updateFields []string) error {
	return helper.GenericUpdate(ctx, obj, updateFields)
}

func (*CartItem) FilterByCart(ctx context.Context, cartID int) ([]model.CartItem, error) {
	condition := model.CartItem{CartID: cartID}
	return helper.GenericFilter(ctx, &condition, "Sku")
}

func (*CartItem) Delete(ctx context.Context, obj *model.CartItem) error {
	return helper.GenericDelete(ctx, obj)
}
