package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/ordersystem/model"
)

type OrderItem struct{}

func NewOrderItem() *OrderItem {
	return &OrderItem{}
}

func (*OrderItem) IsUniqueUID(ctx context.Context, uid string) (bool, error) {
	db := config.GetConnectionCtx(ctx)
	var count int64
	err := db.WithContext(ctx).
		Model(&model.OrderItem{}).
		Where(&model.OrderItem{UID: uid}).
		Count(&count).Error
	if err != nil {
		return false, err
	}

	return count == 0, nil
}

func (oi *OrderItem) Create(
	ctx context.Context,
	orderID int,
	skuID int,
	orignalPrice float32,
	discountedPrice float32,
) (*model.OrderItem, error) {
	uid, err := helper.GenerateUniqueUID(ctx, oi, helper.UIDSize8)
	if err != nil {
		return nil, err
	}

	obj := model.OrderItem{
		UID:             uid,
		OrderID:         orderID,
		SkuID:           skuID,
		OriginalPrice:   orignalPrice,
		DiscountedPrice: discountedPrice,
	}

	return helper.GenericCreate(ctx, &obj)
}

func (*OrderItem) FilterByOrderID(ctx context.Context, orderID int) ([]model.OrderItem, error) {
	condition := model.OrderItem{
		OrderID: orderID,
	}

	return helper.GenericFilter(ctx, &condition, "Sku")
}

func (*OrderItem) UpdateByOrder(ctx context.Context, orderID int, status model.OrderStatus) error {
	db := config.GetConnectionCtx(ctx)

	return db.WithContext(ctx).
		Model(&model.OrderItem{}).
		Where("order_id = ?", orderID).
		Update("order_status", status.Int()).
		Error
}

func (*OrderItem) SetPendingOrderItemsByHoursFailed(ctx context.Context, hours int) error {
	db := config.GetConnectionCtx(ctx)

	db.Model(&model.OrderItem{}).
		Where("created_at < DATE_SUB(NOW(),INTERVAL ? HOUR) and order_status in (?, ?)", hours, model.OrderStatusUndefined, model.OrderStatusPending).
		Updates(&model.OrderItem{OrderStatus: model.OrderStatusFailed, Remark: "timeout"})
	return nil
}
