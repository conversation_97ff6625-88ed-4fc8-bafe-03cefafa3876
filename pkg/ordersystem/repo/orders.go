package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/ordersystem/model"
)

type Order struct{}

func NewOrder() *Order {
	return &Order{}
}

func (*Order) IsUniqueUID(ctx context.Context, uid string) (bool, error) {
	db := config.GetConnectionCtx(ctx)
	var count int64
	err := db.WithContext(ctx).
		Model(&model.Order{}).
		Where(&model.Order{UID: uid}).
		Count(&count).Error
	if err != nil {
		return false, err
	}

	return count == 0, nil
}

func (o *Order) Create(ctx context.Context, userID int64, category model.CategoryType, price float32) (*model.Order, error) {
	uid, err := helper.GenerateUniqueUID(ctx, o, helper.UIDSize8)
	if err != nil {
		return nil, err
	}

	obj := model.Order{
		UID:         uid,
		UserID:      userID,
		Category:    category,
		OrderStatus: model.OrderStatusPending,
		TotalPrice:  price,
	}
	return helper.GenericCreate(ctx, &obj)
}

func (*Order) Update(ctx context.Context, obj *model.Order, updateFields []string) error {
	return helper.GenericUpdate(ctx, obj, updateFields)
}

func (*Order) GetByUID(ctx context.Context, uid string) (*model.Order, error) {
	obj := model.Order{UID: uid}
	return helper.GenericGet(ctx, &obj)
}

func (*Order) FilterByCategory(ctx context.Context, userID int64, category model.CategoryType) ([]model.Order, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []model.Order
	err := db.WithContext(ctx).
		Model(&model.Order{}).
		Where(&model.Order{UserID: userID, Category: category}).
		Order("id DESC").
		Scan(&obj).
		Error

	return obj, err
}
func (*Order) GetCurrentMonthOrders(ctx context.Context, userID int64, category model.CategoryType) ([]model.Order, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []model.Order
	err := db.WithContext(ctx).
		Model(&model.Order{}).
		Where("user_id = ? and category = ? and MONTH(created_at) = MONTH(CURRENT_DATE()) AND YEAR(created_at) = YEAR(CURRENT_DATE())", userID, category).
		Order("id DESC").
		Scan(&obj).
		Error

	return obj, err
}

func (*Order) GetLatestOrder(ctx context.Context, userID int64, catogory model.CategoryType) (*model.Order, error) {
	condition := model.Order{UserID: userID, Category: catogory}
	return helper.GenericGetLast(ctx, &condition)
}

func (*Order) FilterSuccessOrderIDs(ctx context.Context, userID int64) ([]int, error) {
	db := config.GetConnectionCtx(ctx)

	var obj []int
	err := db.WithContext(ctx).
		Model(&model.Order{}).
		Where(&model.Order{UserID: userID, OrderStatus: model.OrderStatusSuccess}).
		Pluck("id", &obj).Error
	if err != nil {
		return nil, err
	}

	return obj, nil
}

func (*Order) SetPendingOrdersByHoursFailed(ctx context.Context, hours int) error {
	db := config.GetConnectionCtx(ctx)

	db.Model(&model.Order{}).
		Where("created_at < DATE_SUB(NOW(),INTERVAL ? HOUR) and order_status in (?, ?)", hours, model.OrderStatusUndefined, model.OrderStatusPending).
		Updates(&model.Order{OrderStatus: model.OrderStatusFailed, Remark: "timeout"})
	return nil
}
