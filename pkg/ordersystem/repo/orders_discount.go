package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/ordersystem/model"
)

type OrdersDiscount struct{}

func NewOrdersDiscount() *OrdersDiscount {
	return &OrdersDiscount{}
}

func (*OrdersDiscount) IsDiscountTaken(ctx context.Context, discountID int, successOrderIDs []int) (bool, error) {
	db := config.GetConnectionCtx(ctx)

	var count int64
	err := db.WithContext(ctx).
		Model(&model.OrdersDiscounts{}).
		Where("discount_id = ? AND order_id IN (?)", discountID, successOrderIDs).
		Count(&count).Error

	return count > 0, err
}

func (*OrdersDiscount) Create(ctx context.Context, orderID int, discountID int) (*model.OrdersDiscounts, error) {
	obj := model.OrdersDiscounts{
		OrderID:    orderID,
		DiscountID: discountID,
	}
	return helper.GenericCreate(ctx, &obj)
}

func (*OrdersDiscount) GetByOrder(ctx context.Context, orderID int) (*model.OrdersDiscounts, error) {
	condition := model.OrdersDiscounts{
		OrderID: orderID,
	}

	return helper.GenericGet(ctx, &condition, "Discount")
}
