package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/ordersystem/model"
)

type ProductSKU struct{}

func NewProductSKU() *ProductSKU {
	return &ProductSKU{}
}

func (*ProductSKU) IsUniqueUID(ctx context.Context, uid string) (bool, error) {
	db := config.GetConnectionCtx(ctx)
	var count int64
	err := db.WithContext(ctx).
		Model(&model.ProductSKU{}).
		Where(&model.ProductSKU{UID: uid}).
		Count(&count).Error
	if err != nil {
		return false, err
	}

	return count == 0, nil
}

func (*ProductSKU) GetByIdentifier(ctx context.Context, identifierType model.CategoryType, identifierUID string) (*model.ProductSKU, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.ProductSKU
	err := db.WithContext(ctx).
		Where(&model.ProductSKU{IdentifierUID: identifierUID, IdentifierType: identifierType}).
		Last(&obj).Error

	return &obj, err
}

func (r *ProductSKU) Create(ctx context.Context, categoryType model.CategoryType, identifierUID string, price float32) (*model.ProductSKU, error) {
	uid, err := helper.GenerateUniqueUID(ctx, r, helper.UIDSize8)
	if err != nil {
		return nil, err
	}

	obj := model.ProductSKU{
		UID:                uid,
		IsActive:           false,
		IdentifierType:     categoryType,
		IdentifierUID:      identifierUID,
		OriginalPrice:      price,
		DiscountedPrice:    price,
		DiscountPercentage: 0,
	}

	return helper.GenericCreate(ctx, &obj)
}

func (*ProductSKU) Update(ctx context.Context, obj *model.ProductSKU, updateFields []string) error {
	return helper.GenericUpdate(ctx, obj, updateFields)
}

func (*ProductSKU) MarkSKUIncative(ctx context.Context, identifierUID string, identifierType model.CategoryType) error {

	db := config.GetConnectionCtx(ctx)
	err := db.WithContext(ctx).
		Where(&model.ProductSKU{IdentifierUID: identifierUID, IdentifierType: identifierType}).
		Updates(&model.ProductSKU{IsActive: false}).Error

	return err
}
