package service

import (
	"context"
	"errors"
	"fmt"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/ordersystem/model"
	"stashfin.com/stashfin/magneto/util"
)

var (
	ErrCartNotFound = errors.New("cart not found")
	ErrSKUNotFound  = errors.New("sku not found")
)

type CartItemDetails struct {
	IdentifierUID      string
	Quantity           int
	OriginalPrice      float32
	DiscountedPrice    float32
	DiscountPercentage int
}

type CartDetail struct {
	Items           []CartItemDetails
	TotalPrice      float32
	DiscountedPrice float32
}

func (s *Service) GetCart(ctx context.Context, category model.CategoryType, userID int64) (*CartDetail, error) {
	cartObj, err := s.repo.Cart.GetActiveCartByCategory(ctx, userID, category)
	if util.IsNotFoundError(err) {
		return nil, ErrCartNotFound
	}

	if err != nil {
		return nil, fmt.Errorf("error fetching cart for user: %d, err: %w", userID, err)
	}

	cartItemsObj, err := s.repo.CartItem.FilterByCart(ctx, cartObj.ID)
	if err != nil {
		return nil, fmt.Errorf("error fetching cart items: %w", err)
	}

	var cartDetails []CartItemDetails
	for _, cartItem := range cartItemsObj {
		cartDetails = append(cartDetails, CartItemDetails{
			IdentifierUID:      cartItem.Sku.IdentifierUID,
			Quantity:           cartItem.Quantity,
			OriginalPrice:      cartItem.Sku.OriginalPrice,
			DiscountedPrice:    cartItem.Sku.DiscountedPrice,
			DiscountPercentage: cartItem.Sku.DiscountPercentage,
		})
	}

	return &CartDetail{
		Items:           cartDetails,
		TotalPrice:      cartObj.TotalPrice,
		DiscountedPrice: cartObj.DiscountPrice,
	}, nil
}

func (s *Service) UpdateCart(
	ctx context.Context,
	category model.CategoryType,
	userID int64,
	identifierUID string,
	quantity int,
) error {
	// fetch and validate sku using identifier
	skuObj, err := s.repo.ProductSKU.GetByIdentifier(ctx, category, identifierUID)
	if util.IsNotFoundError(err) {
		return ErrSKUNotFound
	}

	if err != nil {
		return fmt.Errorf("error fetching sku: %w", err)
	}

	// fetch any active cart for this user and category and create new if not found
	cartObj, err := s.repo.Cart.GetActiveCartByCategory(ctx, userID, category)
	if util.IsServiceError(err) {
		return fmt.Errorf("error fetching cart, err: %w", err)
	}

	if util.IsNotFoundError(err) {
		cartObj, err = s.repo.Cart.Create(ctx, userID, category)
		if err != nil {
			return fmt.Errorf("error creating cart, err: %w", err)
		}
	}

	// check if sku is already in cart, if so update quantity else create new
	cartItemObj, err := s.repo.CartItem.GetByCartAndSKU(ctx, cartObj.ID, skuObj.ID)
	if util.IsServiceError(err) {
		return fmt.Errorf("error fetching cart item, err: %w", err)
	}

	if util.IsNotFoundError(err) {

		// Check to make sure that user is not able to increase cart amount over a fixed limit OR if he is already over limit, then he can only decrease the cart value
		if cartObj.DiscountPrice >= float32(config.GetConfigValues().CartTotalDiscountedAmountLimit) {
			return fmt.Errorf("update failed because total cart value can't exceed 5000")
		} else {
			newDiscountedPrice := cartObj.DiscountPrice + (skuObj.DiscountedPrice * float32(quantity))
			if newDiscountedPrice > float32(config.GetConfigValues().CartTotalDiscountedAmountLimit) {
				return fmt.Errorf("update failed because total cart value can't exceed 5000")
			}
		}

		_, err = s.repo.CartItem.Create(ctx, cartObj.ID, skuObj.ID, quantity)
		if err != nil {
			return err
		}
		return s.UpdateCartPrice(ctx, cartObj.ID)
	}

	// Check to make sure that user is not able to increase cart amount over a fixed limit OR if he is already over limit, then he can only decrease the cart value
	if cartObj.DiscountPrice >= float32(config.GetConfigValues().CartTotalDiscountedAmountLimit) {
		if quantity >= cartItemObj.Quantity {
			return fmt.Errorf("update failed because total cart value can't exceed 5000")
		}
	} else {
		newDiscountedPrice := cartObj.DiscountPrice - (skuObj.DiscountedPrice * float32(cartItemObj.Quantity)) + (skuObj.DiscountedPrice * float32(quantity))
		if newDiscountedPrice > float32(config.GetConfigValues().CartTotalDiscountedAmountLimit) {
			return fmt.Errorf("update failed because total cart value can't exceed 5000")
		}
	}

	if quantity == 0 {
		err = s.repo.CartItem.Delete(ctx, cartItemObj)
	} else {
		cartItemObj.Quantity = quantity
		err = s.repo.CartItem.Update(ctx, cartItemObj, []string{"quantity"})
	}

	if err != nil {
		return fmt.Errorf("failed to update cart item, err: %w", err)
	}

	return s.UpdateCartPrice(ctx, cartObj.ID)
}

func (s *Service) RemoveCartSKU(ctx context.Context, cartID int, skuID int) error {
	cartItemObj, err := s.repo.CartItem.GetByCartAndSKU(ctx, cartID, skuID)
	if util.IsServiceError(err) {
		return fmt.Errorf("error fetching cart item, err: %w", err)
	}

	err = s.repo.CartItem.Delete(ctx, cartItemObj)

	if err != nil {
		return fmt.Errorf("failed to update cart item, err: %w", err)
	}

	return s.UpdateCartPrice(ctx, cartID)
}

func (s *Service) UpdateCartPrice(ctx context.Context, cartID int) error {
	cartObj, err := s.repo.Cart.GetByID(ctx, cartID)
	if err != nil {
		return err
	}

	cartItemObjs, err := s.repo.CartItem.FilterByCart(ctx, cartID)
	if util.IsServiceError(err) {
		return fmt.Errorf("failed to find cart item, err: %w", err)
	}

	var totalPrice, discountedPrice float32
	for _, cartItemObj := range cartItemObjs {
		totalPrice += float32(cartItemObj.Quantity) * cartItemObj.Sku.OriginalPrice
		discountedPrice += float32(cartItemObj.Quantity) * cartItemObj.Sku.DiscountedPrice
	}

	cartObj.TotalPrice = totalPrice
	cartObj.DiscountPrice = discountedPrice
	return s.repo.Cart.Update(ctx, cartObj, []string{"total_price", "discount_price"})
}
