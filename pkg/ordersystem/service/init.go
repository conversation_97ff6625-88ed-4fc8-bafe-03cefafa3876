package service

import (
	"context"

	"stashfin.com/stashfin/magneto/pkg/ordersystem/repo"
)

type Repo struct {
	ProductSKU     *repo.ProductSKU
	Cart           *repo.Cart
	CartItem       *repo.CartItem
	Order          *repo.Order
	OrderItem      *repo.OrderItem
	Discount       *repo.Discount
	OrdersDiscount *repo.OrdersDiscount
}

type Service struct {
	repo               *Repo
	postPaymentHandler PostPaymentHandler
}

type IPostPaymentHandler interface {
	PostPaymentHandler(ctx context.Context, orderUID string, orderStatus int) error
}

type PostPaymentHandler struct {
	Offer IPostPaymentHandler
}

func NewService(repo *Repo, postPaymentHandler PostPaymentHandler) *Service {
	return &Service{
		repo:               repo,
		postPaymentHandler: postPaymentHandler,
	}
}
