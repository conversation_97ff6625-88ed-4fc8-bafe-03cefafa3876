package service

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/ordersystem/model"
)

func (s *Service) GetOfferCart(ctx context.Context, userID int64) (*CartDetail, error) {
	return s.GetCart(ctx, model.OfferCategoryType, userID)
}

func (s *Service) UpdateOfferCart(ctx context.Context, userID int64, identifierUID string, quantity int) error {
	return s.UpdateCart(ctx, model.OfferCategoryType, userID, identifierUID, quantity)
}

func (s *Service) PlaceOfferUserOrder(ctx context.Context, userID int64) error {
	return s.PlaceUserOrder(ctx, model.OfferCategoryType, userID, config.GetConfigValues().OfferLocCouponCode)
}

func (s *Service) FetchAllOfferOrders(ctx context.Context, userID int64) ([]OrderDetail, error) {
	return s.FetchAllOrders(ctx, model.OfferCategoryType, userID)
}

func (s *Service) GetCurrentMonthOffierOrders(ctx context.Context, userID int64) ([]OrderShortDetail, error) {
	return s.GetCurrentMonthOrders(ctx, model.OfferCategoryType, userID)
}

func (s *Service) GetLatestOfferOrder(ctx context.Context, userID int64) (*OrderDetail, error) {
	return s.GetLatestOrder(ctx, model.OfferCategoryType, userID)
}

func (s *Service) CreateOfferSKU(ctx context.Context, identifierUID string, price float32) (*model.ProductSKU, error) {
	return s.repo.ProductSKU.Create(ctx, model.OfferCategoryType, identifierUID, price)
}

func (s *Service) MarkSKUIncative(ctx context.Context, identifierUID string) error {
	return s.repo.ProductSKU.MarkSKUIncative(ctx, identifierUID, model.OfferCategoryType)
}
