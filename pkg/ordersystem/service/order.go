package service

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/pkg/ordersystem/model"
	"stashfin.com/stashfin/magneto/util"
)

func (s *Service) PlaceUserOrder(ctx context.Context, category model.CategoryType, userID int64, couponCode string) error {
	logger := util.CLogCtx(ctx)

	// fetch the cart for the user and category
	cartObj, err := s.repo.Cart.GetActiveCartByCategory(ctx, userID, category)
	if util.IsServiceError(err) {
		return fmt.Errorf("error fetching cart, err: %w", err)
	}

	if util.IsNotFoundError(err) {
		return errors.New("cart not found")
	}

	// Need to check if all SKUs are active and in stock, if not Update the cart and throw error
	isUpdated, err := s.ValidateCartItem(ctx, cartObj.ID)
	if err != nil {
		return fmt.Errorf("error validating cart items, err: %s", err)
	}

	if isUpdated {
		return helper.NewCustomError("cart updated due to some items in cart updated", http.StatusBadRequest)
	}

	// create an order for the cart
	orderObj, err := s.repo.Order.Create(ctx, userID, category, cartObj.DiscountPrice)
	if err != nil {
		return fmt.Errorf("failed to create order, err: %w", err)
	}

	// mark the cart as completed (i.e. is_active = false)
	cartObj.IsActive = false
	err = s.repo.Cart.Update(ctx, cartObj, []string{"is_active"})
	if err != nil {
		return fmt.Errorf("failed to update cart, err: %w", err)
	}

	// fetch all cart items
	cartItemObjs, err := s.repo.CartItem.FilterByCart(ctx, cartObj.ID)
	if util.IsNotFoundError(err) || len(cartItemObjs) == 0 {
		return errors.New("no items in cart")
	}

	if util.IsServiceError(err) {
		return fmt.Errorf("error fetching cart items, err: %w", err)
	}

	// create order items for each cart item
	for _, cartItemObj := range cartItemObjs {
		for i := 0; i < cartItemObj.Quantity; i++ {
			_, err = s.repo.OrderItem.Create(
				ctx,
				orderObj.ID,
				cartItemObj.SkuID,
				cartItemObj.Sku.OriginalPrice,
				cartItemObj.Sku.DiscountedPrice,
			)
			if err != nil {
				return fmt.Errorf("failed to create order item, err: %w", err)
			}
		}
	}

	if couponCode != "" {
		discountObj, err := s.repo.Discount.GetByCode(ctx, couponCode)
		if err != nil {
			logger.Info("coupon code not found")
			return nil
		}

		IsEligibleForLOCDiscount, err := customer.GetCustomerService().IsCustomerOfferDiscountEligible(ctx, userID)
		if err != nil {
			logger.Errorf("failed to check if customer location is open, err: %s", err)
		}

		if !IsEligibleForLOCDiscount {
			logger.Infof("customer not eligible for LOC discount. While trying to apply for order id: %d, discount id: %d, user id: %d", orderObj.ID, discountObj.ID, userID)
			return nil
		}

		isDiscountTaken, err := s.IsDiscountAlreadyTaken(ctx, couponCode, userID)
		if err != nil {
			logger.Errorf("failed to check discount eligibility, err: %s", err)
			isDiscountTaken = true
		}

		if isDiscountTaken && discountObj.IsSingleUse {
			logger.Infof("coupon code already used. While trying to apply for order id: %d, discount id: %d", orderObj.ID, discountObj.ID)
			return nil
		}

		_, err = s.repo.OrdersDiscount.Create(ctx, orderObj.ID, discountObj.ID)
		if err != nil {
			logger.Errorf("failed to create order discount, err: %s", err)
			return nil
		}
	}

	return nil
}

func (s *Service) FetchOrderStatus(ctx context.Context, orderUID string) (model.OrderStatus, error) {
	orderObj, err := s.repo.Order.GetByUID(ctx, orderUID)
	if util.IsNotFoundError(err) {
		return model.OrderStatusUndefined, fmt.Errorf("invalid order uid")
	}

	if util.IsServiceError(err) {
		return model.OrderStatusUndefined, fmt.Errorf("error fetching order, err: %w", err)
	}

	return orderObj.OrderStatus, nil
}

type OrderItemDetail struct {
	UID             string
	IdentifierUID   string
	OriginalPrice   float32
	DiscountedPrice float32
	OrderStatus     model.OrderStatus
}

type OrderDetail struct {
	OrderItems  []OrderItemDetail
	TotalPrice  float32
	PaymentID   string
	UID         string
	OrderStatus model.OrderStatus
}

type OrderShortDetail struct {
	TotalPrice  float32
	PaymentID   string
	UID         string
	OrderStatus model.OrderStatus
}

func (s *Service) GetCurrentMonthOrders(ctx context.Context, category model.CategoryType, userID int64) ([]OrderShortDetail, error) {
	ordersObjs, err := s.repo.Order.GetCurrentMonthOrders(ctx, userID, category)
	if util.IsNotFoundError(err) || len(ordersObjs) == 0 {
		return nil, nil
	}

	if util.IsServiceError(err) {
		return nil, fmt.Errorf("error fetching orders, err: %w", err)
	}
	var orderShortDetails []OrderShortDetail
	for _, orderObj := range ordersObjs {
		if orderObj.OrderStatus == model.OrderStatusFailed {
			continue
		}
		orderShortDetails = append(orderShortDetails, OrderShortDetail{
			TotalPrice:  orderObj.TotalPrice,
			PaymentID:   orderObj.PaymentID,
			UID:         orderObj.UID,
			OrderStatus: orderObj.OrderStatus,
		})
	}

	return orderShortDetails, nil
}

func (s *Service) FetchAllOrders(ctx context.Context, category model.CategoryType, userID int64) ([]OrderDetail, error) {
	ordersObjs, err := s.repo.Order.FilterByCategory(ctx, userID, category)
	if util.IsNotFoundError(err) || len(ordersObjs) == 0 {
		return nil, nil
	}

	if util.IsServiceError(err) {
		return nil, fmt.Errorf("error fetching orders, err: %w", err)
	}

	var orderDetails []OrderDetail
	for _, orderObj := range ordersObjs {
		orderItemObjs, err := s.repo.OrderItem.FilterByOrderID(ctx, orderObj.ID)
		if util.IsServiceError(err) {
			return nil, fmt.Errorf("error fetching order items, err: %w", err)
		}

		if util.IsNotFoundError(err) || len(orderItemObjs) == 0 {
			continue
		}

		var orderItemDetails []OrderItemDetail
		for _, orderItemDetail := range orderItemObjs {
			orderItemDetails = append(orderItemDetails, OrderItemDetail{
				UID:             orderItemDetail.UID,
				IdentifierUID:   orderItemDetail.Sku.IdentifierUID,
				OriginalPrice:   orderItemDetail.OriginalPrice,
				DiscountedPrice: orderItemDetail.DiscountedPrice,
				OrderStatus:     orderItemDetail.OrderStatus,
			})
		}

		orderDetails = append(orderDetails, OrderDetail{
			TotalPrice:  orderObj.TotalPrice,
			PaymentID:   orderObj.PaymentID,
			UID:         orderObj.UID,
			OrderStatus: orderObj.OrderStatus,
			OrderItems:  orderItemDetails,
		})
	}

	return orderDetails, nil
}

func (s *Service) GetLatestOrder(ctx context.Context, category model.CategoryType, userID int64) (*OrderDetail, error) {
	orderObj, err := s.repo.Order.GetLatestOrder(ctx, userID, category)
	if util.IsNotFoundError(err) {
		return nil, fmt.Errorf("no order found")
	}

	if util.IsServiceError(err) {
		return nil, fmt.Errorf("error fetching orders, err: %w", err)
	}

	orderItemObjs, err := s.repo.OrderItem.FilterByOrderID(ctx, orderObj.ID)
	if util.IsServiceError(err) {
		return nil, fmt.Errorf("error fetching order items, err: %w", err)
	}

	if util.IsNotFoundError(err) || len(orderItemObjs) == 0 {
		return nil, fmt.Errorf("no items in order")
	}

	var orderItemDetails []OrderItemDetail
	for _, orderItemDetail := range orderItemObjs {
		orderItemDetails = append(orderItemDetails, OrderItemDetail{
			UID:             orderItemDetail.UID,
			IdentifierUID:   orderItemDetail.Sku.IdentifierUID,
			OriginalPrice:   orderItemDetail.OriginalPrice,
			DiscountedPrice: orderItemDetail.DiscountedPrice,
			OrderStatus:     orderItemDetail.OrderStatus,
		})
	}

	var discountPercentage, discountCap float32 = 0, 0
	orderDiscountObj, err := s.repo.OrdersDiscount.GetByOrder(ctx, orderObj.ID)
	if util.IsServiceError(err) {
		return nil, fmt.Errorf("error fetching order discounts, err: %w", err)
	}

	if util.IsNotFoundError(err) {
		discountPercentage = 0
		discountCap = 0
	} else {
		discountPercentage = orderDiscountObj.Discount.DiscountPercentage
		discountCap = orderDiscountObj.Discount.DiscountCap
	}

	discountPrice := orderObj.TotalPrice * (discountPercentage) / 100

	if discountCap != 0 && discountPrice > discountCap {
		discountPrice = discountCap
	}

	finalAmount := orderObj.TotalPrice - discountPrice

	return &OrderDetail{
		TotalPrice:  finalAmount,
		PaymentID:   orderObj.PaymentID,
		UID:         orderObj.UID,
		OrderStatus: orderObj.OrderStatus,
		OrderItems:  orderItemDetails,
	}, nil
}

func (s *Service) ValidateCartItem(ctx context.Context, cartID int) (bool, error) {
	// fetch all cart items
	cartItemObjs, err := s.repo.CartItem.FilterByCart(ctx, cartID)
	if util.IsNotFoundError(err) || len(cartItemObjs) == 0 {
		return false, errors.New("no items in cart")
	}

	if util.IsServiceError(err) {
		return false, fmt.Errorf("error fetching cart items, err: %w", err)
	}

	isUpdated := false
	for _, cartItemObj := range cartItemObjs {
		// if cart sku is not active, remove it from cart
		if !cartItemObj.Sku.IsActive {
			// remove sku from cart
			isUpdated = true
			err = s.repo.CartItem.Delete(ctx, &cartItemObj)
			if err != nil {
				return false, fmt.Errorf("failed to update cart item, err: %w", err)
			}
		}
	}

	if isUpdated {
		return true, s.UpdateCartPrice(ctx, cartID)
	}

	return isUpdated, nil
}
