package service

import (
	"context"
	"fmt"

	"stashfin.com/stashfin/magneto/pkg/ordersystem/model"
	"stashfin.com/stashfin/magneto/util"
)

func (s *Service) ProcessPostOrder(ctx context.Context, orderUID string, orderStatus int, paymentID string) error {
	logger := util.CLogCtx(ctx)
	orderObj, err := s.repo.Order.GetByUID(ctx, orderUID)
	logger.Infof("Processing post order for order UID: %s, status: %d", orderUID, orderStatus)
	if util.IsNotFoundError(err) {
		return fmt.Errorf("invalid order uid")
	}

	if util.IsServiceError(err) {
		return fmt.Errorf("error fetching order, err: %w", err)
	}

	orderObj.OrderStatus = model.ConvertOrderStatus(orderStatus)
	orderObj.PaymentID = paymentID
	logger.Infof("Processing post update order for order UID: %s, status: %d", orderUID, orderStatus)
	err = s.repo.Order.Update(ctx, orderObj, []string{"order_status","payment_id"})
	if err != nil {
		return fmt.Errorf("error updating order, err: %w", err)
	}

	err = s.repo.OrderItem.UpdateByOrder(ctx, orderObj.ID, orderObj.OrderStatus)
	if err != nil {
		return fmt.Errorf("error updating order item, err: %w", err)
	}
	return s.postPaymentHandler.Offer.PostPaymentHandler(ctx, orderUID, orderStatus)
}

func (s *Service) GetDiscount(ctx context.Context, couponCode string) (float32, float32, error) {
	logger := util.CLogCtx(ctx)
	discountObj, err := s.repo.Discount.GetByCode(ctx, couponCode)
	if util.IsServiceError(err) {
		logger.Errorf("error fetching discount, err: %s", err)
		return 0, 0, err
	}

	return discountObj.DiscountPercentage, discountObj.DiscountCap, nil
}

func (s *Service) IsDiscountAlreadyTaken(ctx context.Context, couponCode string, customerID int64) (bool, error) {
	logger := util.CLogCtx(ctx)

	if couponCode == "" && customerID == 0 {
		return true, fmt.Errorf("invalid coupon code and customer id")
	}

	discountObj, err := s.repo.Discount.GetByCode(ctx, couponCode)
	if err != nil {
		logger.Info("coupon code not found")
		return true, fmt.Errorf("coupon code not found")
	}

	userSuccessOrdersIDs, err := s.repo.Order.FilterSuccessOrderIDs(ctx, customerID)
	if util.IsServiceError(err) {
		logger.Errorf("failed to fetch success order uids, err: %s", err)
		return true, fmt.Errorf("failed to fetch success order uids, err: %s", err)
	}

	if util.IsNotFoundError(err) || len(userSuccessOrdersIDs) == 0 {
		return false, nil
	}

	isDiscountTaken, err := s.repo.OrdersDiscount.IsDiscountTaken(ctx, discountObj.ID, userSuccessOrdersIDs)
	if err != nil {
		logger.Errorf("failed to check discount eligibility, err: %s", err)
		return true, fmt.Errorf("failed to check discount eligibility, err: %s", err)
	}

	return isDiscountTaken, nil
}

func (s *Service) OrdersCheckTimeout(ctx context.Context) error {
	logger := util.CLogCtx(ctx)
	err := s.repo.Order.SetPendingOrdersByHoursFailed(ctx, 1)
	if err != nil {
		logger.Errorf("failed to update pending orders, err: %s", err)
	}
	err = s.repo.OrderItem.SetPendingOrderItemsByHoursFailed(ctx, 1)
	if err != nil {
		logger.Errorf("failed to update pending order items, err: %s", err)
	}
	return err
}
