package restructure

import (
	"stashfin.com/stashfin/magneto/pkg/restructure/repo"
	"stashfin.com/stashfin/magneto/pkg/restructure/service"
)

var (
	serviceInstance *service.Service
)

func InitializeServices() {
	serviceInstance = &service.Service{
		RestructureLoanRepo:  repo.NewRestructureLoanRepo(),
		CommunicationLogRepo: repo.NewCommunicationLogRepo(),
	}
}

func GetService() *service.Service {
	return serviceInstance
}
