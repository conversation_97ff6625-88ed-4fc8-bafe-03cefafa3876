package model

import "time"

type IsSuccessStatus int

const (
	ReadStatus      IsSuccessStatus = 2
	FailedStatus    IsSuccessStatus = 3
	DeliveredStatus IsSuccessStatus = 4
	SentStatus      IsSuccessStatus = 5
	TriggeredStatus IsSuccessStatus = 6
	ScheduledStatus IsSuccessStatus = 7
)

var SuccessStatus = []IsSuccessStatus{ReadStatus, DeliveredStatus, TriggeredStatus}

type CommunicationLog struct {
	Id         int64           `gorm:"column:id"`
	CustomerId int64           `gorm:"column:customer_id"`
	TemplateId string          `gorm:"column:template_id"`
	IsSuccess  IsSuccessStatus `gorm:"column:is_success"`
	CreateDate time.Time       `gorm:"column:create_date"`
}

func (CommunicationLog) TableName() string {
	return "communication_log"
}
