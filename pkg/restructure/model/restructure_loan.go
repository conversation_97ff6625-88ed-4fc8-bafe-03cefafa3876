package model

import "time"

type RestructureLoan struct {
	ID                  int64     `gorm:"primaryKey;column:id"`
	CustomerID          int64     `gorm:"column:customer_id"`
	RequestExpiry       time.Time `gorm:"column:request_expiry"`
	IsOTPVerified       bool      `gorm:"column:is_otp_verified"`
	IsRestructuringDone bool      `gorm:"column:is_restructuring_done"`
	CreatedAt           time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt           time.Time `gorm:"column:updated_at;autoUpdateTime"`
	PendingEmi          int       `gorm:"column:pending_emi"`
	PendingLoanAmount   int       `gorm:"column:pending_loan_amount"`
	PendingLoanTenure   string    `gorm:"column:pending_loan_tenure"`
	RevisedEmi          int       `gorm:"column:revised_emi"`
	RevisedLoanTenure   string    `gorm:"column:revised_loan_tenure"`
}

func (*RestructureLoan) TableName() string {
	return "restructure_loan"
}
