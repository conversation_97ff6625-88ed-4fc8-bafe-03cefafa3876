package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/restructure/model"
)

type CommunicationLogRepo struct{}

func NewCommunicationLogRepo() *CommunicationLogRepo {
	return &CommunicationLogRepo{}
}

func (*CommunicationLogRepo) GetSuccessByCustomerAndTemplate(
	ctx context.Context,
	customerId int64,
	templateID string,
) (*model.CommunicationLog, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.CommunicationLog
	err := db.WithContext(ctx).
		Where("customer_id = ? AND template_id = ? AND is_success in ?", customerId, templateID, model.SuccessStatus).
		Last(&obj).Error

	return &obj, err
}
