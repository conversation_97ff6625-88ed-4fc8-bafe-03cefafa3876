package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/restructure/model"
)

type RestructureLoanRepo struct{}

func NewRestructureLoanRepo() *RestructureLoanRepo {
	return &RestructureLoanRepo{}
}

func (*RestructureLoanRepo) GetByCustomerID(ctx context.Context, customerID int64) (*model.RestructureLoan, error) {
	db := config.GetConnectionCtx(ctx)

	var obj model.RestructureLoan
	err := db.WithContext(ctx).
		Where(&model.RestructureLoan{CustomerID: customerID}).
		Last(&obj).Error
	if err != nil {
		return nil, err
	}

	return &obj, nil
}

func (*RestructureLoanRepo) Update(ctx context.Context, obj *model.RestructureLoan, updateFields []string) error {
	if len(updateFields) == 0 {
		updateFields = []string{"*"}
	}

	db := config.GetConnectionCtx(ctx)
	err := db.WithContext(ctx).Select(updateFields).Updates(obj).Error
	if err != nil {
		return err
	}

	return nil
}
