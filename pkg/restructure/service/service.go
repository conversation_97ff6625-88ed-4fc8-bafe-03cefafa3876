package service

import (
	"context"
	"errors"
	"strconv"
	"time"

	"gorm.io/gorm"

	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/util"
)

const (
	InitiatedCommunicationTemplateName = "replan1_wa"
	VerifiedCommunicationTemplate      = "replan2_sms"
)

var NotEligibleErr = helper.BadRequestError("You are not eligible for this")

func (s *Service) GetDetails(ctx context.Context, customerID int64) (*dtype.RestructureLoanDetailsResponse, error) {
	obj, err := s.RestructureLoanRepo.GetByCustomerID(ctx, customerID)
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, NotEligibleErr
	}

	if err != nil {
		return nil, err
	}

	return &dtype.RestructureLoanDetailsResponse{
		PendingLoanAmount: obj.PendingLoanAmount,
		PendingEMI:        obj.PendingEmi,
		PendingLoanTenure: obj.PendingLoanTenure,
		RevisedEMI:        obj.RevisedEmi,
		RevisedLoanTenure: obj.RevisedLoanTenure,
	}, nil
}

func (s *Service) SendOtp(ctx context.Context, customerID int64) error {
	err, phone := customer.GetCustomerService().GetCustomerPhone(ctx, customerID)
	if err != nil {
		return err
	}

	phoneNumber, err := strconv.Atoi(phone)
	if err != nil {
		return err
	}

	return client.GetApiV2Client().RequestOTP(ctx, phoneNumber)
}

func (s *Service) VerifyRestructure(ctx context.Context, customerID int64, otp int) error {
	if !config.GetConfigValues().RestructureEnabled {
		return helper.NotFoundError("Restructure loan is not available at the moment.")
	}

	err := customer.GetCustomerService().ValidateUserOTP(ctx, customerID, otp)
	if err != nil {
		return err
	}

	obj, err := s.RestructureLoanRepo.GetByCustomerID(ctx, customerID)
	if util.IsNotFoundError(err) {
		return NotEligibleErr
	}

	if util.IsServiceError(err) {
		return err
	}

	if obj.RequestExpiry.Before(time.Now()) {
		return NotEligibleErr
	}

	if obj.IsOTPVerified {
		return helper.BadRequestError("Request already recorded please wait for sometime")
	}

	_, err = s.CommunicationLogRepo.GetSuccessByCustomerAndTemplate(ctx, customerID, InitiatedCommunicationTemplateName)
	if util.IsNotFoundError(err) {
		return NotEligibleErr
	}

	if err != nil {
		return err
	}

	go func() {
		ctx := context.Background()
		templateData := map[string]any{}
		err := client.SendSMS(ctx, customerID, VerifiedCommunicationTemplate, templateData)
		if err != nil {
			util.Log.Errorln("failed to push message to redis, err: ", err)
		}
	}()

	obj.IsOTPVerified = true
	return s.RestructureLoanRepo.Update(ctx, obj, []string{"IsOTPVerified"})
}
