package scanpay

import (
	"context"

	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/scanpay/service"
)

type BankAccountDetailService interface {
	Save(context.Context, *dtype.Account, int64) error
	Get(context.Context, *dtype.Accounts, int64) error
	RemoveBankAccounts(context.Context, []string) error
	SaveUpiStatus(context.Context, int64, *dtype.UpiStatusDetail) error
}

type TransactionService interface {
	Get(context.Context, int64, int, int, *dtype.Transactions) error
	Save(context.Context, int64, *dtype.Transaction) error
	SaveICICIPaymentCallback(context.Context, *dtype.ICICIPaymentCallbackRequest) error
	CheckPendingTransactionStatus(context.Context, int64, string, string) (string, error)
	UpdatePendingTransStatus(context.Context)
}

type ComplaintService interface {
	GetComplaintReasonCodes(context.Context) (*dtype.ComplaintReasonCodesResponse, error)
	RaiseTransactionComplaint(context.Context, int64, *dtype.RaiseTransactionComplaint) (
		interface{}, error)
	CheckComplaintStatus(context.Context, int64, string, *dtype.GetComplaintStatus) (
		interface{}, error)
	SaveComplaintReasonCodes(context.Context) error
}

var transactionInstance TransactionService = &service.TransactionInterface{}
var bankAccountDetailInstance BankAccountDetailService = &service.BankAccountDetailInterface{}
var complaintServiceInstance ComplaintService = &service.ScanpayComplaintInterface{}

func GetTransactionService() TransactionService {
	return transactionInstance
}

func GetBankAccountDetailService() BankAccountDetailService {
	return bankAccountDetailInstance
}

func GetScanpayComplaintService() ComplaintService {
	return complaintServiceInstance
}
