package model

import (
	"time"
)

type BankAccountDetailsModel struct {
	Id                 int64     `gorm:"column:id;primaryKey;autoIncrement"`
	IsPrimaryAccount   bool      `gorm:"column:is_primary_account"`
	BankId             int32     `gorm:"column:bank_id"`
	CustomerId         int64     `gorm:"column:customer_id"`
	MMID               string    `gorm:"column:mmid"`
	Number             string    `gorm:"column:number"`
	AccountHolderName  string    `gorm:"column:account_holder_name"`
	Type               string    `gorm:"column:type"`
	Status             string    `gorm:"column:status"`
	ReferenceNumber    string    `gorm:"column:reference_number;unique"`
	Name               string    `gorm:"column:name"`
	IfscCode           string    `gorm:"column:ifsc_code"`
	Mbeba              string    `gorm:"column:mbeba"`
	AllowedCredentials string    `gorm:"column:allowed_credentials"`
	IsActve            bool      `gorm:"column:is_active"`
	CreatedAt          time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt          time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (BankAccountDetailsModel) TableName() string {
	return "bank_account_details"
}
