package model

import "time"

type CustomerUpiDetails struct {
	Id         int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CustomerId int64     `gorm:"column:customer_id"`
	ProfileId  string    `gorm:"column:profile_id"`
	Status     string    `gorm:"column:status"`
	UpiId      string    `gorm:"column:upi_id"`
	CreatedAt  time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt  time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (CustomerUpiDetails) TableName() string {
	return "customer_upi_details"
}
