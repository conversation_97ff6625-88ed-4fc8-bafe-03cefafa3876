package model

import "time"

type ICICIComplaintReasonCodesModel struct {
	ReasonCode        string    `gorm:"column:reason_code;primaryKey"`
	ReasonDescription string    `gorm:"column:reason_description"`
	IsActive          bool      `gorm:"column:is_active"`
	CreatedAt         time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt         time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (ICICIComplaintReasonCodesModel) TableName() string {
	return "icici_complaint_reason_codes"
}
