package model

import (
	"time"
)

type UpiTransHistory struct {
	Id             int64     `gorm:"column:id;primaryKey;autoIncrement"`
	CustomerId     int64     `gorm:"column:customer_id"`
	Amount         float32   `gorm:"column:amount"`
	Status         string    `gorm:"column:status"`
	PayeeName      string    `gorm:"column:payee_name"`
	PayerName      string    `gorm:"column:payer_name"`
	TransactionId  string    `gorm:"column:transaction_id;unique"`
	AccountNumber  string    `gorm:"column:account_number"`
	SequenceNumber string    `gorm:"column:sequence_number"`
	CompletionTime time.Time `gorm:"column:completion_time"`
	Type           string    `gorm:"column:type"`
	Remark         string    `gorm:"column:remark"`
	BankMMID       string    `gorm:"column:bank_mmid"`
	IsComplaint    bool      `gorm:"column:is_complaint"`
	CreatedAt      time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt      time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (UpiTransHistory) TableName() string {
	return "upi_transaction_history"
}
