package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/scanpay/model"
)

type BankAccountDetailsRepo interface {
	Save(context.Context, *model.BankAccountDetailsModel) error
	SaveInBatches(context.Context, *[]model.BankAccountDetailsModel) error
	Get(context.Context, int64) (*[]model.BankAccountDetailsModel, error)
	GetByAccRefId(context.Context, *model.BankAccountDetailsModel, string) error
	GetPrimaryAccount(context.Context, int64) (error, *model.BankAccountDetailsModel)
	DisableBankAccounts(context.Context, []string) error
	GetBankDetails(context.Context, int64, string, string) (*model.BankAccountDetailsModel, error)
}

type BankAccountDetailsDB struct {
}

var bankAccountDetailsInstance BankAccountDetailsRepo = &BankAccountDetailsDB{}

func GetBankAccountDetailsRepo() BankAccountDetailsRepo {
	return bankAccountDetailsInstance
}

func (*BankAccountDetailsDB) Save(ctx context.Context,
	bankAccountDetail *model.BankAccountDetailsModel) error {
	db := config.GetConnectionCtx(ctx)

	err := db.Save(bankAccountDetail).Error
	if err != nil {
		return err
	}

	return nil
}

func (*BankAccountDetailsDB) SaveInBatches(ctx context.Context,
	bankAccountDetails *[]model.BankAccountDetailsModel) error {
	db := config.GetConnectionCtx(ctx)

	err := db.Save(bankAccountDetails).Error
	if err != nil {
		return err
	}

	return nil
}

func (*BankAccountDetailsDB) GetPrimaryAccount(ctx context.Context, customerId int64) (
	error, *model.BankAccountDetailsModel) {
	db := config.GetConnectionCtx(ctx)
	var bankAccountDetail model.BankAccountDetailsModel

	err := db.Where("customer_id = ? and is_primary_account = 1 and is_active = 1", customerId).Find(
		&bankAccountDetail).Error
	if err != nil {
		return err, nil
	}

	return nil, &bankAccountDetail
}

func (*BankAccountDetailsDB) Get(ctx context.Context, customerId int64) (
	*[]model.BankAccountDetailsModel, error) {
	var bankAccountDetails []model.BankAccountDetailsModel
	db := config.GetConnectionCtx(ctx)

	err := db.Where("customer_id = ? and is_active = 1", customerId).Find(&bankAccountDetails).Error
	if err != nil {
		return nil, err
	}

	return &bankAccountDetails, nil
}

func (*BankAccountDetailsDB) GetByAccRefId(ctx context.Context,
	bankAccountDetail *model.BankAccountDetailsModel, accRef string) error {
	db := config.GetConnectionCtx(ctx)

	err := db.Where("reference_number = ?", accRef).Find(bankAccountDetail).Error
	if err != nil {
		return err
	}
	return nil
}

func (*BankAccountDetailsDB) DisableBankAccounts(ctx context.Context, accRefIds []string) error {
	db := config.GetConnectionCtx(ctx)

	err := db.Model(&model.BankAccountDetailsModel{}).Where(
		"reference_number IN ?", accRefIds).UpdateColumn("is_active", false).Error
	if err != nil {
		return err
	}

	return nil
}

func (*BankAccountDetailsDB) GetBankDetails(ctx context.Context, customerId int64, accountNo string, ifsc string) (
	*model.BankAccountDetailsModel, error) {
	db := config.GetConnectionCtx(ctx)
	var model model.BankAccountDetailsModel

	err := db.Where("customer_id = ? and number = ? and ifsc_code = ?", customerId, accountNo, ifsc).First(&model).Error
	if err != nil {
		return nil, err
	}

	return &model, nil
}
