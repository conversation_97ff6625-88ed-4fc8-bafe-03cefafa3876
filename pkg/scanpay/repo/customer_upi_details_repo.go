package repo

import (
	"context"
	"errors"
	"net/http"

	"gorm.io/gorm"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/scanpay/model"
)

type CustomerUpiDetailsRepo interface {
	GetByCustomerId(context.Context, int64) (*model.CustomerUpiDetails, error)
	Save(context.Context, int64, *dtype.UpiStatusDetail) error
	GetByUpiProfileId(context.Context, string) (*model.CustomerUpiDetails, error)
}

type CustomerUpiDetailsDB struct {
}

var customerUpiDetailsInstance CustomerUpiDetailsRepo = &CustomerUpiDetailsDB{}

func GetCustomerUpiDetailsRepo() CustomerUpiDetailsRepo {
	return customerUpiDetailsInstance
}

func (*CustomerUpiDetailsDB) GetByCustomerId(ctx context.Context, customerId int64) (
	*model.CustomerUpiDetails, error) {
	db := config.GetConnectionCtx(ctx)
	var model model.CustomerUpiDetails
	err := db.Where("customer_id = ? and status = 'success'", customerId).First(&model).Error
	if err != nil {
		if  errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, helper.NewCustomError("Customer upi details not found", http.StatusBadRequest)
		}
		return nil, err
	}
	
	return &model, nil
}

func (*CustomerUpiDetailsDB) Save(ctx context.Context, customerId int64, upiStatusDetail *dtype.UpiStatusDetail) error {
	db := config.GetConnectionCtx(ctx)
	var existingUpiStatusDetails model.CustomerUpiDetails

	err := db.Where("customer_id = ?", customerId).First(&existingUpiStatusDetails).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	existingUpiStatusDetails.CustomerId = customerId
	existingUpiStatusDetails.UpiId = upiStatusDetail.UpiId
	existingUpiStatusDetails.ProfileId = upiStatusDetail.ProfileId
	existingUpiStatusDetails.Status = upiStatusDetail.UpiStatus
	if err = db.Save(&existingUpiStatusDetails).Error; err != nil {
		return err
	}

	return nil
}

func (*CustomerUpiDetailsDB) GetByUpiProfileId(ctx context.Context, upiProfileId string) (*model.CustomerUpiDetails, error) {
	db := config.GetConnectionCtx(ctx)
	var model model.CustomerUpiDetails

	if err := db.Where("profile_id = ?", upiProfileId).First(&model).Error; err != nil {
		return nil, err
	}

	return &model, nil
}
