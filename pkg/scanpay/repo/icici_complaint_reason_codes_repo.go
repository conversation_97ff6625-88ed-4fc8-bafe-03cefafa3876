package repo

import (
	"context"

	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/pkg/scanpay/model"
)

type ICICIComplaintReasonCodesRepo interface {
	GetAllActive(context.Context) (*[]model.ICICIComplaintReasonCodesModel, error)
	GetAll(ctx context.Context) (*[]model.ICICIComplaintReasonCodesModel, error)
	SaveReasonCodes(context.Context, *[]model.ICICIComplaintReasonCodesModel) error
}

type ICICIComplaintReasonCodesDB struct {}

var iciciComplaintReasonCodesInstance ICICIComplaintReasonCodesRepo = &ICICIComplaintReasonCodesDB{}

func GetICICIComplaintReasonCodesRepo() ICICIComplaintReasonCodesRepo {
	return iciciComplaintReasonCodesInstance
}

func (*ICICIComplaintReasonCodesDB) GetAllActive(ctx context.Context) (*[]model.ICICIComplaintReasonCodesModel, error) {
	var models []model.ICICIComplaintReasonCodesModel
	db := config.GetConnectionCtx(ctx)

	err := db.Where("is_active = true").Find(&models).Error
	if err != nil {
		return nil, err
	}
	return &models, nil
}

func (*ICICIComplaintReasonCodesDB) GetAll(ctx context.Context) (*[]model.ICICIComplaintReasonCodesModel, error) {
	var models []model.ICICIComplaintReasonCodesModel
	db := config.GetConnectionCtx(ctx)

	err := db.Find(&models).Error
	if err != nil {
		return nil, err
	}

	return &models, nil
}

func (*ICICIComplaintReasonCodesDB) SaveReasonCodes(ctx context.Context, 
	models *[]model.ICICIComplaintReasonCodesModel) error {
	db := config.GetConnectionCtx(ctx)

	err := db.Save(&models).Error
	if err != nil {
		return err
	}

	return nil
}
