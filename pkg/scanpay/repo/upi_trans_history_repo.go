package repo

import (
	"context"
	"errors"
	"net/http"

	"gorm.io/gorm"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/scanpay/model"
)

type UpiTransHistoryRepo interface {
	Get(context.Context, int64, int, int) (*[]model.UpiTransHistory, bool, error)
	Save(context.Context, *model.UpiTransHistory) error
	GetByTransId(context.Context, *model.UpiTransHistory, string) error
	UpdateComplaintStatus(context.Context, string) error
	GetPendingTransactions(context.Context, int) (*[]model.UpiTransHistory, error)
	UpdateTransStatus(context.Context, string, string) error
	GetBySeqNo(context.Context, string) (*model.UpiTransHistory, error)
}

type UpiTransHistoryDB struct {
}

var upiTransHistoryRepoInstance UpiTransHistoryRepo = &UpiTransHistoryDB{}

func GetUpiTransHistoryRepo() UpiTransHistoryRepo {
	return upiTransHistoryRepoInstance
}

func (*UpiTransHistoryDB) GetBySeqNo(ctx context.Context, seqNo string) (*model.UpiTransHistory, error) {
	db := config.GetConnectionCtx(ctx)
	var model model.UpiTransHistory

	err := db.Where("sequence_number = ?", seqNo).First(&model).Error
	if err != nil {
		if (errors.Is(err, gorm.ErrRecordNotFound)) {
			return nil, helper.NewCustomError("Sequence number is not present", http.StatusBadRequest)
		} 
		return nil, err
	}

	return &model, nil
}

func (*UpiTransHistoryDB) Get(ctx context.Context, customerId int64, page int, limit int) (
	*[]model.UpiTransHistory, bool, error) {
	db := config.GetDBConnection()
	var upiTransHistories []model.UpiTransHistory
	offset := (page - 1) * limit

	err := db.Where("customer_id = ?", customerId).Order("updated_at DESC").Offset(
		offset).Limit(limit).Find(&upiTransHistories).Error
	if err != nil {
		return nil, false, err
	}

	has_next_page := len(upiTransHistories) == limit

	return &upiTransHistories, has_next_page, nil
}

func (*UpiTransHistoryDB) Save(ctx context.Context,
	upiTransHistory *model.UpiTransHistory) error {
	db := config.GetConnectionCtx(ctx)

	err := db.Save(upiTransHistory).Error
	if err != nil {
		return err
	}

	return nil
}

func (*UpiTransHistoryDB) GetByTransId(ctx context.Context,
	upiTransHistory *model.UpiTransHistory, transId string) error {
	db := config.GetConnectionCtx(ctx)

	err := db.Where("transaction_id = ?", transId).First(upiTransHistory).Error
	if err != nil {
		return err
	}
	return nil
}

func (*UpiTransHistoryDB) UpdateComplaintStatus(ctx context.Context, transSeqNo string) error {
	db := config.GetConnectionCtx(ctx)
	var upiTransHistory model.UpiTransHistory

	err := db.Where("sequence_number = ?", transSeqNo).First(&upiTransHistory).Error 
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return helper.NewCustomError("sequence number is not present", http.StatusBadRequest)
		}
		return err
	}

	upiTransHistory.IsComplaint = true

	if err := db.Save(&upiTransHistory).Error; err != nil {
		return err
	}

	return nil
}

func (*UpiTransHistoryDB) GetPendingTransactions(ctx context.Context, limit int) (*[]model.UpiTransHistory, error) {
	db := config.GetConnectionCtx(ctx)
	var models []model.UpiTransHistory

	if err := db.Where("status = ?", "pending").Order("completion_time ASC").Limit(limit).Find(&models).Error; err != nil {
		return nil, err
	}

	return &models, nil
}

func (*UpiTransHistoryDB) UpdateTransStatus(ctx context.Context, transSeqNo string, status string) error {
	db := config.GetConnectionCtx(ctx)
	var upitransHistory model.UpiTransHistory

	if err := db.Where("sequence_number = ?", transSeqNo).First(&upitransHistory).Error; err != nil {
		return err
	}

	upitransHistory.Status = status

	if err := db.Save(&upitransHistory).Error; err != nil {
		return err
	}

	return nil
}
