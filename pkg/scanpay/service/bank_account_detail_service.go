package service

import (
	"context"

	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/scanpay/model"
	"stashfin.com/stashfin/magneto/pkg/scanpay/repo"
)

type BankAccountDetailInterface struct {
}

func (*BankAccountDetailInterface) Save(ctx context.Context,
	accountPayload *dtype.Account, customerId int64) error {
	var bankAccountDetail model.BankAccountDetailsModel
	var accounts []model.BankAccountDetailsModel

	existingBankAccountDetails, err := repo.GetBankAccountDetailsRepo().Get(ctx, customerId)
	if err != nil {
		return err
	}

	if len(*existingBankAccountDetails) == 0 {
		accountPayload.AccPrimary = 1
	}

	err = repo.GetBankAccountDetailsRepo().GetByAccRefId(ctx, &bankAccountDetail,
		accountPayload.AccRef)
	if err != nil {
		return err
	}

	convertIntoBankAccountDetailsModel(accountPayload, &bankAccountDetail, customerId)

	if accountPayload.AccPrimary == 1 {
		err, primaryAccount := repo.GetBankAccountDetailsRepo().GetPrimaryAccount(ctx,
			customerId)
		if err != nil {
			return err
		} else if primaryAccount.Id != 0 {
			primaryAccount.IsPrimaryAccount = false
			accounts = append(accounts, *primaryAccount)
		}
	}

	accounts = append(accounts, bankAccountDetail)
	err = repo.GetBankAccountDetailsRepo().SaveInBatches(ctx, &accounts)
	if err != nil {
		return err
	}

	return nil
}

func (*BankAccountDetailInterface) Get(ctx context.Context,
	accounts *dtype.Accounts, customerId int64) error {
	bankAccountDetails, err := repo.GetBankAccountDetailsRepo().Get(ctx, customerId)
	if err != nil {
		return err
	}

	convertIntoAccountPayloads(bankAccountDetails, accounts)
	sortAccountList(accounts)
	return nil
}

func (*BankAccountDetailInterface) RemoveBankAccounts(ctx context.Context, accRefIds []string) error {
	err := repo.GetBankAccountDetailsRepo().DisableBankAccounts(ctx, accRefIds)
	if err != nil {
		return err
	}

	return nil
}

func (*BankAccountDetailInterface) SaveUpiStatus(ctx context.Context, customerId int64, req *dtype.UpiStatusDetail) error {
	err := repo.GetCustomerUpiDetailsRepo().Save(ctx, customerId, req)
	if err != nil {
		return err
	}

	return nil
}
