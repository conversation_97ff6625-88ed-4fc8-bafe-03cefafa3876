package service

import (
	"fmt"
	"regexp"
	"sort"
	"strconv"

	"github.com/google/uuid"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/pkg/scanpay/model"
	"stashfin.com/stashfin/magneto/util"
)

func convertIntoAccountPayloads(bankAccountDetails *[]model.BankAccountDetailsModel,
	accounts *dtype.Accounts) error {
	for _, account := range *bankAccountDetails {
		var AccountPayload dtype.Account

		AccountPayload.AccNo = account.Number
		AccountPayload.AccName = account.AccountHolderName
		AccountPayload.AccType = account.Type
		AccountPayload.AccStatus = account.Status

		AccountPayload.AccPrimary = 0
		if account.IsPrimaryAccount {
			AccountPayload.AccPrimary = 1
		}

		AccountPayload.AccRef = account.ReferenceNumber
		AccountPayload.AccBankName = account.Name
		AccountPayload.BankId = account.BankId
		AccountPayload.BankIFSC = account.IfscCode
		AccountPayload.BankMbeba = account.Mbeba
		AccountPayload.AllowedCredentials = account.AllowedCredentials
		AccountPayload.BankMMID = account.MMID

		accounts.AccountPayloads = append(accounts.AccountPayloads, AccountPayload)
	}

	return nil
}

func convertIntoBankAccountDetailsModel(accountPayload *dtype.Account,
	bankAccountDetail *model.BankAccountDetailsModel, customerId int64) error {

	bankAccountDetail.Number = accountPayload.AccNo
	bankAccountDetail.AccountHolderName = accountPayload.AccName
	bankAccountDetail.Type = accountPayload.AccType
	bankAccountDetail.Status = accountPayload.AccStatus

	bankAccountDetail.IsPrimaryAccount = false
	if accountPayload.AccPrimary == 1 {
		bankAccountDetail.IsPrimaryAccount = true
	}

	bankAccountDetail.ReferenceNumber = accountPayload.AccRef
	bankAccountDetail.Name = accountPayload.AccBankName
	bankAccountDetail.BankId = accountPayload.BankId
	bankAccountDetail.CustomerId = customerId
	bankAccountDetail.IfscCode = accountPayload.BankIFSC
	bankAccountDetail.Mbeba = accountPayload.BankMbeba

	bankAccountDetail.AllowedCredentials = accountPayload.AllowedCredentials
	bankAccountDetail.MMID = accountPayload.BankMMID
	bankAccountDetail.IsActve = true

	return nil
}

func sortAccountList(accounts *dtype.Accounts) {
	sort.Slice(accounts.AccountPayloads, func(i, j int) bool {
		return accounts.AccountPayloads[i].AccPrimary > accounts.AccountPayloads[j].AccPrimary
	})
}

func convertIntoTransactions(upiTransHistories *[]model.UpiTransHistory,
	transactions *dtype.Transactions, has_next_page bool) {
	for _, upiTransHistory := range *upiTransHistories {
		var transaction dtype.Transaction

		transaction.TransStatus = upiTransHistory.Status
		transaction.PayeeName = upiTransHistory.PayeeName
		transaction.TransAmount = upiTransHistory.Amount
		transaction.PaidFrom = upiTransHistory.PayerName
		transaction.TransTime = util.GetAppTime(upiTransHistory.CompletionTime)
		transaction.TransId = upiTransHistory.TransactionId
		transaction.TransAccount = upiTransHistory.AccountNumber
		transaction.TransSeqNo = upiTransHistory.SequenceNumber
		transaction.TransType = upiTransHistory.Type
		transaction.TransRemark = upiTransHistory.Remark
		transaction.TransBankMMID = upiTransHistory.BankMMID
		transaction.IsComplaint = upiTransHistory.IsComplaint

		transactions.TransactionHisotries = append(transactions.TransactionHisotries,
			transaction)
	}
	transactions.Has_next_page = has_next_page
}

func convertIntoUpiTransHistoryModel(transaction *dtype.Transaction,
	upiTransHistory *model.UpiTransHistory, customerId int64) error {

	upiTransHistory.CustomerId = customerId
	upiTransHistory.Status = transaction.TransStatus
	upiTransHistory.PayeeName = transaction.PayeeName
	upiTransHistory.Amount = transaction.TransAmount
	upiTransHistory.PayerName = transaction.PaidFrom

	err, transTime := util.StringToIST(transaction.TransTime)
	if err != nil {
		return fmt.Errorf("Error while converting timestring into time: %s", transaction.TransTime)
	}

	upiTransHistory.CompletionTime = transTime
	upiTransHistory.TransactionId = transaction.TransId
	upiTransHistory.AccountNumber = transaction.TransAccount
	upiTransHistory.SequenceNumber = transaction.TransSeqNo
	upiTransHistory.Type = transaction.TransType
	upiTransHistory.Remark = transaction.TransRemark
	upiTransHistory.BankMMID = transaction.TransBankMMID

	return nil
}

func upiTransHistoryFromICICICallback(model *model.UpiTransHistory, upiStatusDetails *model.CustomerUpiDetails,
	bankAccountDetails *model.BankAccountDetailsModel, request *dtype.ICICIPaymentCallbackRequest) error {

	model.CustomerId = upiStatusDetails.CustomerId
	model.Status = getPaymentStatus(request.PayeeData.RespCode)
	model.PayeeName = request.PayeeData.Name

	amount, err := strconv.ParseFloat(request.Amount, 32)
	if err != nil {
		return fmt.Errorf("Amount: %s cannot be parsed in float32", request.Amount)
	}

	model.Amount = float32(amount)
	model.PayerName = request.PayerData.Name

	transTime, err := util.GetStandardTime(request.TxnCompletionDate, "yyyymmddHHMMSS")
	if err != nil {
		return err
	}

	model.CompletionTime = transTime
	model.AccountNumber = request.PayerData.AccountNo
	model.Type = request.TxnType
	model.BankMMID = bankAccountDetails.MMID

	return nil
}

func createICICISeqNo() string {
	uuidObj := uuid.New()
	uuidString := uuidObj.String()

	re := regexp.MustCompile(`[\s\-()]`)
	uuidString = "ICI" + re.ReplaceAllString(uuidString, "")
	return uuidString
}

func getPaymentStatus(responseCode string) string {

	code, err := strconv.Atoi(responseCode)
	if err != nil {
		return "failed"
	}

	responseCodeMap := map[int]string{
		0:  "success",
		91: "pending",
	}

	value, exist := responseCodeMap[code]
	if exist {
		return value
	}

	return "failed"
}
