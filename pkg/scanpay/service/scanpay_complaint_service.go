package service

import (
	"context"
	"net/http"

	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/pkg/scanpay/model"
	"stashfin.com/stashfin/magneto/pkg/scanpay/repo"
	"stashfin.com/stashfin/magneto/util"
)

type ScanpayComplaintInterface struct {
}

func (*ScanpayComplaintInterface) SaveComplaintReasonCodes(ctx context.Context) error {
	response, err := client.GetICICIClient().GetComplaintReasonCode(ctx)
	if err != nil {
		return err
	}

	var newReasonCodes []string
	var models []model.ICICIComplaintReasonCodesModel
	if response.Response == ICICISuccessCode {
		for _, data := range response.MobileAppData {
			newReasonCodes = append(newReasonCodes, data.ReasonCode)
			models = append(models, model.ICICIComplaintReasonCodesModel{
				ReasonCode:        data.ReasonCode,
				ReasonDescription: data.Description,
				IsActive:          true})
		}
	}

	existingComplaintCodes, err := repo.GetICICIComplaintReasonCodesRepo().GetAllActive(ctx)
	if err != nil {
		return err
	}

	for _, complaint := range *existingComplaintCodes {
		if !util.CheckStringInList(newReasonCodes, complaint.ReasonCode) {
			complaint.IsActive = false
			models = append(models, complaint)
		}
	}

	err = repo.GetICICIComplaintReasonCodesRepo().SaveReasonCodes(ctx, &models)
	if err != nil {
		return err
	}

	return nil
}

func (*ScanpayComplaintInterface) GetComplaintReasonCodes(ctx context.Context) (*dtype.ComplaintReasonCodesResponse, error) {
	complaintReasonCodes, err := repo.GetICICIComplaintReasonCodesRepo().GetAllActive(ctx)
	if err != nil {
		return nil, err
	}

	var response dtype.ComplaintReasonCodesResponse
	var complaintReasonList []dtype.ComplaintReasonCodeData

	for _, data := range *complaintReasonCodes {
		var payload dtype.ComplaintReasonCodeData
		payload.Description = data.ReasonDescription
		payload.ReasonCode = data.ReasonCode
		complaintReasonList = append(complaintReasonList, payload)
	}

	response.ComplaintReasonCodes = complaintReasonList
	return &response, nil
}

func (*ScanpayComplaintInterface) RaiseTransactionComplaint(ctx context.Context, customerId int64,
	req *dtype.RaiseTransactionComplaint) (interface{}, error) {

	upiStatusDetail, err := repo.GetCustomerUpiDetailsRepo().GetByCustomerId(ctx, customerId)
	if err != nil {
		return nil, err
	}

	isFailedTxn, err := checkIfFailedTxn(ctx, req.OriSeqNo)
	if err != nil {
		return nil, err
	}

	raiseTransactionComplaintRequest := map[string]string{
		"seq-no":       createICICISeqNo(),
		"channel-code": CHANNEL_CODE,
		"profile-id":   upiStatusDetail.ProfileId,
		"ori-seq-no":   req.OriSeqNo,
	}

	if !isFailedTxn {
		raiseTransactionComplaintRequest["reason-code"] = req.ReasonCode
	}

	response, err := client.GetICICIClient().RaiseTransactionComplaint(ctx, raiseTransactionComplaintRequest)
	if err != nil {
		return nil, helper.NewCustomError("Raise transaction API failed", http.StatusInternalServerError)
	}

	if (response["response"] == ICICISuccessCode) &&
		response["message"] == "Your complaint is raise successfully and it is under Process" {
		err = repo.GetUpiTransHistoryRepo().UpdateComplaintStatus(ctx, req.OriSeqNo)
		if err != nil {
			return nil, err
		}
	}

	return response, nil
}

func checkIfFailedTxn(ctx context.Context, oriSeqNo string) (bool, error) {
	model, err := repo.GetUpiTransHistoryRepo().GetBySeqNo(ctx, oriSeqNo)
	if err != nil {
		return false, err
	}

	if model.Status == FAILED {
		return true, nil
	}
	return false, nil
}

func (*ScanpayComplaintInterface) CheckComplaintStatus(ctx context.Context, customerId int64,
	deviceId string, req *dtype.GetComplaintStatus) (interface{}, error) {

	upiStatusDetail, err := repo.GetCustomerUpiDetailsRepo().GetByCustomerId(ctx, customerId)
	if err != nil {
		return nil, err
	}

	err, phone := customer.GetCustomerService().GetCustomerPhone(ctx, customerId)
	if err != nil {
		return nil, err
	}

	checkTransactionStatusRequest := map[string]string{
		"device-id":    deviceId,
		"mobile":       phone,
		"seq-no":       createICICISeqNo(),
		"channel-code": CHANNEL_CODE,
		"ori-seq-no":   req.OriSeqNo,
		"profile-id":   upiStatusDetail.ProfileId,
		"category":     TRANSACTION_CATEGORY,
	}

	response, err := client.GetICICIClient().CheckTransactionDisputeStatus(ctx, checkTransactionStatusRequest)
	if err != nil {
		return nil, helper.NewCustomError("check complaint status API failed", http.StatusInternalServerError)
	}

	return response, nil
}
