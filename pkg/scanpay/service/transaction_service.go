package service

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"

	"gorm.io/gorm"
	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/helper"
	"stashfin.com/stashfin/magneto/pkg/customer"
	"stashfin.com/stashfin/magneto/pkg/scanpay/model"
	"stashfin.com/stashfin/magneto/pkg/scanpay/repo"
	"stashfin.com/stashfin/magneto/util"
)

type TransactionInterface struct {
}

func (*TransactionInterface) Get(ctx context.Context, customerId int64, page int, limit int,
	transactions *dtype.Transactions) error {
	upiTransHisotries, has_next_page, err := repo.GetUpiTransHistoryRepo().Get(ctx, customerId,
		page, limit)
	if err != nil {
		return err
	}

	convertIntoTransactions(upiTransHisotries, transactions, has_next_page)
	return nil
}

func (*TransactionInterface) Save(ctx context.Context, customerId int64,
	transaction *dtype.Transaction) error {
	var upiTransHistory model.UpiTransHistory
	err := repo.GetUpiTransHistoryRepo().GetByTransId(ctx, &upiTransHistory, transaction.TransId)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}

	err = convertIntoUpiTransHistoryModel(transaction, &upiTransHistory, customerId)
	if err != nil {
		return helper.NewCustomError(err.Error(), http.StatusInternalServerError)
	}

	err = repo.GetUpiTransHistoryRepo().Save(ctx, &upiTransHistory)
	if err != nil {
		return err
	}

	return nil
}

func (*TransactionInterface) SaveICICIPaymentCallback(ctx context.Context, request *dtype.ICICIPaymentCallbackRequest) error {
	upiDetail, err := repo.GetCustomerUpiDetailsRepo().GetByUpiProfileId(ctx, request.ProfileId)
	if err != nil {
		return fmt.Errorf("Error: %s, while getting upi details", err.Error())
	}

	request.PayerData.AccountNo = maskedBankAccount(request.PayerData.AccountNo)
	bankAccountDetail, err := repo.GetBankAccountDetailsRepo().GetBankDetails(ctx, upiDetail.CustomerId,
		request.PayerData.AccountNo, request.PayerData.Ifsc)
	if err != nil {
		return fmt.Errorf("Error: %s, while getting bank account details", err.Error())
	}

	var upiTransHistory model.UpiTransHistory
	if err := repo.GetUpiTransHistoryRepo().GetByTransId(ctx, &upiTransHistory, request.Rrn); err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("Error: %s, while getting transaction details", err.Error())
		}
		upiTransHistory.SequenceNumber = request.OriginalTxnId
		upiTransHistory.TransactionId = request.Rrn
	}

	upiTransHistoryFromICICICallback(&upiTransHistory, upiDetail, bankAccountDetail, request)
	if err = repo.GetUpiTransHistoryRepo().Save(ctx, &upiTransHistory); err != nil {
		return fmt.Errorf("Error: %s, while saving transaction details", err.Error())
	}

	return nil
}

func maskedBankAccount(accNo string) string {
	if !strings.Contains(accNo, "X") {
		len := len(accNo)
		lastTwoChar := accNo[len-2:]
		accNo = strings.Repeat("X", len-2) + lastTwoChar
	}

	return accNo
}

func (*TransactionInterface) CheckPendingTransactionStatus(ctx context.Context, customerId int64, transSeqNo string,
	deviceId string) (string, error) {
	err, phone := customer.GetCustomerService().GetCustomerPhone(ctx, customerId)
	if err != nil {
		return "", err
	}

	upiStatusDetail, err := repo.GetCustomerUpiDetailsRepo().GetByCustomerId(ctx, customerId)
	if err != nil {
		return "", err
	}

	transactionStatusRequest := map[string]string{
		"mobile":       phone,
		"device-id":    deviceId,
		"seq-no":       createICICISeqNo(),
		"channel-code": "STASH",
		"profile-id":   upiStatusDetail.ProfileId,
		"ori-seq-no":   transSeqNo,
	}

	response, err := client.GetICICIClient().TransactionStatus(ctx, transactionStatusRequest)
	if err != nil {
		return "", helper.NewCustomError("Check transaction status API failed", http.StatusInternalServerError)
	}

	if responseCode, ok := response["response"].(string); ok {
		status := getPaymentStatus(responseCode)
		err = repo.GetUpiTransHistoryRepo().UpdateTransStatus(ctx, transSeqNo, status)
		if err != nil {
			return "", err
		}
		return status, nil
	}

	return "", fmt.Errorf("Error: %s", response["message"])
}

func (t *TransactionInterface) UpdatePendingTransStatus(ctx context.Context) {
	logger := util.Log

	upiTransHistories, err := repo.GetUpiTransHistoryRepo().GetPendingTransactions(ctx,
		config.GetConfigValues().ScanpayPendingTransactionSyncSize)
	if err != nil {
		return
	}

	logger.Infof("%d pending transactions picked", len(*upiTransHistories))

	for _, transaction := range *upiTransHistories {
		deviceId, err := customer.GetCustomerService().GetLatestDeviceIdByTime(ctx, transaction.CustomerId,
			transaction.CompletionTime)
		if err != nil {
			logger.Errorf("Error while getting latest device id: %s", err.Error())
			continue
		}

		_, err = t.CheckPendingTransactionStatus(ctx, transaction.CustomerId,
			transaction.SequenceNumber, deviceId)
		if err != nil {
			logger.Errorf("Error while geting transaction status: %s", err.Error())
			continue
		}
	}
}
