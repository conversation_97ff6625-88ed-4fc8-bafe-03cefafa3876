package request

import (
	"context"
	"errors"

	"stashfin.com/stashfin/magneto/client"
	"stashfin.com/stashfin/magneto/config"
	"stashfin.com/stashfin/magneto/dtype"
	"stashfin.com/stashfin/magneto/util"
)

func GetBigcityUserAuthtoken(req *dtype.BigcityUserAuthRequest, ctx context.Context) (error, *dtype.BigcityUserAuthResponse) {
	err, response := client.PostJsonRequest(config.GetConfigValues().BigcityUserAuthUrl, nil, req, "bigcity_customer_auth", ctx)
	if err != nil {
		return errors.New("Exception while getting customer auth from bigcity: " + err.Error()), nil
	}

	if response["success"].(bool) {
		var authResponse dtype.BigcityUserAuthResponse
		err = util.ConvertToStruct(response["data"].(map[string]interface{}), &authResponse)
		if err != nil {
			return errors.New("Error while populating response of bigcity auth token: " + err.Error()), nil
		}
		return nil, &authResponse
	}
	var voucherErrorResponse dtype.VoucherErrorResponse
	err = util.ConvertToStruct(response["data"].(map[string]interface{}), &voucherErrorResponse)
	if err != nil {
		return errors.New("Error while populating error response from create voucher req: " + err.Error()), nil
	} else if voucherErrorResponse.Status == 400 {
		return errors.New("Authentication Error in bigcity customer auth API"), nil
	}

	return errors.New("Error in bigcity customer auth API: " + voucherErrorResponse.Message), nil
}

func BigcityCreateVoucher(req *dtype.BigcityVoucherCreateRequest, token string, ctx context.Context) (error, *dtype.BigcityVoucherCreateResponse) {
	headers := map[string]string{
		"Authorization": "Bearer " + token,
	}

	err, response := client.PostJsonRequest(config.GetConfigValues().BigcityOrderCreateUrl, headers, req, "bigcity_create_voucher", ctx)
	if err != nil {
		return errors.New("Exception while sending create voucher request to bigcity: " + err.Error()), nil
	}

	if response["success"].(bool) {
		var voucherResponse dtype.BigcityVoucherCreateResponse
		err = util.ConvertToStruct(response["data"].(map[string]interface{}), &voucherResponse)
		if err != nil {
			return errors.New("Error while populating success response from create voucher req: " + err.Error()), nil
		}
		return nil, &voucherResponse
	}
	var voucherErrorResponse dtype.VoucherErrorResponse
	err = util.ConvertToStruct(response["data"].(map[string]interface{}), &voucherErrorResponse)
	if err != nil {
		return errors.New("Error while populating error response from create voucher req: " + err.Error()), nil
	} else if voucherErrorResponse.Status == 400 {
		return errors.New("Token Expired"), nil
	}

	return errors.New("Error in bigcity create voucher API: " + voucherErrorResponse.Message), nil
}
