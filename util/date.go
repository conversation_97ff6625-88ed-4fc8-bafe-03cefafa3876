package util

import (
	"fmt"
	"strconv"
	"time"
)

const (
	InsuranceMinAge = 18
	InsuranceMaxAge = 60

	DateMonthYearLayout = "02 Jan 2006"
)

func StringToIST(timeString string) (error, time.Time) {
	istLocation, err := time.LoadLocation("Asia/Kolkata")
	if err != nil {
		return err, time.Time{}
	}

	parsedTime, err := time.ParseInLocation("2006-01-02 15:04:05", timeString, istLocation)
	if err != nil {
		return err, time.Time{}
	}

	return nil, parsedTime
}

func TimeToString(time time.Time) string {
	timeString := time.Format("2006-01-02 15:04:05")

	return timeString
}

func ConvertDateStringIntoDate(dateString string) (error, time.Time) {
	layout := "Jan 2,2006"

	date, err := time.Parse(layout, dateString)
	if err != nil {
		return err, time.Now()
	}

	return nil, date
}

func ConvertDateIntoDateString(date time.Time) string {
	layout := "Jan 2,2006"

	return date.Format(layout)
}

func ParseDateString(inputString, layout string) *time.Time {
	parsedTime, err := time.Parse(layout, inputString)
	if err != nil {
		return nil
	}
	return &parsedTime
}

func ParseEpochTime(epochString string) (string, error) {
	epochTime, err := strconv.ParseInt(epochString, 10, 64)
	if err != nil {
		return "", fmt.Errorf("unable to parse this epochString %s into timestamp", epochString)
	}

	timestamp := time.Unix(epochTime, 0)
	iso8601Format := "2006-01-02T15:04:05"
	iso8601Time := timestamp.Format(iso8601Format)
	return iso8601Time, nil
}

func IsValidAge(birthdate *time.Time, minAge, maxAge int) bool {
	currentDate := time.Now()
	age := currentDate.Year() - birthdate.Year()

	if currentDate.YearDay() < birthdate.YearDay() {
		age--
	}

	return age >= minAge && age <= maxAge
}

func GetStandardTime(timeString string, format string) (time.Time, error) {

	if format == "yyyymmddHHMMSS" {
		parsedLayout := "20060102150405"
		parsedTime, err := time.Parse(parsedLayout, timeString)
		if err != nil {
			return time.Time{}, err
		}
		return parsedTime, nil
	}

	return time.Time{}, fmt.Errorf("not supporting this time format: %s", format)
}

func GetAppTime(inputTime time.Time) string {
	layout := "02 Jan, 03:04 PM"

	formattedTime := inputTime.Format(layout)

	return formattedTime
}

func ParseDateStringIntoLayout(dateString, layout string) (*time.Time, error) {
	date, err := time.Parse(layout, dateString)
	if err != nil {
		return nil, err
	}

	return &date, nil
}

func ParseDateTimeIntoDateString(datetime time.Time) string {
	layout := "02-01-2006"
	return datetime.Format(layout)
}

func FormatDateWithPattern(date *time.Time, pattern string) string {
	if date == nil || date.IsZero() {
		return ""
	}
	return date.Format(pattern)
}
