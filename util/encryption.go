package util

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
)

type Encryptor struct {
	key []byte
}

var encryptorInstance *Encryptor

func InitializeEncryptor(key string) error {
	decodedKey, err := base64.StdEncoding.DecodeString(key)
	if err != nil {
		return err
	}

	encryptorInstance = &Encryptor{
		key: decodedKey,
	}
	return nil
}

func GetEncryptor() *Encryptor {
	return encryptorInstance
}

func (e *Encryptor) EncryptString(plainText string) (string, error) {
	plaintext := []byte(plainText)

	block, err := aes.NewCipher(e.key)
	if err != nil {
		return "", err
	}

	iv := make([]byte, aes.BlockSize)
	if _, err := rand.Read(iv); err != nil {
		return "", err
	}

	b := cipher.NewCBCEncrypter(block, iv)

	plaintext = PKCS7Padding(plaintext, aes.BlockSize)

	ciphertext := make([]byte, len(plaintext))
	b.CryptBlocks(ciphertext, plaintext)

	cipherTextWithIV := append(iv, ciphertext...)
	return base64.StdEncoding.EncodeToString(cipherTextWithIV), nil
}

func (e *Encryptor) DecryptString(encryptedText string) (string, error) {
	cipherTextWithIV, err := base64.StdEncoding.DecodeString(encryptedText)
	if err != nil {
		return "", err
	}

	if len(cipherTextWithIV) < aes.BlockSize {
		return "", errors.New("invalid cipher text length")
	}

	iv := cipherTextWithIV[:aes.BlockSize]
	ciphertext := cipherTextWithIV[aes.BlockSize:]

	block, err := aes.NewCipher(e.key)
	if err != nil {
		return "", err
	}

	b := cipher.NewCBCDecrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	b.CryptBlocks(plaintext, ciphertext)

	plaintext = PKCS7Trimming(plaintext)
	return string(plaintext), nil
}

// PKCS7Padding pads the input with PKCS#7 padding
func PKCS7Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

// PKCS7Trimming removes PKCS#7 padding
func PKCS7Trimming(plaintext []byte) []byte {
	padding := plaintext[len(plaintext)-1]
	return plaintext[:len(plaintext)-int(padding)]
}
