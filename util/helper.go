package util

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

func ValidateRequestBody(c *gin.Context, data interface{}) error {
	validationError := c.BindJSON(&data)
	if validationError != nil {
		ErrorResponse("Invalid Request Body", c, http.StatusBadRequest)
		return validationError
	}

	validate := validator.New()
	validationError = validate.Struct(data)
	if validationError != nil {
		ErrorResponse("Invalid Json", c, http.StatusBadRequest)
		return validationError
	}

	body, _ := ioutil.ReadAll(c.Request.Body)
	json.Unmarshal(body, &data)
	return nil
}

func GetIntParam(c *gin.Context, key string, defaultvalue int) (error, int) {
	_, hasParam := c.<PERSON><PERSON>y(key)

	if hasParam {
		valueInt, err := strconv.Atoi(c.Query(key))

		if err != nil {
			ErrorResponse("Invalid key", c, http.StatusBadRequest)
			return err, -1
		}

		return nil, valueInt
	}

	return nil, defaultvalue
}

func ConvertToStruct(data map[string]interface{}, response interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}

	err = json.Unmarshal(jsonData, response)
	if err != nil {
		return err
	}

	return nil
}

func FormatMonths(months int) string {
	if months == 1 {
		return "1 month"
	} else if months > 1 {
		return fmt.Sprintf("%d months", months)
	} else {
		return "0 months"
	}
}

func CheckStringInList(list []string, target string) bool {
	for _, s := range list {
		if s == target {
			return true
		}
	}

	return false
}
func ConvertBase64ToJson(base64Str string) (map[string]interface{}, error) {
	jsonBytes, err := base64.StdEncoding.DecodeString(base64Str)
	if err != nil {
		return nil, errors.New("base64 decode error")
	}
	var myDataMap map[string]interface{}
	err = json.Unmarshal(jsonBytes, &myDataMap)
	return myDataMap, nil
}
