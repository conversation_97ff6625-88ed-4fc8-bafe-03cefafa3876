package util

import (
	"math/rand"
	"time"

	gonanoid "github.com/matoous/go-nanoid"
)

const uidCharacters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"

func GenerateUID(size int) (string, error) {
	uid, err := gonanoid.Generate(uidCharacters, size)
	if err != nil {
		return "", err
	}

	return uid, nil
}

func RandomInt(min, max int) int {
	rand.Seed(time.Now().UnixNano())
	return min + rand.Intn(max-min+1)
}
