package util

import (
	"context"
	"math/rand"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"stashfin.com/stashfin/magneto/dtype"
)

func SuccessResponse(obj interface{}, c *gin.Context) {
	respObj := dtype.BaseResponse{
		Success:      true,
		Data:         obj,
		ErrorMessage: "",
	}

	c.<PERSON>(http.StatusOK, respObj)
}

func ErrorResponse(errorMessage string, c *gin.Context, errorCode int) {
	respObj := dtype.BaseResponse{
		Success:      false,
		Data:         nil,
		ErrorMessage: errorMessage,
	}

	c.<PERSON>(errorCode, respObj)
}

func ErrorResponseWithData(errorMessage string, c *gin.Context, errorCode int, obj interface{}) {
	respObj := dtype.BaseResponse{
		Success:      false,
		Data:         obj,
		ErrorMessage: errorMessage,
	}

	c.<PERSON>(errorCode, respObj)
}

func MarkRequestUnauthorize(c *gin.Context, message string) {
	respObj := dtype.BaseResponse{
		Success:      false,
		Data:         nil,
		ErrorMessage: message,
	}

	c.AbortWithStatusJSON(http.StatusUnauthorized, respObj)
}

func CLog(r *http.Request) *logrus.Entry {
	return r.Context().Value("clog").(*logrus.Entry)
}

func CLogCtx(ctx context.Context) *logrus.Entry {
	return ctx.Value("clog").(*logrus.Entry)
}

func GenerateRandomString(n int) string {
	const characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	b := make([]byte, n)
	for i := range b {
		b[i] = characters[rand.Intn(len(characters))]
	}
	return string(b)
}
